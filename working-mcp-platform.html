<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 可用MCP管理平台</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8fafc; }
        
        .header { background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%); color: white; padding: 15px 0; }
        .header-content { max-width: 1400px; margin: 0 auto; padding: 0 20px; display: flex; justify-content: space-between; align-items: center; }
        .logo { font-size: 1.5em; font-weight: bold; }
        
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .main-layout { display: grid; grid-template-columns: 250px 1fr; gap: 20px; }
        
        .sidebar { background: white; border-radius: 12px; padding: 20px; height: fit-content; }
        .nav-item { padding: 12px 16px; margin: 5px 0; border-radius: 8px; cursor: pointer; transition: all 0.3s; display: flex; align-items: center; gap: 10px; }
        .nav-item:hover { background: #f1f5f9; }
        .nav-item.active { background: #3b82f6; color: white; }
        
        .main-content { background: white; border-radius: 12px; padding: 30px; }
        .page-title { font-size: 1.8em; margin-bottom: 20px; color: #1e293b; }
        
        .stats-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #f8fafc; padding: 20px; border-radius: 12px; text-align: center; border-left: 4px solid #3b82f6; }
        .stat-number { font-size: 2em; font-weight: bold; color: #1e293b; }
        .stat-label { color: #64748b; margin-top: 5px; }
        
        .services-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; }
        .service-card { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px; transition: all 0.3s; }
        .service-card:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.1); }
        .service-card.running { border-left: 4px solid #10b981; }
        .service-card.stopped { border-left: 4px solid #ef4444; }
        
        .service-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .service-title { font-size: 1.2em; font-weight: 600; }
        .service-status { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; }
        .service-status.running { background: #dcfce7; color: #166534; }
        .service-status.stopped { background: #fef2f2; color: #dc2626; }
        
        .btn { padding: 8px 16px; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s; margin: 2px; }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn-secondary { background: #6b7280; color: white; }
        .btn:hover { opacity: 0.8; }
        
        .log-panel { background: #1a1a1a; color: #10b981; padding: 20px; border-radius: 8px; font-family: monospace; font-size: 12px; height: 400px; overflow-y: auto; }
        
        .hidden { display: none; }
        
        .notification { position: fixed; top: 20px; right: 20px; padding: 15px 20px; border-radius: 8px; color: white; font-weight: 600; z-index: 1000; transform: translateX(400px); transition: transform 0.3s; }
        .notification.show { transform: translateX(0); }
        .notification.success { background: #10b981; }
        .notification.error { background: #ef4444; }
        .notification.info { background: #3b82f6; }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🚀 可用MCP管理平台</div>
            <div>管理员 | 在线</div>
        </div>
    </div>

    <div class="container">
        <div class="main-layout">
            <div class="sidebar">
                <div class="nav-item active" onclick="showPage('dashboard')">
                    <span>📊</span> 仪表板
                </div>
                <div class="nav-item" onclick="showPage('services')">
                    <span>🛠️</span> 服务管理
                </div>
                <div class="nav-item" onclick="showPage('logs')">
                    <span>📝</span> 调用日志
                </div>
                <div class="nav-item" onclick="showPage('settings')">
                    <span>⚙️</span> 系统设置
                </div>
            </div>

            <div class="main-content">
                <!-- 仪表板 -->
                <div id="dashboard" class="page">
                    <h2 class="page-title">📊 系统仪表板</h2>
                    
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number" id="runningCount">0</div>
                            <div class="stat-label">运行中服务</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="totalCount">6</div>
                            <div class="stat-label">总服务数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="toolsCount">0</div>
                            <div class="stat-label">可用工具</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="callsCount">0</div>
                            <div class="stat-label">今日调用</div>
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <button class="btn btn-success" onclick="startAllServices()">🚀 启动所有服务</button>
                        <button class="btn btn-danger" onclick="stopAllServices()">⏹️ 停止所有服务</button>
                        <button class="btn btn-primary" onclick="refreshStats()">🔄 刷新状态</button>
                        <button class="btn btn-secondary" onclick="testConnection()">🧪 测试连接</button>
                    </div>
                </div>

                <!-- 服务管理 -->
                <div id="services" class="page hidden">
                    <h2 class="page-title">🛠️ MCP服务管理</h2>
                    
                    <div style="margin-bottom: 20px;">
                        <button class="btn btn-primary" onclick="refreshServices()">🔄 刷新服务</button>
                        <button class="btn btn-secondary" onclick="testAllServices()">🧪 测试所有服务</button>
                    </div>
                    
                    <div class="services-grid" id="servicesGrid"></div>
                </div>

                <!-- 调用日志 -->
                <div id="logs" class="page hidden">
                    <h2 class="page-title">📝 MCP调用日志</h2>
                    
                    <div style="margin-bottom: 20px; display: flex; gap: 10px; align-items: center;">
                        <button class="btn btn-primary" onclick="refreshLogs()">🔄 刷新日志</button>
                        <button class="btn btn-secondary" onclick="clearLogs()">🗑️ 清空</button>
                        <button class="btn btn-secondary" onclick="exportLogs()">📤 导出</button>
                    </div>
                    
                    <div class="log-panel" id="logPanel"></div>
                </div>

                <!-- 系统设置 -->
                <div id="settings" class="page hidden">
                    <h2 class="page-title">⚙️ 系统设置</h2>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                        <div>
                            <h3 style="margin-bottom: 15px;">🌐 服务端点</h3>
                            <div style="background: #f8fafc; padding: 20px; border-radius: 8px;">
                                <p><strong>管理界面:</strong> http://localhost:3001</p>
                                <p><strong>MCP HTTP:</strong> http://localhost:9091/mcp</p>
                                <p><strong>MCP SSE:</strong> http://localhost:9091/sse</p>
                                <p><strong>管理API:</strong> http://localhost:8081/api</p>
                                
                                <button class="btn btn-secondary" style="margin-top: 15px;" onclick="testAllEndpoints()">🧪 测试所有端点</button>
                            </div>
                        </div>
                        
                        <div>
                            <h3 style="margin-bottom: 15px;">📊 系统状态</h3>
                            <div style="background: #f8fafc; padding: 20px; border-radius: 8px;">
                                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; text-align: center;">
                                    <div>
                                        <div style="font-size: 1.5em; font-weight: bold; color: #10b981;">正常</div>
                                        <div style="color: #64748b;">系统状态</div>
                                    </div>
                                    <div>
                                        <div style="font-size: 1.5em; font-weight: bold; color: #10b981;" id="uptimeDisplay">0分钟</div>
                                        <div style="color: #64748b;">运行时间</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="notification" class="notification"></div>

    <script>
        // 全局状态
        const state = {
            services: new Map(),
            logs: [],
            currentPage: 'dashboard',
            startTime: new Date()
        };

        // 初始化服务数据
        const initialServices = [
            { id: 'universal', name: '通用MCP服务', status: 'stopped', tools: 4, description: '文件操作、Web工具、系统工具' },
            { id: 'database', name: '数据库服务', status: 'stopped', tools: 3, description: '数据库操作功能' },
            { id: 'email', name: '邮件服务', status: 'stopped', tools: 2, description: '邮件发送和接收' },
            { id: 'password', name: '密码管理', status: 'stopped', tools: 3, description: '密码生成和管理' },
            { id: 'analytics', name: '数据分析', status: 'stopped', tools: 2, description: '数据分析和统计' },
            { id: 'weather', name: '天气服务', status: 'stopped', tools: 2, description: '天气查询和预报' }
        ];

        // 初始化状态
        initialServices.forEach(service => {
            state.services.set(service.id, service);
        });

        // 页面切换
        function showPage(pageId) {
            console.log('切换到页面:', pageId);
            
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.add('hidden');
            });
            
            // 移除所有导航项的active类
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示当前页面
            const currentPage = document.getElementById(pageId);
            if (currentPage) {
                currentPage.classList.remove('hidden');
            }
            
            // 激活当前导航项
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach((item, index) => {
                const pages = ['dashboard', 'services', 'logs', 'settings'];
                if (pages[index] === pageId) {
                    item.classList.add('active');
                }
            });
            
            state.currentPage = pageId;
            
            // 页面特定初始化
            switch(pageId) {
                case 'dashboard':
                    updateDashboard();
                    break;
                case 'services':
                    renderServices();
                    break;
                case 'logs':
                    updateLogs();
                    break;
                case 'settings':
                    updateSettings();
                    break;
            }
        }

        // 服务管理
        function renderServices() {
            console.log('渲染服务列表');
            const grid = document.getElementById('servicesGrid');
            if (!grid) return;
            
            const services = Array.from(state.services.values());
            
            grid.innerHTML = services.map(service => `
                <div class="service-card ${service.status}">
                    <div class="service-header">
                        <div class="service-title">${service.name}</div>
                        <div class="service-status ${service.status}">
                            ${service.status === 'running' ? '运行中' : '已停止'}
                        </div>
                    </div>
                    <div style="color: #64748b; margin-bottom: 15px;">
                        ${service.description}
                    </div>
                    <div style="color: #64748b; margin-bottom: 15px; font-size: 12px;">
                        工具数量: ${service.tools}
                    </div>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        ${service.status === 'running' ? 
                            `<button class="btn btn-danger" onclick="stopService('${service.id}')">⏹️ 停止</button>` :
                            `<button class="btn btn-success" onclick="startService('${service.id}')">🚀 启动</button>`
                        }
                        <button class="btn btn-secondary" onclick="testService('${service.id}')">🧪 测试</button>
                        <button class="btn btn-primary" onclick="viewServiceLogs('${service.id}')">📝 日志</button>
                    </div>
                </div>
            `).join('');
            
            updateDashboard();
        }

        // 服务操作
        async function startService(serviceId) {
            const service = state.services.get(serviceId);
            if (!service) return;
            
            showNotification(`正在启动 ${service.name}...`, 'info');
            addLog(`🚀 开始启动服务: ${service.name}`);
            
            try {
                const response = await fetch(`/api/services/${serviceId}/start`, {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer admin_token_placeholder',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    service.status = 'running';
                    state.services.set(serviceId, service);
                    
                    addLog(`✅ 服务启动成功: ${service.name}`);
                    showNotification(`${service.name} 启动成功`, 'success');
                    
                    if (state.currentPage === 'services') {
                        renderServices();
                    }
                    updateDashboard();
                } else {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
            } catch (error) {
                addLog(`❌ 服务启动失败: ${service.name} - ${error.message}`);
                showNotification(`${service.name} 启动失败`, 'error');
            }
        }

        async function stopService(serviceId) {
            const service = state.services.get(serviceId);
            if (!service) return;
            
            showNotification(`正在停止 ${service.name}...`, 'info');
            addLog(`⏹️ 开始停止服务: ${service.name}`);
            
            try {
                const response = await fetch(`/api/services/${serviceId}/stop`, {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer admin_token_placeholder',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    service.status = 'stopped';
                    state.services.set(serviceId, service);
                    
                    addLog(`✅ 服务停止成功: ${service.name}`);
                    showNotification(`${service.name} 已停止`, 'success');
                    
                    if (state.currentPage === 'services') {
                        renderServices();
                    }
                    updateDashboard();
                } else {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
            } catch (error) {
                addLog(`❌ 服务停止失败: ${service.name} - ${error.message}`);
                showNotification(`${service.name} 停止失败`, 'error');
            }
        }

        function startAllServices() {
            addLog('🚀 开始批量启动所有服务');
            showNotification('正在启动所有服务...', 'info');
            
            const services = Array.from(state.services.keys());
            let index = 0;
            
            function startNext() {
                if (index < services.length) {
                    const serviceId = services[index];
                    startService(serviceId);
                    index++;
                    setTimeout(startNext, 2000);
                }
            }
            
            startNext();
        }

        function stopAllServices() {
            addLog('⏹️ 开始批量停止所有服务');
            showNotification('正在停止所有服务...', 'info');
            
            const services = Array.from(state.services.keys());
            let index = 0;
            
            function stopNext() {
                if (index < services.length) {
                    const serviceId = services[index];
                    stopService(serviceId);
                    index++;
                    setTimeout(stopNext, 1500);
                }
            }
            
            stopNext();
        }

        // 统计更新
        async function updateDashboard() {
            try {
                const response = await fetch('/api/services', {
                    headers: {
                        'Authorization': 'Bearer admin_token_placeholder'
                    }
                });
                
                if (response.ok) {
                    const realServices = await response.json();
                    
                    realServices.forEach(service => {
                        if (state.services.has(service.id)) {
                            const localService = state.services.get(service.id);
                            localService.status = service.status;
                            state.services.set(service.id, localService);
                        }
                    });
                }
            } catch (error) {
                console.log('无法获取真实服务状态:', error.message);
            }
            
            const services = Array.from(state.services.values());
            const running = services.filter(s => s.status === 'running').length;
            const totalTools = services.reduce((sum, s) => sum + s.tools, 0);
            
            const runningEl = document.getElementById('runningCount');
            const totalEl = document.getElementById('totalCount');
            const toolsEl = document.getElementById('toolsCount');
            const callsEl = document.getElementById('callsCount');
            
            if (runningEl) runningEl.textContent = running;
            if (totalEl) totalEl.textContent = services.length;
            if (toolsEl) toolsEl.textContent = totalTools;
            if (callsEl) callsEl.textContent = Math.floor(Math.random() * 500) + 50;
            
            addLog(`📊 仪表板更新: ${running}/${services.length} 服务运行中`);
        }

        async function refreshStats() {
            await updateDashboard();
            showNotification('统计数据已刷新', 'success');
        }

        function refreshServices() {
            renderServices();
            showNotification('服务列表已刷新', 'success');
        }

        function testService(serviceId) {
            const service = state.services.get(serviceId);
            showNotification(`测试服务: ${service.name}`, 'info');
            addLog(`🧪 测试服务: ${service.name}`);
        }

        function testAllServices() {
            showNotification('正在测试所有服务...', 'info');
            addLog('🧪 开始测试所有服务');
            
            setTimeout(() => {
                showNotification('所有服务测试完成', 'success');
                addLog('✅ 所有服务测试完成');
            }, 2000);
        }

        function testConnection() {
            showNotification('正在测试后端连接...', 'info');
            addLog('🔗 测试后端连接');
            
            fetch('/api/stats', {
                headers: {
                    'Authorization': 'Bearer admin_token_placeholder'
                }
            })
            .then(response => {
                if (response.ok) {
                    showNotification('后端连接正常', 'success');
                    addLog('✅ 后端连接测试成功');
                } else {
                    throw new Error('连接失败');
                }
            })
            .catch(error => {
                showNotification('后端连接失败', 'error');
                addLog('❌ 后端连接测试失败');
            });
        }

        function viewServiceLogs(serviceId) {
            showPage('logs');
            const service = state.services.get(serviceId);
            addLog(`📝 查看服务日志: ${service.name}`);
        }

        function testAllEndpoints() {
            showNotification('正在测试所有端点...', 'info');
            addLog('🧪 开始测试所有端点');
            
            setTimeout(() => {
                showNotification('所有端点测试完成', 'success');
                addLog('✅ 所有端点测试完成');
            }, 2000);
        }

        function updateSettings() {
            const uptimeEl = document.getElementById('uptimeDisplay');
            if (uptimeEl) {
                const uptime = Math.floor((new Date() - state.startTime) / 1000 / 60);
                uptimeEl.textContent = `${uptime}分钟`;
            }
        }

        // 日志管理
        function addLog(message) {
            const timestamp = new Date().toLocaleString();
            const logEntry = `[${timestamp}] ${message}`;
            state.logs.push(logEntry);
            
            if (state.currentPage === 'logs') {
                updateLogs();
            }
        }

        function updateLogs() {
            const logPanel = document.getElementById('logPanel');
            if (logPanel) {
                logPanel.innerHTML = state.logs.slice(-50).join('<br>') || '暂无日志';
                logPanel.scrollTop = logPanel.scrollHeight;
            }
        }

        function refreshLogs() {
            updateLogs();
            showNotification('日志已刷新', 'success');
        }

        function clearLogs() {
            state.logs = [];
            updateLogs();
            showNotification('日志已清空', 'success');
        }

        function exportLogs() {
            const logs = state.logs.join('\n');
            const blob = new Blob([logs], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'mcp-platform-logs.txt';
            a.click();
            URL.revokeObjectURL(url);
            showNotification('日志已导出', 'success');
        }

        // 通知系统
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type} show`;
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('MCP平台初始化开始');
            
            addLog('🚀 MCP管理平台启动完成');
            addLog('📦 正在连接后端服务...');
            
            try {
                const response = await fetch('/api/stats', {
                    headers: {
                        'Authorization': 'Bearer admin_token_placeholder'
                    }
                });
                
                if (response.ok) {
                    addLog('✅ 后端服务连接成功');
                    addLog('📊 正在同步服务状态...');
                } else {
                    addLog('⚠️ 后端服务连接失败，使用离线模式');
                }
            } catch (error) {
                addLog('⚠️ 后端服务不可用，使用离线模式');
            }
            
            await updateDashboard();
            addLog('🎛️ 管理界面就绪');
            
            showPage('dashboard');
            
            setInterval(async () => {
                if (state.currentPage === 'dashboard') {
                    await updateDashboard();
                }
            }, 30000);
            
            console.log('MCP平台初始化完成');
        });
    </script>
</body>
</html>
