<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏢 简化MCP管理平台</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8fafc; }
        
        .header { background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%); color: white; padding: 15px 0; }
        .header-content { max-width: 1200px; margin: 0 auto; padding: 0 20px; display: flex; justify-content: space-between; align-items: center; }
        .logo { font-size: 1.5em; font-weight: bold; }
        
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .main-layout { display: grid; grid-template-columns: 200px 1fr; gap: 20px; }
        
        .sidebar { background: white; border-radius: 12px; padding: 20px; height: fit-content; }
        .nav-item { padding: 12px 16px; margin: 5px 0; border-radius: 8px; cursor: pointer; transition: all 0.3s; }
        .nav-item:hover { background: #f1f5f9; }
        .nav-item.active { background: #3b82f6; color: white; }
        
        .main-content { background: white; border-radius: 12px; padding: 30px; }
        .page-title { font-size: 1.8em; margin-bottom: 20px; color: #1e293b; }
        
        .stats-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #f8fafc; padding: 20px; border-radius: 12px; text-align: center; border-left: 4px solid #3b82f6; }
        .stat-number { font-size: 2em; font-weight: bold; color: #1e293b; }
        .stat-label { color: #64748b; margin-top: 5px; }
        
        .services-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .service-card { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px; }
        .service-card.running { border-left: 4px solid #10b981; }
        .service-card.stopped { border-left: 4px solid #ef4444; }
        
        .service-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .service-title { font-size: 1.2em; font-weight: 600; }
        .service-status { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; }
        .service-status.running { background: #dcfce7; color: #166534; }
        .service-status.stopped { background: #fef2f2; color: #dc2626; }
        
        .btn { padding: 8px 16px; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s; margin: 2px; }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn:hover { opacity: 0.8; }
        
        .log-panel { background: #1a1a1a; color: #10b981; padding: 20px; border-radius: 8px; font-family: monospace; font-size: 12px; height: 300px; overflow-y: auto; }
        
        .hidden { display: none; }
        
        .notification { position: fixed; top: 20px; right: 20px; padding: 15px 20px; border-radius: 8px; color: white; font-weight: 600; z-index: 1000; transform: translateX(400px); transition: transform 0.3s; }
        .notification.show { transform: translateX(0); }
        .notification.success { background: #10b981; }
        .notification.error { background: #ef4444; }
        .notification.info { background: #3b82f6; }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏢 简化MCP平台</div>
            <div>管理员</div>
        </div>
    </div>

    <div class="container">
        <div class="main-layout">
            <div class="sidebar">
                <div class="nav-item active" data-page="dashboard">📊 仪表板</div>
                <div class="nav-item" data-page="services">🛠️ 服务管理</div>
                <div class="nav-item" data-page="logs">📝 日志监控</div>
                <div class="nav-item" data-page="settings">⚙️ 系统设置</div>
            </div>

            <div class="main-content">
                <!-- 仪表板 -->
                <div id="dashboard" class="page">
                    <h2 class="page-title">📊 系统仪表板</h2>
                    
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number" id="runningCount">0</div>
                            <div class="stat-label">运行中服务</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="totalCount">6</div>
                            <div class="stat-label">总服务数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="toolsCount">0</div>
                            <div class="stat-label">可用工具</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="callsCount">0</div>
                            <div class="stat-label">今日调用</div>
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <button class="btn btn-success" onclick="startAllServices()">🚀 启动所有服务</button>
                        <button class="btn btn-danger" onclick="stopAllServices()">⏹️ 停止所有服务</button>
                        <button class="btn btn-primary" onclick="refreshStats()">🔄 刷新状态</button>
                    </div>
                </div>

                <!-- 服务管理 -->
                <div id="services" class="page hidden">
                    <h2 class="page-title">🛠️ MCP服务管理</h2>
                    <div class="services-grid" id="servicesGrid"></div>
                </div>

                <!-- 日志监控 -->
                <div id="logs" class="page hidden">
                    <h2 class="page-title">📝 系统日志</h2>
                    <div style="margin-bottom: 20px;">
                        <button class="btn btn-primary" onclick="refreshLogs()">🔄 刷新日志</button>
                        <button class="btn btn-danger" onclick="clearLogs()">🗑️ 清空日志</button>
                    </div>
                    <div class="log-panel" id="logPanel"></div>
                </div>

                <!-- 系统设置 -->
                <div id="settings" class="page hidden">
                    <h2 class="page-title">⚙️ 系统设置</h2>
                    <div style="background: #f8fafc; padding: 20px; border-radius: 8px;">
                        <h3>🔐 认证信息</h3>
                        <p><strong>管理员令牌:</strong> mcp_admin_0205a6d19866acf1c5afd6fc464c8d50</p>
                        <p><strong>用户令牌:</strong> mcp_user_b5297672c1a1dd6858bd3115cab4af45</p>
                        
                        <h3 style="margin-top: 20px;">🌐 服务端点</h3>
                        <p><strong>管理界面:</strong> http://localhost:3000</p>
                        <p><strong>MCP HTTP:</strong> http://localhost:9090/mcp</p>
                        <p><strong>MCP SSE:</strong> http://localhost:9090/sse</p>
                        <p><strong>管理API:</strong> http://localhost:8080/api</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="notification" class="notification"></div>

    <script>
        // 全局状态
        const state = {
            services: new Map([
                ['universal', { id: 'universal', name: '通用MCP服务', status: 'stopped', tools: 4, description: '文件操作、Web工具、系统工具' }],
                ['database', { id: 'database', name: '数据库服务', status: 'stopped', tools: 3, description: '数据库操作功能' }],
                ['email', { id: 'email', name: '邮件服务', status: 'stopped', tools: 2, description: '邮件发送接收功能' }],
                ['password', { id: 'password', name: '密码管理', status: 'stopped', tools: 3, description: '密码生成管理功能' }],
                ['analytics', { id: 'analytics', name: '数据分析', status: 'stopped', tools: 2, description: '数据分析统计功能' }],
                ['weather', { id: 'weather', name: '天气服务', status: 'stopped', tools: 2, description: '天气查询预报功能' }]
            ]),
            logs: [],
            currentPage: 'dashboard'
        };

        // 页面切换
        function showPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.add('hidden');
            });
            
            // 移除所有导航项的active类
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示当前页面
            const currentPage = document.getElementById(pageId);
            if (currentPage) {
                currentPage.classList.remove('hidden');
            }
            
            // 激活当前导航项
            const currentNav = document.querySelector(`[data-page="${pageId}"]`);
            if (currentNav) {
                currentNav.classList.add('active');
            }
            
            state.currentPage = pageId;
            
            // 页面特定初始化
            if (pageId === 'services') {
                renderServices();
            } else if (pageId === 'logs') {
                updateLogs();
            }
        }

        // 服务管理
        function renderServices() {
            const grid = document.getElementById('servicesGrid');
            const services = Array.from(state.services.values());
            
            grid.innerHTML = services.map(service => `
                <div class="service-card ${service.status}">
                    <div class="service-header">
                        <div class="service-title">${service.name}</div>
                        <div class="service-status ${service.status}">
                            ${service.status === 'running' ? '运行中' : '已停止'}
                        </div>
                    </div>
                    <div style="color: #64748b; margin-bottom: 15px;">
                        ${service.description}
                    </div>
                    <div style="color: #64748b; margin-bottom: 15px; font-size: 12px;">
                        提供 ${service.tools} 个工具
                    </div>
                    <div>
                        ${service.status === 'running' ? 
                            `<button class="btn btn-danger" onclick="stopService('${service.id}')">⏹️ 停止</button>` :
                            `<button class="btn btn-success" onclick="startService('${service.id}')">🚀 启动</button>`
                        }
                        <button class="btn btn-primary" onclick="testService('${service.id}')">🧪 测试</button>
                    </div>
                </div>
            `).join('');
        }

        // 服务操作
        function startService(serviceId) {
            const service = state.services.get(serviceId);
            if (!service) return;
            
            showNotification(`正在启动 ${service.name}...`, 'info');
            
            setTimeout(() => {
                service.status = 'running';
                state.services.set(serviceId, service);
                
                addLog(`✅ 服务启动成功: ${service.name}`);
                showNotification(`${service.name} 启动成功`, 'success');
                
                if (state.currentPage === 'services') {
                    renderServices();
                }
                updateStats();
            }, 1000);
        }

        function stopService(serviceId) {
            const service = state.services.get(serviceId);
            if (!service) return;
            
            showNotification(`正在停止 ${service.name}...`, 'info');
            
            setTimeout(() => {
                service.status = 'stopped';
                state.services.set(serviceId, service);
                
                addLog(`⏹️ 服务停止: ${service.name}`);
                showNotification(`${service.name} 已停止`, 'success');
                
                if (state.currentPage === 'services') {
                    renderServices();
                }
                updateStats();
            }, 1000);
        }

        function testService(serviceId) {
            const service = state.services.get(serviceId);
            if (!service) return;
            
            addLog(`🧪 测试服务: ${service.name}`);
            showNotification(`测试 ${service.name}`, 'info');
        }

        function startAllServices() {
            const services = Array.from(state.services.keys());
            let index = 0;
            
            function startNext() {
                if (index < services.length) {
                    startService(services[index]);
                    index++;
                    setTimeout(startNext, 1500);
                }
            }
            
            startNext();
        }

        function stopAllServices() {
            const services = Array.from(state.services.keys());
            let index = 0;
            
            function stopNext() {
                if (index < services.length) {
                    stopService(services[index]);
                    index++;
                    setTimeout(stopNext, 1500);
                }
            }
            
            stopNext();
        }

        // 统计更新
        function updateStats() {
            const services = Array.from(state.services.values());
            const running = services.filter(s => s.status === 'running').length;
            const totalTools = services.reduce((sum, s) => sum + s.tools, 0);
            
            document.getElementById('runningCount').textContent = running;
            document.getElementById('toolsCount').textContent = totalTools;
            document.getElementById('callsCount').textContent = Math.floor(Math.random() * 100);
        }

        function refreshStats() {
            updateStats();
            showNotification('统计数据已刷新', 'success');
        }

        // 日志管理
        function addLog(message) {
            const timestamp = new Date().toLocaleString();
            const logEntry = `[${timestamp}] ${message}`;
            state.logs.push(logEntry);
            
            if (state.currentPage === 'logs') {
                updateLogs();
            }
        }

        function updateLogs() {
            const logPanel = document.getElementById('logPanel');
            if (logPanel) {
                logPanel.innerHTML = state.logs.slice(-20).join('<br>') || '暂无日志';
                logPanel.scrollTop = logPanel.scrollHeight;
            }
        }

        function refreshLogs() {
            updateLogs();
            showNotification('日志已刷新', 'success');
        }

        function clearLogs() {
            state.logs = [];
            updateLogs();
            showNotification('日志已清空', 'success');
        }

        // 通知系统
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type} show`;
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 添加导航点击事件
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', () => {
                    const pageId = item.getAttribute('data-page');
                    showPage(pageId);
                });
            });
            
            // 初始化数据
            updateStats();
            addLog('🚀 简化MCP平台启动完成');
            addLog('📦 服务配置加载完成');
            addLog('🎛️ 管理界面就绪');
            
            // 显示默认页面
            showPage('dashboard');
        });
    </script>
</body>
</html>
