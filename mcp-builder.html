<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 MCP服务构建器 - 点击生成MCP服务</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f7; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 40px; }
        .header h1 { color: #1d1d1f; font-size: 2.5em; margin-bottom: 10px; }
        .header p { color: #86868b; font-size: 1.2em; }
        
        .builder { background: white; border-radius: 12px; padding: 30px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .section { margin-bottom: 30px; }
        .section h3 { color: #1d1d1f; margin-bottom: 15px; font-size: 1.3em; }
        
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; color: #1d1d1f; }
        .form-group input, .form-group textarea, .form-group select { 
            width: 100%; padding: 12px; border: 2px solid #d2d2d7; border-radius: 8px; 
            font-size: 16px; transition: border-color 0.3s;
        }
        .form-group input:focus, .form-group textarea:focus, .form-group select:focus { 
            outline: none; border-color: #007aff; 
        }
        
        .tools-section { background: #f6f6f6; padding: 20px; border-radius: 8px; }
        .tool-item { background: white; padding: 15px; margin-bottom: 15px; border-radius: 8px; border: 1px solid #d2d2d7; }
        .tool-header { display: flex; justify-content: between; align-items: center; margin-bottom: 10px; }
        .tool-header input { flex: 1; margin-right: 10px; }
        .remove-tool { background: #ff3b30; color: white; border: none; padding: 8px 12px; border-radius: 6px; cursor: pointer; }
        
        .btn { padding: 12px 24px; border: none; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.3s; }
        .btn-primary { background: #007aff; color: white; }
        .btn-primary:hover { background: #0056cc; }
        .btn-secondary { background: #34c759; color: white; margin-left: 10px; }
        .btn-secondary:hover { background: #28a745; }
        
        .output { background: #1d1d1f; color: #f5f5f7; padding: 20px; border-radius: 8px; margin-top: 20px; }
        .output pre { white-space: pre-wrap; font-family: 'SF Mono', Monaco, monospace; font-size: 14px; }
        
        .templates { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .template-card { background: white; padding: 20px; border-radius: 8px; border: 2px solid #d2d2d7; cursor: pointer; transition: all 0.3s; }
        .template-card:hover { border-color: #007aff; transform: translateY(-2px); }
        .template-card.selected { border-color: #007aff; background: #f0f8ff; }
        .template-card h4 { color: #1d1d1f; margin-bottom: 10px; }
        .template-card p { color: #86868b; font-size: 14px; }
        
        .hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 MCP服务构建器</h1>
            <p>点击鼠标，3分钟生成完整的MCP服务代码</p>
        </div>

        <div class="builder">
            <!-- 模板选择 -->
            <div class="section">
                <h3>📦 选择模板</h3>
                <div class="templates">
                    <div class="template-card" data-template="file">
                        <h4>🗂️ 文件操作服务</h4>
                        <p>读写文件、列出目录、文件搜索</p>
                    </div>
                    <div class="template-card" data-template="web">
                        <h4>🌐 Web工具服务</h4>
                        <p>HTTP请求、网页抓取、URL分析</p>
                    </div>
                    <div class="template-card" data-template="database">
                        <h4>🗄️ 数据库服务</h4>
                        <p>SQL查询、数据操作、表管理</p>
                    </div>
                    <div class="template-card" data-template="custom">
                        <h4>🛠️ 自定义服务</h4>
                        <p>从零开始创建自己的工具</p>
                    </div>
                </div>
            </div>

            <!-- 基本信息 -->
            <div class="section">
                <h3>ℹ️ 基本信息</h3>
                <div class="form-group">
                    <label>服务名称</label>
                    <input type="text" id="serviceName" placeholder="my-mcp-service" value="my-file-service">
                </div>
                <div class="form-group">
                    <label>服务描述</label>
                    <input type="text" id="serviceDescription" placeholder="我的MCP服务" value="文件操作MCP服务">
                </div>
                <div class="form-group">
                    <label>版本</label>
                    <input type="text" id="serviceVersion" placeholder="1.0.0" value="1.0.0">
                </div>
            </div>

            <!-- 工具配置 -->
            <div class="section">
                <h3>🔧 工具配置</h3>
                <div class="tools-section">
                    <div id="toolsList"></div>
                    <button type="button" class="btn btn-secondary" onclick="addTool()">+ 添加工具</button>
                </div>
            </div>

            <!-- 生成按钮 -->
            <div class="section">
                <button type="button" class="btn btn-primary" onclick="generateService()">🚀 生成MCP服务</button>
                <button type="button" class="btn btn-secondary" onclick="downloadService()">💾 下载代码</button>
                <button type="button" class="btn btn-secondary" onclick="copyClaudeConfig()">📋 复制Claude配置</button>
            </div>

            <!-- 输出区域 -->
            <div id="output" class="output hidden">
                <pre id="generatedCode"></pre>
            </div>
        </div>
    </div>

    <script>
        let selectedTemplate = 'file';
        let tools = [];

        // 模板数据
        const templates = {
            file: {
                name: 'file-service',
                description: '文件操作MCP服务',
                tools: [
                    { name: 'read_file', description: '读取文件内容', params: 'path' },
                    { name: 'write_file', description: '写入文件内容', params: 'path, content' },
                    { name: 'list_directory', description: '列出目录内容', params: 'path' }
                ]
            },
            web: {
                name: 'web-service',
                description: 'Web工具MCP服务',
                tools: [
                    { name: 'http_request', description: '发送HTTP请求', params: 'url, method' },
                    { name: 'scrape_page', description: '抓取网页内容', params: 'url' },
                    { name: 'analyze_url', description: '分析URL结构', params: 'url' }
                ]
            },
            database: {
                name: 'db-service',
                description: '数据库MCP服务',
                tools: [
                    { name: 'query_sql', description: '执行SQL查询', params: 'sql' },
                    { name: 'list_tables', description: '列出数据表', params: '' },
                    { name: 'describe_table', description: '描述表结构', params: 'table' }
                ]
            },
            custom: {
                name: 'custom-service',
                description: '自定义MCP服务',
                tools: []
            }
        };

        // 选择模板
        document.querySelectorAll('.template-card').forEach(card => {
            card.addEventListener('click', () => {
                document.querySelectorAll('.template-card').forEach(c => c.classList.remove('selected'));
                card.classList.add('selected');
                selectedTemplate = card.dataset.template;
                loadTemplate();
            });
        });

        // 加载模板
        function loadTemplate() {
            const template = templates[selectedTemplate];
            document.getElementById('serviceName').value = template.name;
            document.getElementById('serviceDescription').value = template.description;
            
            tools = [...template.tools];
            renderTools();
        }

        // 渲染工具列表
        function renderTools() {
            const toolsList = document.getElementById('toolsList');
            toolsList.innerHTML = tools.map((tool, index) => `
                <div class="tool-item">
                    <div class="tool-header">
                        <input type="text" placeholder="工具名称" value="${tool.name}" onchange="updateTool(${index}, 'name', this.value)">
                        <button class="remove-tool" onclick="removeTool(${index})">删除</button>
                    </div>
                    <input type="text" placeholder="工具描述" value="${tool.description}" onchange="updateTool(${index}, 'description', this.value)">
                    <input type="text" placeholder="参数 (用逗号分隔)" value="${tool.params}" onchange="updateTool(${index}, 'params', this.value)">
                </div>
            `).join('');
        }

        // 添加工具
        function addTool() {
            tools.push({ name: '', description: '', params: '' });
            renderTools();
        }

        // 删除工具
        function removeTool(index) {
            tools.splice(index, 1);
            renderTools();
        }

        // 更新工具
        function updateTool(index, field, value) {
            tools[index][field] = value;
        }

        // 生成服务代码
        function generateService() {
            const serviceName = document.getElementById('serviceName').value;
            const serviceDescription = document.getElementById('serviceDescription').value;
            const serviceVersion = document.getElementById('serviceVersion').value;

            const code = generateMCPCode(serviceName, serviceDescription, serviceVersion, tools);
            
            document.getElementById('generatedCode').textContent = code;
            document.getElementById('output').classList.remove('hidden');
        }

        // 生成MCP代码
        function generateMCPCode(name, description, version, tools) {
            return `#!/usr/bin/env node

/**
 * ${description}
 * 
 * 自动生成的MCP服务
 * 生成时间: ${new Date().toISOString()}
 */

import fs from 'fs/promises';
import path from 'path';

class ${toPascalCase(name)} {
  constructor() {
    this.serverInfo = {
      name: "${name}",
      version: "${version}"
    };
    
    this.tools = [
${tools.map(tool => `      {
        name: "${tool.name}",
        description: "${tool.description}",
        inputSchema: {
          type: "object",
          properties: {
${tool.params.split(',').map(p => p.trim()).filter(p => p).map(param => `            ${param}: { type: "string", description: "${param}参数" }`).join(',\n')}
          },
          required: [${tool.params.split(',').map(p => `"${p.trim()}"`).filter(p => p !== '""').join(', ')}]
        }
      }`).join(',\n')}
    ];
  }

  async handleRequest(request) {
    try {
      if (request.jsonrpc !== "2.0") {
        throw new Error("Invalid JSON-RPC version");
      }

      let result;
      
      switch (request.method) {
        case "initialize":
          result = {
            protocolVersion: "2024-11-05",
            capabilities: { tools: {} },
            serverInfo: this.serverInfo
          };
          break;
          
        case "notifications/initialized":
          return null;
          
        case "tools/list":
          result = { tools: this.tools };
          break;
          
        case "tools/call":
          result = await this.callTool(request.params);
          break;
          
        default:
          throw new Error(\`Unknown method: \${request.method}\`);
      }

      return { jsonrpc: "2.0", id: request.id, result };
      
    } catch (error) {
      return {
        jsonrpc: "2.0", 
        id: request.id || null,
        error: { code: -32603, message: error.message }
      };
    }
  }

  async callTool(params) {
    const { name, arguments: args } = params;
    
    let content;
    
    switch (name) {
${tools.map(tool => `      case "${tool.name}":
        content = await this.${toCamelCase(tool.name)}(args);
        break;`).join('\n')}
        
      default:
        throw new Error(\`Unknown tool: \${name}\`);
    }

    return {
      content: [{ type: "text", text: content }]
    };
  }

${tools.map(tool => `  async ${toCamelCase(tool.name)}(args) {
    // TODO: 实现 ${tool.description}
    const params = ${tool.params.split(',').map(p => p.trim()).filter(p => p).map(param => `args.${param}`).join(', ')};
    return \`${tool.description}执行完成，参数: \${JSON.stringify(args)}\`;
  }`).join('\n\n')}
}

// 启动服务
async function main() {
  const service = new ${toPascalCase(name)}();
  
  console.error("🚀 ${description}已启动");
  console.error(\`🔧 可用工具: \${service.tools.map(t => t.name).join(', ')}\`);

  process.stdin.setEncoding('utf8');
  let buffer = '';
  
  process.stdin.on('data', async (chunk) => {
    buffer += chunk;
    const lines = buffer.split('\\n');
    buffer = lines.pop() || '';
    
    for (const line of lines) {
      if (line.trim()) {
        try {
          const request = JSON.parse(line.trim());
          const response = await service.handleRequest(request);
          if (response) console.log(JSON.stringify(response));
        } catch (error) {
          console.log(JSON.stringify({
            jsonrpc: "2.0", id: null,
            error: { code: -32700, message: "Parse error" }
          }));
        }
      }
    }
  });
}

main().catch(console.error);`;
        }

        // 工具函数
        function toPascalCase(str) {
            return str.replace(/(^|-)([a-z])/g, (_, __, c) => c.toUpperCase());
        }

        function toCamelCase(str) {
            return str.replace(/-([a-z])/g, (_, c) => c.toUpperCase());
        }

        // 下载服务代码
        function downloadService() {
            const serviceName = document.getElementById('serviceName').value;
            const code = document.getElementById('generatedCode').textContent;
            
            const blob = new Blob([code], { type: 'text/javascript' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${serviceName}.js`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 复制Claude配置
        function copyClaudeConfig() {
            const serviceName = document.getElementById('serviceName').value;
            const config = `{
  "mcpServers": {
    "${serviceName}": {
      "command": "node",
      "args": ["${process.cwd()}/${serviceName}.js"]
    }
  }
}`;
            
            navigator.clipboard.writeText(config).then(() => {
                alert('Claude Desktop配置已复制到剪贴板！');
            });
        }

        // 初始化
        document.querySelector('.template-card[data-template="file"]').classList.add('selected');
        loadTemplate();
    </script>
</body>
</html>`
