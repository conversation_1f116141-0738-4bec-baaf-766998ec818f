#!/usr/bin/env node

/**
 * 🌟 通用MCP服务器
 * 
 * 核心理念：一个MCP服务，无限功能扩展
 * - 客户端只需配置一次
 * - 通过配置文件启用/禁用功能模块
 * - 支持热插拔功能模块
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 功能模块注册表
const modules = new Map();

// 注册功能模块
function registerModule(name, module) {
  modules.set(name, module);
  console.error(`📦 已注册模块: ${name}`);
}

// 文件操作模块
registerModule('file-ops', {
  name: '文件操作',
  tools: [
    {
      name: 'read_file',
      description: '读取文件内容',
      inputSchema: {
        type: 'object',
        properties: {
          path: { type: 'string', description: '文件路径' }
        },
        required: ['path']
      },
      handler: async ({ path: filePath }) => {
        const content = await fs.readFile(filePath, 'utf-8');
        return `📄 文件内容 (${filePath}):\n\n${content}`;
      }
    },
    {
      name: 'write_file',
      description: '写入文件内容',
      inputSchema: {
        type: 'object',
        properties: {
          path: { type: 'string', description: '文件路径' },
          content: { type: 'string', description: '文件内容' }
        },
        required: ['path', 'content']
      },
      handler: async ({ path: filePath, content }) => {
        await fs.writeFile(filePath, content, 'utf-8');
        return `✅ 文件已写入: ${filePath}`;
      }
    },
    {
      name: 'list_directory',
      description: '列出目录内容',
      inputSchema: {
        type: 'object',
        properties: {
          path: { type: 'string', description: '目录路径', default: '.' }
        }
      },
      handler: async ({ path: dirPath = '.' }) => {
        const items = await fs.readdir(dirPath, { withFileTypes: true });
        const list = items.map(item => 
          `${item.isDirectory() ? '📁' : '📄'} ${item.name}`
        );
        return `📁 目录内容 (${dirPath}):\n${list.join('\n')}`;
      }
    }
  ]
});

// Web工具模块
registerModule('web-tools', {
  name: 'Web工具',
  tools: [
    {
      name: 'http_request',
      description: '发送HTTP请求',
      inputSchema: {
        type: 'object',
        properties: {
          url: { type: 'string', description: 'URL地址' },
          method: { type: 'string', description: 'HTTP方法', default: 'GET' }
        },
        required: ['url']
      },
      handler: async ({ url, method = 'GET' }) => {
        // 模拟HTTP请求
        return `🌐 HTTP ${method} 请求到 ${url}\n状态: 200 OK\n响应: 模拟数据`;
      }
    },
    {
      name: 'analyze_url',
      description: '分析URL结构',
      inputSchema: {
        type: 'object',
        properties: {
          url: { type: 'string', description: 'URL地址' }
        },
        required: ['url']
      },
      handler: async ({ url }) => {
        const parsed = new URL(url);
        return `🔍 URL分析结果:\n协议: ${parsed.protocol}\n域名: ${parsed.hostname}\n路径: ${parsed.pathname}`;
      }
    }
  ]
});

// 系统工具模块
registerModule('system-tools', {
  name: '系统工具',
  tools: [
    {
      name: 'get_system_info',
      description: '获取系统信息',
      inputSchema: { type: 'object', properties: {} },
      handler: async () => {
        return `💻 系统信息:\n平台: ${process.platform}\nNode版本: ${process.version}\n内存使用: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`;
      }
    },
    {
      name: 'current_time',
      description: '获取当前时间',
      inputSchema: { type: 'object', properties: {} },
      handler: async () => {
        return `⏰ 当前时间: ${new Date().toLocaleString('zh-CN')}`;
      }
    }
  ]
});

// 数据处理模块
registerModule('data-tools', {
  name: '数据处理',
  tools: [
    {
      name: 'json_format',
      description: '格式化JSON数据',
      inputSchema: {
        type: 'object',
        properties: {
          json: { type: 'string', description: 'JSON字符串' }
        },
        required: ['json']
      },
      handler: async ({ json }) => {
        try {
          const parsed = JSON.parse(json);
          return `📊 格式化后的JSON:\n${JSON.stringify(parsed, null, 2)}`;
        } catch (error) {
          return `❌ JSON格式错误: ${error.message}`;
        }
      }
    },
    {
      name: 'text_analysis',
      description: '文本分析',
      inputSchema: {
        type: 'object',
        properties: {
          text: { type: 'string', description: '要分析的文本' }
        },
        required: ['text']
      },
      handler: async ({ text }) => {
        const words = text.split(/\s+/).length;
        const chars = text.length;
        const lines = text.split('\n').length;
        return `📝 文本分析结果:\n字数: ${words}\n字符数: ${chars}\n行数: ${lines}`;
      }
    }
  ]
});

// 通用MCP服务器类
class UniversalMCPServer {
  constructor() {
    this.config = this.loadConfig();
    this.activeModules = new Map();
    this.loadActiveModules();
  }

  // 加载配置
  loadConfig() {
    try {
      // 默认配置
      return {
        name: 'universal-mcp-server',
        version: '1.0.0',
        enabledModules: ['file-ops', 'web-tools', 'system-tools', 'data-tools'] // 默认启用所有模块
      };
    } catch (error) {
      console.error('配置加载失败，使用默认配置');
      return { enabledModules: ['file-ops'] };
    }
  }

  // 加载激活的模块
  loadActiveModules() {
    for (const moduleName of this.config.enabledModules) {
      if (modules.has(moduleName)) {
        this.activeModules.set(moduleName, modules.get(moduleName));
        console.error(`✅ 已启用模块: ${moduleName}`);
      }
    }
  }

  // 获取所有工具
  getAllTools() {
    const tools = [];
    for (const [moduleName, module] of this.activeModules) {
      for (const tool of module.tools) {
        tools.push({
          ...tool,
          name: `${moduleName}__${tool.name}`, // 添加模块前缀避免冲突
          description: `[${module.name}] ${tool.description}`
        });
      }
    }
    return tools;
  }

  // 调用工具
  async callTool(toolName, args) {
    // 解析模块名和工具名
    const [moduleName, actualToolName] = toolName.split('__');
    
    if (!this.activeModules.has(moduleName)) {
      throw new Error(`模块未启用: ${moduleName}`);
    }

    const module = this.activeModules.get(moduleName);
    const tool = module.tools.find(t => t.name === actualToolName);
    
    if (!tool) {
      throw new Error(`工具不存在: ${actualToolName}`);
    }

    return await tool.handler(args);
  }

  // 处理MCP请求
  async handleRequest(request) {
    try {
      if (request.jsonrpc !== "2.0") {
        throw new Error("Invalid JSON-RPC version");
      }

      let result;
      
      switch (request.method) {
        case "initialize":
          result = {
            protocolVersion: "2024-11-05",
            capabilities: { tools: {} },
            serverInfo: {
              name: this.config.name,
              version: this.config.version
            }
          };
          break;
          
        case "notifications/initialized":
          return null;
          
        case "tools/list":
          result = { tools: this.getAllTools() };
          break;
          
        case "tools/call":
          const output = await this.callTool(request.params.name, request.params.arguments || {});
          result = {
            content: [{ type: "text", text: output }]
          };
          break;
          
        default:
          throw new Error(`Unknown method: ${request.method}`);
      }

      return { jsonrpc: "2.0", id: request.id, result };
      
    } catch (error) {
      return {
        jsonrpc: "2.0", 
        id: request.id || null,
        error: { code: -32603, message: error.message }
      };
    }
  }

  // 启动服务器
  start() {
    console.error(`🚀 通用MCP服务器已启动`);
    console.error(`📦 已启用模块: ${Array.from(this.activeModules.keys()).join(', ')}`);
    console.error(`🔧 可用工具数量: ${this.getAllTools().length}`);

    process.stdin.setEncoding('utf8');
    let buffer = '';
    
    process.stdin.on('data', async (chunk) => {
      buffer += chunk;
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.trim()) {
          try {
            const request = JSON.parse(line.trim());
            const response = await this.handleRequest(request);
            if (response) console.log(JSON.stringify(response));
          } catch (error) {
            console.log(JSON.stringify({
              jsonrpc: "2.0", id: null,
              error: { code: -32700, message: "Parse error" }
            }));
          }
        }
      }
    });
  }
}

// 启动服务器
const server = new UniversalMCPServer();
server.start();
