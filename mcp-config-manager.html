<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎛️ MCP功能配置中心</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f7; }
        .container { max-width: 1000px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 40px; }
        .header h1 { color: #1d1d1f; font-size: 2.5em; margin-bottom: 10px; }
        .header p { color: #86868b; font-size: 1.2em; }
        
        .config-panel { background: white; border-radius: 12px; padding: 30px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); margin-bottom: 30px; }
        .section { margin-bottom: 30px; }
        .section h3 { color: #1d1d1f; margin-bottom: 15px; font-size: 1.3em; }
        
        .modules-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .module-card { 
            background: #f6f6f6; border-radius: 8px; padding: 20px; 
            border: 2px solid transparent; transition: all 0.3s;
        }
        .module-card.enabled { border-color: #34c759; background: #f0fff4; }
        .module-card.disabled { border-color: #ff3b30; background: #fff5f5; }
        
        .module-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .module-title { font-size: 1.2em; font-weight: 600; color: #1d1d1f; }
        .toggle-switch { 
            position: relative; width: 50px; height: 24px; background: #ccc; 
            border-radius: 12px; cursor: pointer; transition: background 0.3s;
        }
        .toggle-switch.enabled { background: #34c759; }
        .toggle-switch::after {
            content: ''; position: absolute; top: 2px; left: 2px; width: 20px; height: 20px;
            background: white; border-radius: 50%; transition: transform 0.3s;
        }
        .toggle-switch.enabled::after { transform: translateX(26px); }
        
        .module-description { color: #86868b; margin-bottom: 15px; }
        .tools-list { list-style: none; }
        .tools-list li { 
            background: white; padding: 8px 12px; margin: 5px 0; border-radius: 6px;
            font-size: 14px; color: #1d1d1f;
        }
        
        .status-bar { 
            background: #1d1d1f; color: white; padding: 15px; border-radius: 8px;
            display: flex; justify-content: space-between; align-items: center;
        }
        .status-info { display: flex; gap: 30px; }
        .status-item { text-align: center; }
        .status-number { font-size: 1.5em; font-weight: bold; color: #34c759; }
        .status-label { font-size: 0.9em; opacity: 0.8; }
        
        .btn { 
            padding: 12px 24px; border: none; border-radius: 8px; 
            font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.3s;
        }
        .btn-primary { background: #007aff; color: white; }
        .btn-primary:hover { background: #0056cc; }
        .btn-success { background: #34c759; color: white; }
        .btn-success:hover { background: #28a745; }
        
        .claude-config { 
            background: #1d1d1f; color: #f5f5f7; padding: 20px; border-radius: 8px; 
            font-family: 'SF Mono', Monaco, monospace; font-size: 14px;
        }
        
        .notification { 
            position: fixed; top: 20px; right: 20px; padding: 15px 20px; 
            border-radius: 8px; color: white; font-weight: 600; z-index: 1000;
            transform: translateX(400px); transition: transform 0.3s;
        }
        .notification.show { transform: translateX(0); }
        .notification.success { background: #34c759; }
        .notification.error { background: #ff3b30; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎛️ MCP功能配置中心</h1>
            <p>一个MCP服务，按需启用功能模块</p>
        </div>

        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-info">
                <div class="status-item">
                    <div class="status-number" id="enabledCount">0</div>
                    <div class="status-label">已启用模块</div>
                </div>
                <div class="status-item">
                    <div class="status-number" id="toolsCount">0</div>
                    <div class="status-label">可用工具</div>
                </div>
                <div class="status-item">
                    <div class="status-number" id="totalModules">0</div>
                    <div class="status-label">总模块数</div>
                </div>
            </div>
            <div>
                <button class="btn btn-success" onclick="saveConfig()">💾 保存配置</button>
                <button class="btn btn-primary" onclick="restartServer()">🔄 重启服务</button>
            </div>
        </div>

        <!-- 模块配置 -->
        <div class="config-panel">
            <div class="section">
                <h3>📦 功能模块</h3>
                <div class="modules-grid" id="modulesGrid"></div>
            </div>
        </div>

        <!-- Claude配置 -->
        <div class="config-panel">
            <div class="section">
                <h3>🤖 Claude Desktop配置</h3>
                <p style="color: #86868b; margin-bottom: 15px;">
                    将以下配置添加到Claude Desktop的配置文件中，只需配置一次即可使用所有功能：
                </p>
                <div class="claude-config" id="claudeConfig"></div>
                <button class="btn btn-primary" onclick="copyClaudeConfig()" style="margin-top: 15px;">
                    📋 复制配置
                </button>
            </div>
        </div>
    </div>

    <!-- 通知 -->
    <div id="notification" class="notification"></div>

    <script>
        // 模块定义
        const modules = {
            'file-ops': {
                name: '文件操作',
                description: '读写文件、目录管理、文件搜索等文件系统操作',
                tools: ['read_file', 'write_file', 'list_directory'],
                enabled: true
            },
            'web-tools': {
                name: 'Web工具',
                description: 'HTTP请求、网页抓取、URL分析等网络工具',
                tools: ['http_request', 'analyze_url'],
                enabled: true
            },
            'system-tools': {
                name: '系统工具',
                description: '系统信息、时间获取等系统级工具',
                tools: ['get_system_info', 'current_time'],
                enabled: true
            },
            'data-tools': {
                name: '数据处理',
                description: 'JSON格式化、文本分析等数据处理工具',
                tools: ['json_format', 'text_analysis'],
                enabled: true
            }
        };

        // 渲染模块
        function renderModules() {
            const grid = document.getElementById('modulesGrid');
            grid.innerHTML = Object.entries(modules).map(([id, module]) => `
                <div class="module-card ${module.enabled ? 'enabled' : 'disabled'}">
                    <div class="module-header">
                        <div class="module-title">${module.name}</div>
                        <div class="toggle-switch ${module.enabled ? 'enabled' : ''}" 
                             onclick="toggleModule('${id}')"></div>
                    </div>
                    <div class="module-description">${module.description}</div>
                    <ul class="tools-list">
                        ${module.tools.map(tool => `<li>🔧 ${tool}</li>`).join('')}
                    </ul>
                </div>
            `).join('');
            
            updateStatus();
            updateClaudeConfig();
        }

        // 切换模块状态
        function toggleModule(moduleId) {
            modules[moduleId].enabled = !modules[moduleId].enabled;
            renderModules();
            showNotification(
                `${modules[moduleId].name} 已${modules[moduleId].enabled ? '启用' : '禁用'}`,
                'success'
            );
        }

        // 更新状态
        function updateStatus() {
            const enabledModules = Object.values(modules).filter(m => m.enabled);
            const totalTools = enabledModules.reduce((sum, m) => sum + m.tools.length, 0);
            
            document.getElementById('enabledCount').textContent = enabledModules.length;
            document.getElementById('toolsCount').textContent = totalTools;
            document.getElementById('totalModules').textContent = Object.keys(modules).length;
        }

        // 更新Claude配置
        function updateClaudeConfig() {
            const config = {
                mcpServers: {
                    "universal-mcp": {
                        command: "node",
                        args: [`${window.location.pathname.replace('/mcp-config-manager.html', '')}/universal-mcp-server.js`]
                    }
                }
            };
            
            document.getElementById('claudeConfig').textContent = JSON.stringify(config, null, 2);
        }

        // 保存配置
        function saveConfig() {
            const config = {
                enabledModules: Object.entries(modules)
                    .filter(([_, module]) => module.enabled)
                    .map(([id, _]) => id)
            };
            
            // 这里可以保存到文件或发送到服务器
            localStorage.setItem('mcpConfig', JSON.stringify(config));
            showNotification('配置已保存', 'success');
        }

        // 重启服务
        function restartServer() {
            showNotification('服务重启中...', 'success');
            // 这里可以发送重启信号到服务器
            setTimeout(() => {
                showNotification('服务已重启', 'success');
            }, 2000);
        }

        // 复制Claude配置
        function copyClaudeConfig() {
            const config = document.getElementById('claudeConfig').textContent;
            navigator.clipboard.writeText(config).then(() => {
                showNotification('Claude配置已复制到剪贴板', 'success');
            }).catch(() => {
                showNotification('复制失败', 'error');
            });
        }

        // 显示通知
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type} show`;
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // 加载保存的配置
        function loadConfig() {
            const saved = localStorage.getItem('mcpConfig');
            if (saved) {
                const config = JSON.parse(saved);
                // 重置所有模块为禁用
                Object.keys(modules).forEach(id => {
                    modules[id].enabled = false;
                });
                // 启用保存的模块
                config.enabledModules.forEach(id => {
                    if (modules[id]) {
                        modules[id].enabled = true;
                    }
                });
            }
        }

        // 初始化
        loadConfig();
        renderModules();
    </script>
</body>
</html>
