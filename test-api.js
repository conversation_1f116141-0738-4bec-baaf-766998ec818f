#!/usr/bin/env node

/**
 * 🧪 测试MCP平台API连接
 */

async function testAPI() {
  console.log('🧪 开始测试MCP平台API...\n');

  // 测试管理API状态
  try {
    console.log('📊 测试管理API状态...');
    const response = await fetch('http://localhost:8081/api/stats', {
      headers: {
        'Authorization': 'Bearer admin_token_placeholder'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ 管理API连接成功');
      console.log('📊 统计数据:', JSON.stringify(data, null, 2));
    } else {
      console.log('❌ 管理API连接失败:', response.status);
    }
  } catch (error) {
    console.log('❌ 管理API连接错误:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // 测试服务列表API
  try {
    console.log('🛠️ 测试服务列表API...');
    const response = await fetch('http://localhost:8081/api/services', {
      headers: {
        'Authorization': 'Bearer admin_token_placeholder'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ 服务列表API连接成功');
      console.log('🛠️ 服务列表:', JSON.stringify(data, null, 2));
    } else {
      console.log('❌ 服务列表API连接失败:', response.status);
    }
  } catch (error) {
    console.log('❌ 服务列表API连接错误:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // 测试启动服务API
  try {
    console.log('🚀 测试启动服务API...');
    const response = await fetch('http://localhost:8081/api/services/universal/start', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer admin_token_placeholder',
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ 启动服务API调用成功');
      console.log('🚀 响应:', JSON.stringify(data, null, 2));
    } else {
      console.log('❌ 启动服务API调用失败:', response.status);
      const errorText = await response.text();
      console.log('错误详情:', errorText);
    }
  } catch (error) {
    console.log('❌ 启动服务API调用错误:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // 测试MCP HTTP端点
  try {
    console.log('🌐 测试MCP HTTP端点...');
    const response = await fetch('http://localhost:9091/api/status');
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ MCP HTTP端点连接成功');
      console.log('🌐 状态:', JSON.stringify(data, null, 2));
    } else {
      console.log('❌ MCP HTTP端点连接失败:', response.status);
    }
  } catch (error) {
    console.log('❌ MCP HTTP端点连接错误:', error.message);
  }

  console.log('\n🎉 API测试完成！');
}

// 运行测试
testAPI().catch(console.error);
