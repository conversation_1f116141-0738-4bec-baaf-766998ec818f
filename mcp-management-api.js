#!/usr/bin/env node

/**
 * 📊 MCP管理API服务器
 * 
 * 为企业MCP平台提供完整的管理API
 */

import http from 'http';
import crypto from 'crypto';
import fs from 'fs/promises';

class MCPManagementAPI {
  constructor(mcpServer) {
    this.mcpServer = mcpServer;
    this.adminTokens = new Set([
      'admin_token_placeholder', // 临时令牌用于测试
      'admin_token_' + crypto.randomBytes(16).toString('hex')
    ]);
    console.log('📊 管理API初始化，管理员令牌:', Array.from(this.adminTokens));
  }

  // 验证管理员权限
  verifyAdminToken(req) {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return false;
    }
    
    const token = authHeader.substring(7);
    return this.adminTokens.has(token);
  }

  // 启动管理API服务器
  async startServer(port = 8080) {
    const server = http.createServer(async (req, res) => {
      // CORS设置
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

      if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
      }

      const url = new URL(req.url, `http://${req.headers.host}`);
      
      try {
        // 管理员权限验证
        if (!this.verifyAdminToken(req)) {
          res.writeHead(401, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ error: '需要管理员权限' }));
          return;
        }

        await this.handleRequest(req, res, url);
      } catch (error) {
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: error.message }));
      }
    });

    server.listen(port, () => {
      console.log(`📊 管理API服务器启动: http://localhost:${port}`);
    });

    return server;
  }

  // 处理API请求
  async handleRequest(req, res, url) {
    const path = url.pathname;
    const method = req.method;

    // 服务管理API
    if (path === '/api/services' && method === 'GET') {
      await this.getServices(req, res);
    } else if (path === '/api/services' && method === 'POST') {
      await this.createService(req, res);
    } else if (path.startsWith('/api/services/') && method === 'PUT') {
      await this.updateService(req, res, path);
    } else if (path.startsWith('/api/services/') && method === 'DELETE') {
      await this.deleteService(req, res, path);
    } else if (path.startsWith('/api/services/') && path.endsWith('/start') && method === 'POST') {
      await this.startService(req, res, path);
    } else if (path.startsWith('/api/services/') && path.endsWith('/stop') && method === 'POST') {
      await this.stopService(req, res, path);
    }
    
    // 用户管理API
    else if (path === '/api/users' && method === 'GET') {
      await this.getUsers(req, res);
    } else if (path === '/api/users' && method === 'POST') {
      await this.createUser(req, res);
    } else if (path.startsWith('/api/users/') && method === 'PUT') {
      await this.updateUser(req, res, path);
    } else if (path.startsWith('/api/users/') && method === 'DELETE') {
      await this.deleteUser(req, res, path);
    }
    
    // 权限管理API
    else if (path.startsWith('/api/users/') && path.endsWith('/permissions') && method === 'PUT') {
      await this.updateUserPermissions(req, res, path);
    } else if (path.startsWith('/api/users/') && path.endsWith('/tokens') && method === 'POST') {
      await this.generateUserToken(req, res, path);
    } else if (path.startsWith('/api/tokens/') && method === 'DELETE') {
      await this.revokeToken(req, res, path);
    }
    
    // 日志API
    else if (path === '/api/logs' && method === 'GET') {
      await this.getLogs(req, res, url);
    } else if (path === '/api/logs' && method === 'DELETE') {
      await this.clearLogs(req, res);
    }
    
    // 统计API
    else if (path === '/api/stats' && method === 'GET') {
      await this.getStats(req, res);
    }
    
    // API转换器管理
    else if (path === '/api/converters' && method === 'GET') {
      await this.getConverters(req, res);
    } else if (path === '/api/converters' && method === 'POST') {
      await this.createConverter(req, res);
    } else if (path.startsWith('/api/converters/') && method === 'DELETE') {
      await this.deleteConverter(req, res, path);
    }
    
    else {
      res.writeHead(404, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: 'API端点不存在' }));
    }
  }

  // 服务管理API实现
  async getServices(req, res) {
    const services = Array.from(this.mcpServer.services.values()).map(service => ({
      id: service.id,
      name: service.name,
      status: service.status,
      tools: service.tools.length,
      startTime: service.startTime,
      lastActivity: service.lastActivity
    }));

    this.sendJson(res, services);
  }

  async createService(req, res) {
    const body = await this.readBody(req);
    const serviceData = JSON.parse(body);
    
    this.mcpServer.registerService(serviceData.id, serviceData);
    this.sendJson(res, { success: true, message: '服务创建成功' });
  }

  async startService(req, res, path) {
    const serviceId = path.split('/')[3];
    await this.mcpServer.startService(serviceId);
    this.sendJson(res, { success: true, message: '服务启动成功' });
  }

  async stopService(req, res, path) {
    const serviceId = path.split('/')[3];
    await this.mcpServer.stopService(serviceId);
    this.sendJson(res, { success: true, message: '服务停止成功' });
  }

  // 用户管理API实现
  async getUsers(req, res) {
    const users = Array.from(this.mcpServer.users.values()).map(user => ({
      id: user.id,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
      active: user.active,
      createdAt: user.createdAt,
      lastLogin: user.lastLogin,
      tokenCount: user.tokens ? user.tokens.length : 0
    }));

    this.sendJson(res, users);
  }

  async createUser(req, res) {
    const body = await this.readBody(req);
    const userData = JSON.parse(body);
    
    // 生成用户ID和认证令牌
    userData.id = userData.id || crypto.randomUUID();
    userData.authToken = this.mcpServer.generateAuthToken(userData.id);
    userData.createdAt = new Date();
    userData.active = true;
    
    this.mcpServer.addUser(userData);
    this.sendJson(res, { success: true, user: userData });
  }

  async updateUserPermissions(req, res, path) {
    const userId = path.split('/')[3];
    const body = await this.readBody(req);
    const { permissions } = JSON.parse(body);
    
    await this.mcpServer.updateUserPermissions(userId, permissions);
    this.sendJson(res, { success: true, message: '权限更新成功' });
  }

  async generateUserToken(req, res, path) {
    const userId = path.split('/')[3];
    const body = await this.readBody(req);
    const options = JSON.parse(body);
    
    const token = this.mcpServer.generateAuthToken(userId, options);
    this.sendJson(res, { success: true, token });
  }

  // 日志API实现
  async getLogs(req, res, url) {
    const params = url.searchParams;
    const filter = params.get('filter') || 'all';
    const limit = parseInt(params.get('limit')) || 100;
    const offset = parseInt(params.get('offset')) || 0;
    
    let logs = this.mcpServer.callLogs;
    
    // 应用过滤器
    if (filter !== 'all') {
      logs = logs.filter(log => log.status.toLowerCase() === filter.toLowerCase());
    }
    
    // 分页
    const paginatedLogs = logs.slice(offset, offset + limit);
    
    this.sendJson(res, {
      logs: paginatedLogs,
      total: logs.length,
      offset,
      limit
    });
  }

  async clearLogs(req, res) {
    this.mcpServer.callLogs = [];
    this.sendJson(res, { success: true, message: '日志已清空' });
  }

  // 统计API实现
  async getStats(req, res) {
    const stats = this.mcpServer.getSystemStatus();
    
    // 添加详细统计
    const userStats = Array.from(this.mcpServer.users.values()).reduce((acc, user) => {
      acc[user.role] = (acc[user.role] || 0) + 1;
      return acc;
    }, {});

    const serviceStats = Array.from(this.mcpServer.services.values()).reduce((acc, service) => {
      acc[service.status] = (acc[service.status] || 0) + 1;
      return acc;
    }, {});

    const callStats = this.mcpServer.callLogs.reduce((acc, log) => {
      const date = new Date(log.timestamp).toDateString();
      acc[date] = (acc[date] || 0) + 1;
      return acc;
    }, {});

    this.sendJson(res, {
      ...stats,
      usersByRole: userStats,
      servicesByStatus: serviceStats,
      callsByDate: callStats
    });
  }

  // API转换器管理
  async getConverters(req, res) {
    const converters = Array.from(this.mcpServer.apiConverters.values());
    this.sendJson(res, converters);
  }

  async createConverter(req, res) {
    const body = await this.readBody(req);
    const converterData = JSON.parse(body);
    
    this.mcpServer.addApiConverter(converterData.id, converterData);
    this.sendJson(res, { success: true, message: 'API转换器创建成功' });
  }

  async deleteConverter(req, res, path) {
    const converterId = path.split('/')[3];
    this.mcpServer.apiConverters.delete(converterId);
    this.sendJson(res, { success: true, message: 'API转换器删除成功' });
  }

  // 工具方法
  async readBody(req) {
    return new Promise((resolve, reject) => {
      let body = '';
      req.on('data', chunk => body += chunk.toString());
      req.on('end', () => resolve(body));
      req.on('error', reject);
    });
  }

  sendJson(res, data) {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(data));
  }
}

export default MCPManagementAPI;
