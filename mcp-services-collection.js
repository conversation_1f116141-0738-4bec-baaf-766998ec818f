#!/usr/bin/env node

/**
 * 🏪 MCP服务集合
 * 
 * 包含各种实用的MCP服务，可以独立运行或集成到平台
 */

import fs from 'fs/promises';
import path from 'path';
import { spawn } from 'child_process';
import crypto from 'crypto';

// 基础MCP服务类
class BaseMCPService {
  constructor(name, version = '1.0.0') {
    this.name = name;
    this.version = version;
    this.tools = [];
  }

  addTool(tool) {
    this.tools.push(tool);
    return this;
  }

  async handleRequest(request) {
    try {
      if (request.jsonrpc !== "2.0") {
        throw new Error("Invalid JSON-RPC version");
      }

      let result;
      switch (request.method) {
        case "initialize":
          result = {
            protocolVersion: "2024-11-05",
            capabilities: { tools: {} },
            serverInfo: { name: this.name, version: this.version }
          };
          break;
        case "notifications/initialized":
          return null;
        case "tools/list":
          result = { tools: this.tools };
          break;
        case "tools/call":
          result = await this.callTool(request.params);
          break;
        default:
          throw new Error(`Unknown method: ${request.method}`);
      }
      return { jsonrpc: "2.0", id: request.id, result };
    } catch (error) {
      return {
        jsonrpc: "2.0", 
        id: request.id || null,
        error: { code: -32603, message: error.message }
      };
    }
  }

  async callTool(params) {
    const tool = this.tools.find(t => t.name === params.name);
    if (!tool) throw new Error(`Tool not found: ${params.name}`);
    
    const result = await tool.handler(params.arguments || {});
    return { content: [{ type: "text", text: result }] };
  }

  start() {
    console.error(`🚀 ${this.name} v${this.version} 已启动`);
    process.stdin.setEncoding('utf8');
    let buffer = '';
    
    process.stdin.on('data', async (chunk) => {
      buffer += chunk;
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.trim()) {
          try {
            const request = JSON.parse(line.trim());
            const response = await this.handleRequest(request);
            if (response) console.log(JSON.stringify(response));
          } catch (error) {
            console.log(JSON.stringify({
              jsonrpc: "2.0", id: null,
              error: { code: -32700, message: "Parse error" }
            }));
          }
        }
      }
    });
  }
}

// 1. 🗄️ 数据库服务
class DatabaseService extends BaseMCPService {
  constructor() {
    super('database-service');
    this.databases = new Map(); // 模拟数据库
    this.initTools();
  }

  initTools() {
    this.addTool({
      name: 'create_table',
      description: '创建数据表',
      inputSchema: {
        type: 'object',
        properties: {
          table: { type: 'string', description: '表名' },
          schema: { type: 'string', description: '表结构JSON' }
        },
        required: ['table', 'schema']
      },
      handler: async ({ table, schema }) => {
        this.databases.set(table, { schema: JSON.parse(schema), data: [] });
        return `✅ 数据表 ${table} 创建成功`;
      }
    });

    this.addTool({
      name: 'insert_data',
      description: '插入数据',
      inputSchema: {
        type: 'object',
        properties: {
          table: { type: 'string', description: '表名' },
          data: { type: 'string', description: '数据JSON' }
        },
        required: ['table', 'data']
      },
      handler: async ({ table, data }) => {
        if (!this.databases.has(table)) throw new Error(`表 ${table} 不存在`);
        const tableData = this.databases.get(table);
        tableData.data.push(JSON.parse(data));
        return `✅ 数据插入成功，当前记录数: ${tableData.data.length}`;
      }
    });

    this.addTool({
      name: 'query_data',
      description: '查询数据',
      inputSchema: {
        type: 'object',
        properties: {
          table: { type: 'string', description: '表名' },
          filter: { type: 'string', description: '过滤条件JSON', default: '{}' }
        },
        required: ['table']
      },
      handler: async ({ table, filter = '{}' }) => {
        if (!this.databases.has(table)) throw new Error(`表 ${table} 不存在`);
        const tableData = this.databases.get(table);
        const filterObj = JSON.parse(filter);
        
        let results = tableData.data;
        if (Object.keys(filterObj).length > 0) {
          results = results.filter(row => {
            return Object.entries(filterObj).every(([key, value]) => row[key] === value);
          });
        }
        
        return `📊 查询结果 (${results.length} 条记录):\n${JSON.stringify(results, null, 2)}`;
      }
    });
  }
}

// 2. 📧 邮件服务
class EmailService extends BaseMCPService {
  constructor() {
    super('email-service');
    this.initTools();
  }

  initTools() {
    this.addTool({
      name: 'send_email',
      description: '发送邮件',
      inputSchema: {
        type: 'object',
        properties: {
          to: { type: 'string', description: '收件人' },
          subject: { type: 'string', description: '主题' },
          body: { type: 'string', description: '邮件内容' }
        },
        required: ['to', 'subject', 'body']
      },
      handler: async ({ to, subject, body }) => {
        // 模拟发送邮件
        const messageId = crypto.randomUUID();
        return `📧 邮件发送成功!\n收件人: ${to}\n主题: ${subject}\n消息ID: ${messageId}`;
      }
    });

    this.addTool({
      name: 'check_inbox',
      description: '检查收件箱',
      inputSchema: { type: 'object', properties: {} },
      handler: async () => {
        // 模拟收件箱
        const emails = [
          { from: '<EMAIL>', subject: '系统通知', time: new Date().toISOString() },
          { from: '<EMAIL>', subject: '用户反馈', time: new Date().toISOString() }
        ];
        return `📬 收件箱 (${emails.length} 封邮件):\n${emails.map(e => `${e.from}: ${e.subject}`).join('\n')}`;
      }
    });
  }
}

// 3. 🔐 密码管理服务
class PasswordService extends BaseMCPService {
  constructor() {
    super('password-service');
    this.passwords = new Map();
    this.initTools();
  }

  initTools() {
    this.addTool({
      name: 'generate_password',
      description: '生成密码',
      inputSchema: {
        type: 'object',
        properties: {
          length: { type: 'number', description: '密码长度', default: 16 },
          include_symbols: { type: 'boolean', description: '包含特殊字符', default: true }
        }
      },
      handler: async ({ length = 16, include_symbols = true }) => {
        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
        const charset = include_symbols ? chars + symbols : chars;
        
        let password = '';
        for (let i = 0; i < length; i++) {
          password += charset.charAt(Math.floor(Math.random() * charset.length));
        }
        
        return `🔐 生成的密码: ${password}\n长度: ${length}\n包含特殊字符: ${include_symbols}`;
      }
    });

    this.addTool({
      name: 'store_password',
      description: '存储密码',
      inputSchema: {
        type: 'object',
        properties: {
          service: { type: 'string', description: '服务名称' },
          username: { type: 'string', description: '用户名' },
          password: { type: 'string', description: '密码' }
        },
        required: ['service', 'username', 'password']
      },
      handler: async ({ service, username, password }) => {
        const key = `${service}:${username}`;
        this.passwords.set(key, { password, created: new Date().toISOString() });
        return `✅ 密码已存储: ${service} (${username})`;
      }
    });

    this.addTool({
      name: 'get_password',
      description: '获取密码',
      inputSchema: {
        type: 'object',
        properties: {
          service: { type: 'string', description: '服务名称' },
          username: { type: 'string', description: '用户名' }
        },
        required: ['service', 'username']
      },
      handler: async ({ service, username }) => {
        const key = `${service}:${username}`;
        const stored = this.passwords.get(key);
        if (!stored) throw new Error('密码未找到');
        return `🔑 ${service} (${username}): ${stored.password}`;
      }
    });
  }
}

// 4. 📊 数据分析服务
class AnalyticsService extends BaseMCPService {
  constructor() {
    super('analytics-service');
    this.initTools();
  }

  initTools() {
    this.addTool({
      name: 'analyze_csv',
      description: '分析CSV数据',
      inputSchema: {
        type: 'object',
        properties: {
          csv_data: { type: 'string', description: 'CSV数据' }
        },
        required: ['csv_data']
      },
      handler: async ({ csv_data }) => {
        const lines = csv_data.trim().split('\n');
        const headers = lines[0].split(',');
        const rows = lines.slice(1).map(line => line.split(','));
        
        const stats = {
          total_rows: rows.length,
          columns: headers.length,
          column_names: headers,
          sample_data: rows.slice(0, 3)
        };
        
        return `📊 CSV分析结果:\n${JSON.stringify(stats, null, 2)}`;
      }
    });

    this.addTool({
      name: 'calculate_stats',
      description: '计算统计数据',
      inputSchema: {
        type: 'object',
        properties: {
          numbers: { type: 'string', description: '数字数组JSON' }
        },
        required: ['numbers']
      },
      handler: async ({ numbers }) => {
        const nums = JSON.parse(numbers);
        const sum = nums.reduce((a, b) => a + b, 0);
        const avg = sum / nums.length;
        const min = Math.min(...nums);
        const max = Math.max(...nums);
        
        return `📈 统计结果:\n总和: ${sum}\n平均值: ${avg.toFixed(2)}\n最小值: ${min}\n最大值: ${max}\n数据量: ${nums.length}`;
      }
    });
  }
}

// 5. 🌤️ 天气服务
class WeatherService extends BaseMCPService {
  constructor() {
    super('weather-service');
    this.initTools();
  }

  initTools() {
    this.addTool({
      name: 'get_weather',
      description: '获取天气信息',
      inputSchema: {
        type: 'object',
        properties: {
          city: { type: 'string', description: '城市名称' }
        },
        required: ['city']
      },
      handler: async ({ city }) => {
        // 模拟天气数据
        const weather = {
          city,
          temperature: Math.floor(Math.random() * 30) + 5,
          humidity: Math.floor(Math.random() * 50) + 30,
          condition: ['晴天', '多云', '小雨', '阴天'][Math.floor(Math.random() * 4)],
          wind_speed: Math.floor(Math.random() * 20) + 5
        };
        
        return `🌤️ ${city}天气:\n温度: ${weather.temperature}°C\n湿度: ${weather.humidity}%\n天气: ${weather.condition}\n风速: ${weather.wind_speed}km/h`;
      }
    });

    this.addTool({
      name: 'get_forecast',
      description: '获取天气预报',
      inputSchema: {
        type: 'object',
        properties: {
          city: { type: 'string', description: '城市名称' },
          days: { type: 'number', description: '预报天数', default: 3 }
        },
        required: ['city']
      },
      handler: async ({ city, days = 3 }) => {
        const forecast = [];
        for (let i = 0; i < days; i++) {
          const date = new Date();
          date.setDate(date.getDate() + i);
          forecast.push({
            date: date.toLocaleDateString('zh-CN'),
            temperature: Math.floor(Math.random() * 30) + 5,
            condition: ['晴天', '多云', '小雨', '阴天'][Math.floor(Math.random() * 4)]
          });
        }
        
        return `📅 ${city} ${days}天预报:\n${forecast.map(f => `${f.date}: ${f.temperature}°C ${f.condition}`).join('\n')}`;
      }
    });
  }
}

// 服务注册表
const services = {
  'database': () => new DatabaseService(),
  'email': () => new EmailService(),
  'password': () => new PasswordService(),
  'analytics': () => new AnalyticsService(),
  'weather': () => new WeatherService()
};

// 命令行启动
if (import.meta.url === `file://${process.argv[1]}`) {
  const serviceName = process.argv[2];
  
  if (!serviceName || !services[serviceName]) {
    console.log(`
🏪 MCP服务集合

可用服务:
  🗄️  database  - 数据库操作服务
  📧 email     - 邮件发送服务  
  🔐 password  - 密码管理服务
  📊 analytics - 数据分析服务
  🌤️  weather   - 天气查询服务

使用方法:
  node mcp-services-collection.js <service-name>
  
示例:
  node mcp-services-collection.js database
  node mcp-services-collection.js email
`);
    process.exit(1);
  }
  
  const service = services[serviceName]();
  service.start();
}

export { services, BaseMCPService };
