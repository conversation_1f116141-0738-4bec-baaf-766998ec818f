<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 MCP服务平台 - 一站式MCP管理</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f7; }
        
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px 0; }
        .header-content { max-width: 1200px; margin: 0 auto; padding: 0 20px; text-align: center; }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { font-size: 1.2em; opacity: 0.9; }
        
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        
        .tabs { display: flex; background: white; border-radius: 12px; margin-bottom: 20px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .tab { flex: 1; padding: 15px; text-align: center; cursor: pointer; transition: all 0.3s; border: none; background: none; font-size: 16px; }
        .tab.active { background: #007aff; color: white; }
        .tab:hover:not(.active) { background: #f0f0f0; }
        
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        
        .panel { background: white; border-radius: 12px; padding: 30px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .section-title { font-size: 1.5em; margin-bottom: 20px; color: #1d1d1f; }
        
        .services-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .service-card { 
            background: #f8f9fa; border-radius: 12px; padding: 20px; 
            border: 2px solid transparent; transition: all 0.3s; cursor: pointer;
        }
        .service-card:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }
        .service-card.enabled { border-color: #34c759; background: #f0fff4; }
        .service-card.disabled { border-color: #ff3b30; background: #fff5f5; }
        
        .service-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .service-title { font-size: 1.3em; font-weight: 600; }
        .service-status { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; }
        .service-status.running { background: #34c759; color: white; }
        .service-status.stopped { background: #ff3b30; color: white; }
        
        .service-description { color: #666; margin-bottom: 15px; line-height: 1.5; }
        .service-tools { display: flex; flex-wrap: gap: 8px; margin-bottom: 15px; }
        .tool-tag { background: #e3f2fd; color: #1976d2; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        
        .service-actions { display: flex; gap: 10px; }
        .btn { padding: 8px 16px; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s; }
        .btn-primary { background: #007aff; color: white; }
        .btn-success { background: #34c759; color: white; }
        .btn-danger { background: #ff3b30; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn:hover { opacity: 0.8; }
        
        .proxy-config { background: #1d1d1f; color: #f5f5f7; padding: 20px; border-radius: 8px; font-family: 'SF Mono', Monaco, monospace; font-size: 14px; }
        .claude-config { background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 8px; font-family: 'SF Mono', Monaco, monospace; font-size: 14px; }
        
        .status-dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .status-card { background: white; padding: 20px; border-radius: 12px; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status-number { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .status-label { color: #666; font-size: 14px; }
        .status-number.running { color: #34c759; }
        .status-number.stopped { color: #ff3b30; }
        .status-number.total { color: #007aff; }
        
        .log-panel { background: #1a1a1a; color: #00ff00; padding: 20px; border-radius: 8px; font-family: 'SF Mono', Monaco, monospace; font-size: 12px; height: 300px; overflow-y: auto; }
        
        .notification { position: fixed; top: 20px; right: 20px; padding: 15px 20px; border-radius: 8px; color: white; font-weight: 600; z-index: 1000; transform: translateX(400px); transition: transform 0.3s; }
        .notification.show { transform: translateX(0); }
        .notification.success { background: #34c759; }
        .notification.error { background: #ff3b30; }
        .notification.info { background: #007aff; }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>🚀 MCP服务平台</h1>
            <p>一站式MCP服务管理 - 让AI助手更强大</p>
        </div>
    </div>

    <div class="container">
        <!-- 标签页 -->
        <div class="tabs">
            <button class="tab active" onclick="switchTab('dashboard')">📊 仪表板</button>
            <button class="tab" onclick="switchTab('services')">🛠️ 服务管理</button>
            <button class="tab" onclick="switchTab('proxy')">🌐 代理配置</button>
            <button class="tab" onclick="switchTab('claude')">🤖 Claude配置</button>
            <button class="tab" onclick="switchTab('logs')">📝 日志监控</button>
        </div>

        <!-- 仪表板 -->
        <div id="dashboard" class="tab-content active">
            <div class="status-dashboard">
                <div class="status-card">
                    <div class="status-number running" id="runningCount">0</div>
                    <div class="status-label">运行中服务</div>
                </div>
                <div class="status-card">
                    <div class="status-number stopped" id="stoppedCount">0</div>
                    <div class="status-label">已停止服务</div>
                </div>
                <div class="status-card">
                    <div class="status-number total" id="totalServices">0</div>
                    <div class="status-label">总服务数</div>
                </div>
                <div class="status-card">
                    <div class="status-number total" id="totalTools">0</div>
                    <div class="status-label">可用工具</div>
                </div>
            </div>
            
            <div class="panel">
                <h3 class="section-title">🎯 快速操作</h3>
                <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                    <button class="btn btn-success" onclick="startAllServices()">🚀 启动所有服务</button>
                    <button class="btn btn-danger" onclick="stopAllServices()">⏹️ 停止所有服务</button>
                    <button class="btn btn-primary" onclick="restartProxy()">🔄 重启代理</button>
                    <button class="btn btn-secondary" onclick="exportConfig()">📤 导出配置</button>
                    <button class="btn btn-secondary" onclick="importConfig()">📥 导入配置</button>
                </div>
            </div>
        </div>

        <!-- 服务管理 -->
        <div id="services" class="tab-content">
            <div class="panel">
                <h3 class="section-title">🛠️ MCP服务管理</h3>
                <div class="services-grid" id="servicesGrid"></div>
            </div>
        </div>

        <!-- 代理配置 -->
        <div id="proxy" class="tab-content">
            <div class="panel">
                <h3 class="section-title">🌐 MCP代理服务器配置</h3>
                <p style="color: #666; margin-bottom: 20px;">
                    配置mcp-proxy服务器，将多个MCP服务聚合为统一的HTTP接口
                </p>
                <div class="proxy-config" id="proxyConfig"></div>
                <div style="margin-top: 15px;">
                    <button class="btn btn-primary" onclick="copyProxyConfig()">📋 复制配置</button>
                    <button class="btn btn-success" onclick="saveProxyConfig()">💾 保存配置</button>
                    <button class="btn btn-secondary" onclick="downloadProxyConfig()">📥 下载配置文件</button>
                </div>
            </div>
        </div>

        <!-- Claude配置 -->
        <div id="claude" class="tab-content">
            <div class="panel">
                <h3 class="section-title">🤖 Claude Desktop配置</h3>
                <p style="color: #666; margin-bottom: 20px;">
                    将以下配置添加到Claude Desktop配置文件中，即可使用所有MCP服务
                </p>
                <div class="claude-config" id="claudeConfig"></div>
                <div style="margin-top: 15px;">
                    <button class="btn btn-primary" onclick="copyClaudeConfig()">📋 复制配置</button>
                    <button class="btn btn-secondary" onclick="openClaudeConfigFolder()">📁 打开配置目录</button>
                </div>
            </div>
        </div>

        <!-- 日志监控 -->
        <div id="logs" class="tab-content">
            <div class="panel">
                <h3 class="section-title">📝 实时日志监控</h3>
                <div class="log-panel" id="logPanel">
                    [2024-01-15 10:30:00] 🚀 MCP平台启动中...<br>
                    [2024-01-15 10:30:01] 📦 加载服务配置...<br>
                    [2024-01-15 10:30:02] ✅ 平台启动完成<br>
                </div>
                <div style="margin-top: 15px;">
                    <button class="btn btn-secondary" onclick="clearLogs()">🗑️ 清空日志</button>
                    <button class="btn btn-primary" onclick="downloadLogs()">📥 下载日志</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知 -->
    <div id="notification" class="notification"></div>

    <script>
        // 服务定义
        const services = {
            'universal': {
                name: '通用MCP服务',
                description: '集成文件操作、Web工具、系统工具、数据处理等多种功能的统一服务',
                tools: ['file-ops__read_file', 'file-ops__write_file', 'web-tools__http_request', 'system-tools__get_system_info'],
                status: 'running',
                port: null,
                command: 'node',
                args: ['universal-mcp-server.js']
            },
            'database': {
                name: '数据库服务',
                description: '提供数据表创建、数据插入、查询等数据库操作功能',
                tools: ['create_table', 'insert_data', 'query_data'],
                status: 'stopped',
                port: null,
                command: 'node',
                args: ['mcp-services-collection.js', 'database']
            },
            'email': {
                name: '邮件服务',
                description: '支持邮件发送、收件箱检查等邮件相关操作',
                tools: ['send_email', 'check_inbox'],
                status: 'stopped',
                port: null,
                command: 'node',
                args: ['mcp-services-collection.js', 'email']
            },
            'password': {
                name: '密码管理',
                description: '密码生成、存储、检索等密码管理功能',
                tools: ['generate_password', 'store_password', 'get_password'],
                status: 'stopped',
                port: null,
                command: 'node',
                args: ['mcp-services-collection.js', 'password']
            },
            'analytics': {
                name: '数据分析',
                description: 'CSV数据分析、统计计算等数据分析功能',
                tools: ['analyze_csv', 'calculate_stats'],
                status: 'stopped',
                port: null,
                command: 'node',
                args: ['mcp-services-collection.js', 'analytics']
            },
            'weather': {
                name: '天气服务',
                description: '天气查询、天气预报等气象信息服务',
                tools: ['get_weather', 'get_forecast'],
                status: 'stopped',
                port: null,
                command: 'node',
                args: ['mcp-services-collection.js', 'weather']
            }
        };

        // 切换标签页
        function switchTab(tabName) {
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            event.target.classList.add('active');
            document.getElementById(tabName).classList.add('active');
            
            if (tabName === 'services') renderServices();
            if (tabName === 'proxy') updateProxyConfig();
            if (tabName === 'claude') updateClaudeConfig();
        }

        // 渲染服务列表
        function renderServices() {
            const grid = document.getElementById('servicesGrid');
            grid.innerHTML = Object.entries(services).map(([id, service]) => `
                <div class="service-card ${service.status === 'running' ? 'enabled' : 'disabled'}">
                    <div class="service-header">
                        <div class="service-title">${service.name}</div>
                        <div class="service-status ${service.status}">${service.status === 'running' ? '运行中' : '已停止'}</div>
                    </div>
                    <div class="service-description">${service.description}</div>
                    <div class="service-tools">
                        ${service.tools.map(tool => `<span class="tool-tag">${tool}</span>`).join('')}
                    </div>
                    <div class="service-actions">
                        ${service.status === 'running' ? 
                            `<button class="btn btn-danger" onclick="stopService('${id}')">⏹️ 停止</button>` :
                            `<button class="btn btn-success" onclick="startService('${id}')">🚀 启动</button>`
                        }
                        <button class="btn btn-secondary" onclick="configService('${id}')">⚙️ 配置</button>
                        <button class="btn btn-secondary" onclick="testService('${id}')">🧪 测试</button>
                    </div>
                </div>
            `).join('');
            
            updateDashboard();
        }

        // 更新仪表板
        function updateDashboard() {
            const running = Object.values(services).filter(s => s.status === 'running').length;
            const stopped = Object.values(services).filter(s => s.status === 'stopped').length;
            const totalTools = Object.values(services).reduce((sum, s) => sum + s.tools.length, 0);
            
            document.getElementById('runningCount').textContent = running;
            document.getElementById('stoppedCount').textContent = stopped;
            document.getElementById('totalServices').textContent = Object.keys(services).length;
            document.getElementById('totalTools').textContent = totalTools;
        }

        // 服务操作
        function startService(serviceId) {
            services[serviceId].status = 'running';
            showNotification(`${services[serviceId].name} 启动成功`, 'success');
            renderServices();
            addLog(`✅ ${services[serviceId].name} 启动成功`);
        }

        function stopService(serviceId) {
            services[serviceId].status = 'stopped';
            showNotification(`${services[serviceId].name} 已停止`, 'info');
            renderServices();
            addLog(`⏹️ ${services[serviceId].name} 已停止`);
        }

        function startAllServices() {
            Object.keys(services).forEach(id => {
                services[id].status = 'running';
            });
            showNotification('所有服务启动成功', 'success');
            renderServices();
            addLog('🚀 所有服务启动成功');
        }

        function stopAllServices() {
            Object.keys(services).forEach(id => {
                services[id].status = 'stopped';
            });
            showNotification('所有服务已停止', 'info');
            renderServices();
            addLog('⏹️ 所有服务已停止');
        }

        // 更新代理配置
        function updateProxyConfig() {
            const runningServices = Object.entries(services).filter(([_, service]) => service.status === 'running');
            const config = {
                mcpProxy: {
                    baseURL: "http://localhost:9090",
                    addr: ":9090",
                    name: "MCP Platform Proxy",
                    version: "1.0.0",
                    type: "streamable-http"
                },
                mcpServers: {}
            };
            
            runningServices.forEach(([id, service]) => {
                config.mcpServers[id] = {
                    command: service.command,
                    args: service.args
                };
            });
            
            document.getElementById('proxyConfig').textContent = JSON.stringify(config, null, 2);
        }

        // 更新Claude配置
        function updateClaudeConfig() {
            const config = {
                mcpServers: {
                    "mcp-platform": {
                        command: "curl",
                        args: ["-X", "POST", "http://localhost:9090/mcp"]
                    }
                }
            };
            
            document.getElementById('claudeConfig').textContent = JSON.stringify(config, null, 2);
        }

        // 复制配置
        function copyProxyConfig() {
            const config = document.getElementById('proxyConfig').textContent;
            navigator.clipboard.writeText(config).then(() => {
                showNotification('代理配置已复制', 'success');
            });
        }

        function copyClaudeConfig() {
            const config = document.getElementById('claudeConfig').textContent;
            navigator.clipboard.writeText(config).then(() => {
                showNotification('Claude配置已复制', 'success');
            });
        }

        // 日志管理
        function addLog(message) {
            const logPanel = document.getElementById('logPanel');
            const timestamp = new Date().toLocaleString();
            logPanel.innerHTML += `[${timestamp}] ${message}<br>`;
            logPanel.scrollTop = logPanel.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('logPanel').innerHTML = '';
            showNotification('日志已清空', 'info');
        }

        // 通知系统
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type} show`;
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // 初始化
        updateDashboard();
        renderServices();
        updateProxyConfig();
        updateClaudeConfig();
        
        // 模拟实时日志
        setInterval(() => {
            const messages = [
                '📊 系统状态检查完成',
                '🔄 服务健康检查',
                '📡 代理服务正常运行',
                '💾 配置自动保存'
            ];
            const randomMessage = messages[Math.floor(Math.random() * messages.length)];
            addLog(randomMessage);
        }, 10000);
    </script>
</body>
</html>
