#!/usr/bin/env node

/**
 * MCP服务测试脚本
 * 
 * 模拟MCP客户端与服务端的JSON-RPC通信
 */

import { spawn } from 'child_process';
import { setTimeout } from 'timers/promises';

console.log('🧪 测试真正的MCP服务...\n');

async function testMCPService() {
  // 启动MCP服务
  const service = spawn('node', ['simple-file-server.js'], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  let responses = [];
  
  // 监听服务响应
  service.stdout.on('data', (data) => {
    const response = data.toString().trim();
    if (response) {
      console.log('📨 服务响应:', response);
      responses.push(JSON.parse(response));
    }
  });

  // 监听服务日志
  service.stderr.on('data', (data) => {
    console.log('📝 服务日志:', data.toString().trim());
  });

  // 等待服务启动
  await setTimeout(1000);

  console.log('🔄 开始测试JSON-RPC通信...\n');

  // 测试1: 初始化
  console.log('1️⃣ 测试初始化...');
  const initMessage = JSON.stringify({
    jsonrpc: "2.0",
    id: 1,
    method: "initialize",
    params: {
      protocolVersion: "2024-11-05",
      capabilities: {},
      clientInfo: {
        name: "test-client",
        version: "1.0.0"
      }
    }
  });
  
  service.stdin.write(initMessage + '\n');
  await setTimeout(500);

  // 测试2: 列出工具
  console.log('2️⃣ 测试列出工具...');
  const toolsListMessage = JSON.stringify({
    jsonrpc: "2.0",
    id: 2,
    method: "tools/list"
  });
  
  service.stdin.write(toolsListMessage + '\n');
  await setTimeout(500);

  // 测试3: 调用工具 - 列出文件
  console.log('3️⃣ 测试列出文件...');
  const listFilesMessage = JSON.stringify({
    jsonrpc: "2.0",
    id: 3,
    method: "tools/call",
    params: {
      name: "list_files",
      arguments: {
        directory: "."
      }
    }
  });
  
  service.stdin.write(listFilesMessage + '\n');
  await setTimeout(500);

  // 测试4: 调用工具 - 写入文件
  console.log('4️⃣ 测试写入文件...');
  const writeFileMessage = JSON.stringify({
    jsonrpc: "2.0",
    id: 4,
    method: "tools/call",
    params: {
      name: "write_file",
      arguments: {
        path: "test-output.txt",
        content: "这是通过MCP服务写入的测试内容！\n时间: " + new Date().toISOString()
      }
    }
  });
  
  service.stdin.write(writeFileMessage + '\n');
  await setTimeout(500);

  // 测试5: 调用工具 - 读取刚写入的文件
  console.log('5️⃣ 测试读取文件...');
  const readFileMessage = JSON.stringify({
    jsonrpc: "2.0",
    id: 5,
    method: "tools/call",
    params: {
      name: "read_file",
      arguments: {
        path: "test-output.txt"
      }
    }
  });
  
  service.stdin.write(readFileMessage + '\n');
  await setTimeout(1000);

  // 关闭服务
  service.stdin.end();
  service.kill();

  console.log('\n📊 测试完成！');
  console.log(`✅ 收到 ${responses.length} 个响应`);
  
  // 验证响应
  const hasInit = responses.some(r => r.result && r.result.protocolVersion);
  const hasTools = responses.some(r => r.result && r.result.tools);
  const hasSuccess = responses.some(r => r.result && r.result.content);
  
  console.log(`📋 测试结果:`);
  console.log(`   初始化: ${hasInit ? '✅' : '❌'}`);
  console.log(`   工具列表: ${hasTools ? '✅' : '❌'}`);
  console.log(`   工具调用: ${hasSuccess ? '✅' : '❌'}`);
  
  if (hasInit && hasTools && hasSuccess) {
    console.log('\n🎉 MCP服务完全正常！');
    console.log('\n📋 如何与Claude Desktop集成:');
    console.log('1. 打开Claude Desktop设置');
    console.log('2. 在MCP服务配置中添加:');
    console.log('   {');
    console.log('     "simple-file-server": {');
    console.log('       "command": "node",');
    console.log(`       "args": ["${process.cwd()}/simple-file-server.js"]`);
    console.log('     }');
    console.log('   }');
    console.log('3. 重启Claude Desktop');
    console.log('4. 现在Claude可以帮你读写文件了！');
  } else {
    console.log('\n❌ 测试失败，需要检查服务实现');
  }
}

testMCPService().catch(console.error);
