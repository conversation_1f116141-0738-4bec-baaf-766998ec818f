#!/usr/bin/env node

/**
 * 真正的MCP服务示例
 * 
 * 这个服务可以与Claude Desktop等MCP客户端配合使用
 * 通过stdio进行JSON-RPC 2.0通信
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// MCP服务类
class MCPFileService {
  constructor() {
    this.tools = [
      {
        name: "read_file",
        description: "读取文件内容",
        inputSchema: {
          type: "object",
          properties: {
            path: {
              type: "string",
              description: "要读取的文件路径"
            }
          },
          required: ["path"]
        }
      },
      {
        name: "list_files",
        description: "列出目录中的文件",
        inputSchema: {
          type: "object",
          properties: {
            directory: {
              type: "string",
              description: "要列出的目录路径",
              default: "."
            }
          }
        }
      },
      {
        name: "write_file",
        description: "写入文件内容",
        inputSchema: {
          type: "object",
          properties: {
            path: {
              type: "string",
              description: "文件路径"
            },
            content: {
              type: "string",
              description: "要写入的内容"
            }
          },
          required: ["path", "content"]
        }
      }
    ];
  }

  // 处理初始化请求
  async handleInitialize(params) {
    return {
      protocolVersion: "2024-11-05",
      capabilities: {
        tools: {}
      },
      serverInfo: {
        name: "simple-file-server",
        version: "1.0.0"
      }
    };
  }

  // 列出可用工具
  async handleToolsList() {
    return { tools: this.tools };
  }

  // 执行工具调用
  async handleToolCall(params) {
    const { name, arguments: args } = params;

    try {
      let result;
      
      switch (name) {
        case "read_file":
          result = await this.readFile(args.path);
          break;
        case "list_files":
          result = await this.listFiles(args.directory || ".");
          break;
        case "write_file":
          result = await this.writeFile(args.path, args.content);
          break;
        default:
          throw new Error(`Unknown tool: ${name}`);
      }

      return {
        content: [
          {
            type: "text",
            text: typeof result === 'string' ? result : JSON.stringify(result, null, 2)
          }
        ]
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `错误: ${error.message}`
          }
        ],
        isError: true
      };
    }
  }

  // 工具实现
  async readFile(filePath) {
    const fullPath = path.resolve(filePath);
    const content = await fs.readFile(fullPath, 'utf-8');
    return `文件内容 (${filePath}):\n\n${content}`;
  }

  async listFiles(directory) {
    const fullPath = path.resolve(directory);
    const files = await fs.readdir(fullPath, { withFileTypes: true });
    
    const result = files.map(file => ({
      name: file.name,
      type: file.isDirectory() ? 'directory' : 'file'
    }));

    return `目录内容 (${directory}):\n${result.map(f => `${f.type === 'directory' ? '📁' : '📄'} ${f.name}`).join('\n')}`;
  }

  async writeFile(filePath, content) {
    const fullPath = path.resolve(filePath);
    await fs.writeFile(fullPath, content, 'utf-8');
    return `文件已写入: ${filePath}`;
  }
}

// JSON-RPC 2.0 处理器
class JSONRPCHandler {
  constructor(service) {
    this.service = service;
  }

  async handleMessage(message) {
    try {
      const request = JSON.parse(message);
      
      // 处理不同的方法
      let result;
      switch (request.method) {
        case "initialize":
          result = await this.service.handleInitialize(request.params);
          break;
        case "tools/list":
          result = await this.service.handleToolsList();
          break;
        case "tools/call":
          result = await this.service.handleToolCall(request.params);
          break;
        default:
          throw new Error(`Unknown method: ${request.method}`);
      }

      // 返回成功响应
      return JSON.stringify({
        jsonrpc: "2.0",
        id: request.id,
        result: result
      });

    } catch (error) {
      // 返回错误响应
      return JSON.stringify({
        jsonrpc: "2.0",
        id: request.id || null,
        error: {
          code: -32603,
          message: error.message
        }
      });
    }
  }
}

// 主程序
async function main() {
  const service = new MCPFileService();
  const handler = new JSONRPCHandler(service);

  console.error("🚀 MCP文件服务已启动 (通过stderr输出日志，不影响JSON-RPC通信)");
  console.error("📁 支持的操作: 读取文件、列出目录、写入文件");
  console.error("🔗 等待MCP客户端连接...");

  // 处理stdin输入 (JSON-RPC消息)
  process.stdin.setEncoding('utf8');
  
  let buffer = '';
  process.stdin.on('data', async (chunk) => {
    buffer += chunk;
    
    // 处理完整的JSON消息 (以换行符分隔)
    const lines = buffer.split('\n');
    buffer = lines.pop() || ''; // 保留不完整的行
    
    for (const line of lines) {
      if (line.trim()) {
        const response = await handler.handleMessage(line.trim());
        console.log(response); // 通过stdout返回JSON-RPC响应
      }
    }
  });

  process.stdin.on('end', () => {
    console.error("📡 MCP客户端连接已断开");
    process.exit(0);
  });
}

main().catch(error => {
  console.error("❌ 服务启动失败:", error);
  process.exit(1);
});
