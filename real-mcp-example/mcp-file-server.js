#!/usr/bin/env node

/**
 * 标准MCP文件服务
 * 
 * 完全符合MCP协议规范的文件操作服务
 * 可与Claude Desktop等MCP客户端完美集成
 */

import fs from 'fs/promises';
import path from 'path';

class MCPFileServer {
  constructor() {
    this.serverInfo = {
      name: "mcp-file-server",
      version: "1.0.0"
    };
    
    this.capabilities = {
      tools: {}
    };
    
    this.tools = [
      {
        name: "read_file",
        description: "读取文件内容",
        inputSchema: {
          type: "object",
          properties: {
            path: {
              type: "string",
              description: "要读取的文件路径"
            }
          },
          required: ["path"]
        }
      },
      {
        name: "write_file", 
        description: "写入文件内容",
        inputSchema: {
          type: "object",
          properties: {
            path: {
              type: "string",
              description: "文件路径"
            },
            content: {
              type: "string", 
              description: "要写入的内容"
            }
          },
          required: ["path", "content"]
        }
      },
      {
        name: "list_directory",
        description: "列出目录内容",
        inputSchema: {
          type: "object",
          properties: {
            path: {
              type: "string",
              description: "目录路径",
              default: "."
            }
          }
        }
      }
    ];
  }

  async handleRequest(request) {
    try {
      // 验证JSON-RPC格式
      if (request.jsonrpc !== "2.0") {
        throw new Error("Invalid JSON-RPC version");
      }

      let result;
      
      switch (request.method) {
        case "initialize":
          result = await this.initialize(request.params);
          break;
          
        case "notifications/initialized":
          // 初始化完成通知，无需响应
          return null;
          
        case "tools/list":
          result = { tools: this.tools };
          break;
          
        case "tools/call":
          result = await this.callTool(request.params);
          break;
          
        default:
          throw new Error(`Unknown method: ${request.method}`);
      }

      return {
        jsonrpc: "2.0",
        id: request.id,
        result: result
      };
      
    } catch (error) {
      return {
        jsonrpc: "2.0", 
        id: request.id || null,
        error: {
          code: -32603,
          message: error.message
        }
      };
    }
  }

  async initialize(params) {
    return {
      protocolVersion: "2024-11-05",
      capabilities: this.capabilities,
      serverInfo: this.serverInfo
    };
  }

  async callTool(params) {
    const { name, arguments: args } = params;
    
    let content;
    
    switch (name) {
      case "read_file":
        content = await this.readFile(args.path);
        break;
        
      case "write_file":
        content = await this.writeFile(args.path, args.content);
        break;
        
      case "list_directory":
        content = await this.listDirectory(args.path || ".");
        break;
        
      default:
        throw new Error(`Unknown tool: ${name}`);
    }

    return {
      content: [
        {
          type: "text",
          text: content
        }
      ]
    };
  }

  async readFile(filePath) {
    try {
      const fullPath = path.resolve(filePath);
      const content = await fs.readFile(fullPath, 'utf-8');
      return `文件内容 (${filePath}):\n\n${content}`;
    } catch (error) {
      throw new Error(`无法读取文件 ${filePath}: ${error.message}`);
    }
  }

  async writeFile(filePath, content) {
    try {
      const fullPath = path.resolve(filePath);
      await fs.writeFile(fullPath, content, 'utf-8');
      return `文件已成功写入: ${filePath}`;
    } catch (error) {
      throw new Error(`无法写入文件 ${filePath}: ${error.message}`);
    }
  }

  async listDirectory(dirPath) {
    try {
      const fullPath = path.resolve(dirPath);
      const items = await fs.readdir(fullPath, { withFileTypes: true });
      
      const fileList = items.map(item => {
        const icon = item.isDirectory() ? '📁' : '📄';
        return `${icon} ${item.name}`;
      });
      
      return `目录内容 (${dirPath}):\n${fileList.join('\n')}`;
    } catch (error) {
      throw new Error(`无法列出目录 ${dirPath}: ${error.message}`);
    }
  }
}

// 主程序
async function main() {
  const server = new MCPFileServer();
  
  // 输出启动信息到stderr (不影响JSON-RPC通信)
  console.error("🚀 MCP文件服务已启动");
  console.error(`📋 服务名称: ${server.serverInfo.name} v${server.serverInfo.version}`);
  console.error(`🔧 可用工具: ${server.tools.map(t => t.name).join(', ')}`);
  console.error("📡 等待客户端连接...");

  // 设置stdin为UTF-8编码
  process.stdin.setEncoding('utf8');
  
  let buffer = '';
  
  // 处理输入数据
  process.stdin.on('data', async (chunk) => {
    buffer += chunk;
    
    // 按行处理JSON-RPC消息
    const lines = buffer.split('\n');
    buffer = lines.pop() || '';
    
    for (const line of lines) {
      if (line.trim()) {
        try {
          const request = JSON.parse(line.trim());
          const response = await server.handleRequest(request);
          
          if (response) {
            console.log(JSON.stringify(response));
          }
        } catch (error) {
          console.error("JSON解析错误:", error.message);
          
          // 发送错误响应
          const errorResponse = {
            jsonrpc: "2.0",
            id: null,
            error: {
              code: -32700,
              message: "Parse error"
            }
          };
          console.log(JSON.stringify(errorResponse));
        }
      }
    }
  });

  // 处理连接关闭
  process.stdin.on('end', () => {
    console.error("📡 客户端连接已断开");
    process.exit(0);
  });

  // 处理进程信号
  process.on('SIGINT', () => {
    console.error("📡 收到中断信号，正在关闭服务...");
    process.exit(0);
  });
}

// 启动服务
main().catch(error => {
  console.error("❌ 服务启动失败:", error.message);
  process.exit(1);
});
