#!/usr/bin/env node

/**
 * 测试新的MCP文件服务
 */

import { spawn } from 'child_process';
import { setTimeout } from 'timers/promises';

console.log('🧪 测试标准MCP文件服务...\n');

async function testMCPServer() {
  const server = spawn('node', ['mcp-file-server.js'], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  const responses = [];
  
  server.stdout.on('data', (data) => {
    const lines = data.toString().trim().split('\n');
    for (const line of lines) {
      if (line.trim()) {
        console.log('📨 服务响应:', line);
        try {
          responses.push(JSON.parse(line));
        } catch (e) {
          console.log('   (非JSON响应)');
        }
      }
    }
  });

  server.stderr.on('data', (data) => {
    console.log('📝 服务日志:', data.toString().trim());
  });

  await setTimeout(1000);

  console.log('🔄 开始测试MCP协议...\n');

  // 测试1: 初始化
  console.log('1️⃣ 发送初始化请求...');
  const initRequest = {
    jsonrpc: "2.0",
    id: 1,
    method: "initialize",
    params: {
      protocolVersion: "2024-11-05",
      capabilities: {},
      clientInfo: {
        name: "test-client",
        version: "1.0.0"
      }
    }
  };
  server.stdin.write(JSON.stringify(initRequest) + '\n');
  await setTimeout(500);

  // 测试2: 初始化完成通知
  console.log('2️⃣ 发送初始化完成通知...');
  const initNotification = {
    jsonrpc: "2.0",
    method: "notifications/initialized"
  };
  server.stdin.write(JSON.stringify(initNotification) + '\n');
  await setTimeout(500);

  // 测试3: 列出工具
  console.log('3️⃣ 请求工具列表...');
  const toolsRequest = {
    jsonrpc: "2.0",
    id: 2,
    method: "tools/list"
  };
  server.stdin.write(JSON.stringify(toolsRequest) + '\n');
  await setTimeout(500);

  // 测试4: 调用工具 - 列出目录
  console.log('4️⃣ 调用列出目录工具...');
  const listRequest = {
    jsonrpc: "2.0",
    id: 3,
    method: "tools/call",
    params: {
      name: "list_directory",
      arguments: {
        path: "."
      }
    }
  };
  server.stdin.write(JSON.stringify(listRequest) + '\n');
  await setTimeout(500);

  // 测试5: 调用工具 - 写入文件
  console.log('5️⃣ 调用写入文件工具...');
  const writeRequest = {
    jsonrpc: "2.0",
    id: 4,
    method: "tools/call",
    params: {
      name: "write_file",
      arguments: {
        path: "mcp-test.txt",
        content: "这是通过MCP服务创建的测试文件！\n创建时间: " + new Date().toISOString()
      }
    }
  };
  server.stdin.write(JSON.stringify(writeRequest) + '\n');
  await setTimeout(500);

  // 测试6: 调用工具 - 读取文件
  console.log('6️⃣ 调用读取文件工具...');
  const readRequest = {
    jsonrpc: "2.0",
    id: 5,
    method: "tools/call",
    params: {
      name: "read_file",
      arguments: {
        path: "mcp-test.txt"
      }
    }
  };
  server.stdin.write(JSON.stringify(readRequest) + '\n');
  await setTimeout(1000);

  // 关闭服务
  server.stdin.end();
  server.kill();

  console.log('\n📊 测试完成！');
  console.log(`✅ 收到 ${responses.length} 个有效响应`);
  
  // 分析响应
  const hasInit = responses.some(r => r.result && r.result.protocolVersion);
  const hasTools = responses.some(r => r.result && r.result.tools);
  const hasToolCall = responses.some(r => r.result && r.result.content);
  const hasErrors = responses.some(r => r.error);
  
  console.log('\n📋 测试结果:');
  console.log(`   初始化响应: ${hasInit ? '✅' : '❌'}`);
  console.log(`   工具列表响应: ${hasTools ? '✅' : '❌'}`);
  console.log(`   工具调用响应: ${hasToolCall ? '✅' : '❌'}`);
  console.log(`   错误处理: ${hasErrors ? '⚠️ 有错误' : '✅ 无错误'}`);
  
  if (hasInit && hasTools && hasToolCall && !hasErrors) {
    console.log('\n🎉 MCP服务完全符合标准！');
    console.log('\n📋 Claude Desktop配置:');
    console.log('```json');
    console.log('{');
    console.log('  "mcpServers": {');
    console.log('    "file-server": {');
    console.log('      "command": "node",');
    console.log(`      "args": ["${process.cwd()}/mcp-file-server.js"]`);
    console.log('    }');
    console.log('  }');
    console.log('}');
    console.log('```');
  } else {
    console.log('\n❌ 服务存在问题，需要修复');
  }
}

testMCPServer().catch(console.error);
