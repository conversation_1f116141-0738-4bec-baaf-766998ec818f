{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "dist", "baseUrl": ".", "paths": {"@mcp-framework/core": ["./packages/core/src"], "@mcp-framework/cli": ["./packages/cli/src"], "@mcp-framework/sdk-typescript": ["./packages/sdk-typescript/src"]}}, "include": ["packages/*/src/**/*", "packages/*/tests/**/*", "examples/*/src/**/*", "tools/*/src/**/*"], "exclude": ["node_modules", "dist", "build", "coverage"]}