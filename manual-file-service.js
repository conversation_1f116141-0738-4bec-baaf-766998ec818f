#!/usr/bin/env node
// 手写MCP服务 - 需要200+行代码

import fs from 'fs/promises';

class ManualMCPService {
  constructor() {
    this.tools = [/* 定义工具... */];
  }
  
  async handleRequest(request) {
    // 50行JSON-RPC处理代码...
    try {
      if (request.jsonrpc !== "2.0") throw new Error("Invalid JSON-RPC");
      
      let result;
      switch (request.method) {
        case "initialize":
          result = {
            protocolVersion: "2024-11-05",
            capabilities: { tools: {} },
            serverInfo: { name: "manual-service", version: "1.0.0" }
          };
          break;
        case "notifications/initialized":
          return null;
        case "tools/list":
          result = { tools: this.tools };
          break;
        case "tools/call":
          result = await this.callTool(request.params);
          break;
        default:
          throw new Error(`Unknown method: ${request.method}`);
      }
      return { jsonrpc: "2.0", id: request.id, result };
    } catch (error) {
      return { jsonrpc: "2.0", id: request.id || null, error: { code: -32603, message: error.message } };
    }
  }
  
  async callTool(params) {
    // 30行工具调用代码...
    const { name, arguments: args } = params;
    let content;
    
    switch (name) {
      case "read_file":
        content = await this.readFile(args.path);
        break;
      case "write_file":
        content = await this.writeFile(args.path, args.content);
        break;
      default:
        throw new Error(`Unknown tool: ${name}`);
    }
    
    return { content: [{ type: "text", text: content }] };
  }
  
  async readFile(path) {
    // 实际工具逻辑...
    const content = await fs.readFile(path, 'utf-8');
    return `文件内容:\n${content}`;
  }
  
  async writeFile(path, content) {
    await fs.writeFile(path, content, 'utf-8');
    return `文件已写入: ${path}`;
  }
}

// 50行启动代码...
const service = new ManualMCPService();
process.stdin.setEncoding('utf8');
let buffer = '';

process.stdin.on('data', async (chunk) => {
  buffer += chunk;
  const lines = buffer.split('\n');
  buffer = lines.pop() || '';
  
  for (const line of lines) {
    if (line.trim()) {
      try {
        const request = JSON.parse(line.trim());
        const response = await service.handleRequest(request);
        if (response) console.log(JSON.stringify(response));
      } catch (error) {
        console.log(JSON.stringify({
          jsonrpc: "2.0", id: null,
          error: { code: -32700, message: "Parse error" }
        }));
      }
    }
  }
});

// 总计：200+ 行代码
