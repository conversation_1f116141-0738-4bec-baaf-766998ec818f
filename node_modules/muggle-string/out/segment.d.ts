import { Segment, Range } from './types';
export declare function segment(text: string): Segment;
export declare function segment(text: string, start: number | Range): Segment;
export declare function segment<T>(text: string, start: number | Range, data: T): Segment<T>;
export declare function segment(text: string, source: string, start: number | Range): Segment;
export declare function segment<T>(text: string, source: string, start: number | Range, data: T): Segment<T>;
