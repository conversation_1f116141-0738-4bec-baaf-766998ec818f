import type { SFCParseResult } from '@vue/compiler-sfc';
import { ISignal, Signal } from 'alien-signals';
import type * as ts from 'typescript';
import type { Sfc, VueLanguagePluginReturn } from '../types';
export declare function computedSfc(ts: typeof import('typescript'), plugins: VueLanguagePluginReturn[], fileName: string, snapshot: Signal<ts.IScriptSnapshot>, parsed: ISignal<SFCParseResult | undefined>): Sfc;
