import type { SFCParseResult } from '@vue/compiler-sfc';
import { Signal } from 'alien-signals';
import type * as ts from 'typescript';
import type { VueLanguagePluginReturn } from '../types';
export declare function computedVueSfc(plugins: VueLanguagePluginReturn[], fileName: string, languageId: string, snapshot: Signal<ts.IScriptSnapshot>): import("alien-signals").Computed<SFCParseResult | undefined>;
