{"version": 3, "file": "DynamicCommandLineAction.js", "sourceRoot": "", "sources": ["../../src/providers/DynamicCommandLineAction.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D,2DAAwD;AAExD;;GAEG;AACH,MAAa,wBAAyB,SAAQ,qCAAiB;IAC1C,KAAK,CAAC,cAAc;QACrC,WAAW;QACX,iCAAiC;IACnC,CAAC;CACF;AALD,4DAKC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\n\nimport { CommandLineAction } from './CommandLineAction';\n\n/**\n * @public\n */\nexport class DynamicCommandLineAction extends CommandLineAction {\n  protected override async onExecuteAsync(): Promise<void> {\n    // abstract\n    // (handled by the external code)\n  }\n}\n"]}