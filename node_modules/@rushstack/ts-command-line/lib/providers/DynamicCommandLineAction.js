"use strict";
// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.
// See LICENSE in the project root for license information.
Object.defineProperty(exports, "__esModule", { value: true });
exports.DynamicCommandLineAction = void 0;
const CommandLineAction_1 = require("./CommandLineAction");
/**
 * @public
 */
class DynamicCommandLineAction extends CommandLineAction_1.CommandLineAction {
    async onExecuteAsync() {
        // abstract
        // (handled by the external code)
    }
}
exports.DynamicCommandLineAction = DynamicCommandLineAction;
//# sourceMappingURL=DynamicCommandLineAction.js.map