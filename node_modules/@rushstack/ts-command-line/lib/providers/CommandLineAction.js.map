{"version": 3, "file": "CommandLineAction.js", "sourceRoot": "", "sources": ["../../src/providers/CommandLineAction.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAI3D,iFAA8E;AAC9E,6EAA0E;AA0B1E;;GAEG;AACH,MAAM,kBAAkB,GAAW,kCAAkC,CAAC;AAEtE;;;;;;;;;;;;GAYG;AACH,MAAsB,iBAAkB,SAAQ,2DAA4B;IAY1E,YAAmB,OAAkC;QACnD,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YACjD,MAAM,IAAI,KAAK,CACb,wBAAwB,OAAO,CAAC,UAAU,KAAK;gBAC7C,2FAA2F,CAC9F,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAE3C,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;IACnC,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,gBAAoC;QACtD,IAAI,CAAC,eAAe,GAAG,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE;YACjE,IAAI,EAAE,IAAI,CAAC,OAAO;YAClB,WAAW,EAAE,IAAI,CAAC,aAAa;SAChC,CAAC,CAAC;QAEH,wDAAwD;QACxD,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,CAAC,MAAc,EAAE,OAAe,EAAE,EAAE;YAC9D,MAAM,IAAI,uDAA0B,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACxD,CAAC,CAAC;QACF,MAAM,6BAA6B,GAAkC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAClG,IAAI,CAAC,eAAe,CACrB,CAAC;QACF,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,CAAC,GAAmB,EAAE,EAAE;YACnD,kFAAkF;YAClF,IAAI,GAAG,YAAY,uDAA0B,EAAE,CAAC;gBAC9C,MAAM,GAAG,CAAC;YACZ,CAAC;YACD,6BAA6B,CAAC,GAAG,CAAC,CAAC;QACrC,CAAC,CAAC;IACJ,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,aAAa;QACxB,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACa,kBAAkB;QAChC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,qCAAqC;YACrC,MAAM,IAAI,KAAK,CAAC,kFAAkF,CAAC,CAAC;QACtG,CAAC;QAED,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;CAMF;AAhFD,8CAgFC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICEN<PERSON> in the project root for license information.\n\nimport type * as argparse from 'argparse';\n\nimport { CommandLineParameterProvider } from './CommandLineParameterProvider';\nimport { CommandLineParserExitError } from './CommandLineParserExitError';\n\n/**\n * Options for the CommandLineAction constructor.\n * @public\n */\nexport interface ICommandLineActionOptions {\n  /**\n   * The name of the action.  For example, if the tool is called \"example\",\n   * then the \"build\" action might be invoked as: \"example build -q --some-other-option\"\n   */\n  actionName: string;\n\n  /**\n   * A quick summary that is shown on the main help page, which is displayed\n   * by the command \"example --help\"\n   */\n  summary: string;\n\n  /**\n   * A detailed description that is shown on the action help page, which is displayed\n   * by the command \"example build --help\", e.g. for actionName=\"build\".\n   */\n  documentation: string;\n}\n\n/**\n * Example: \"do-something\"\n */\nconst ACTION_NAME_REGEXP: RegExp = /^[a-z][a-z0-9]*([-:][a-z0-9]+)*$/;\n\n/**\n * Represents a sub-command that is part of the CommandLineParser command line.\n * Applications should create subclasses of CommandLineAction corresponding to\n * each action that they want to expose.\n *\n * The action name should be comprised of lower case words separated by hyphens\n * or colons. The name should include an English verb (e.g. \"deploy\"). Use a\n * hyphen to separate words (e.g. \"upload-docs\"). A group of related commands\n * can be prefixed with a colon (e.g. \"docs:generate\", \"docs:deploy\",\n * \"docs:serve\", etc).\n *\n * @public\n */\nexport abstract class CommandLineAction extends CommandLineParameterProvider {\n  /** {@inheritDoc ICommandLineActionOptions.actionName} */\n  public readonly actionName: string;\n\n  /** {@inheritDoc ICommandLineActionOptions.summary} */\n  public readonly summary: string;\n\n  /** {@inheritDoc ICommandLineActionOptions.documentation} */\n  public readonly documentation: string;\n\n  private _argumentParser: argparse.ArgumentParser | undefined;\n\n  public constructor(options: ICommandLineActionOptions) {\n    super();\n\n    if (!ACTION_NAME_REGEXP.test(options.actionName)) {\n      throw new Error(\n        `Invalid action name \"${options.actionName}\". ` +\n          `The name must be comprised of lower-case words optionally separated by hyphens or colons.`\n      );\n    }\n\n    this.actionName = options.actionName;\n    this.summary = options.summary;\n    this.documentation = options.documentation;\n\n    this._argumentParser = undefined;\n  }\n\n  /**\n   * This is called internally by CommandLineParser.addAction()\n   * @internal\n   */\n  public _buildParser(actionsSubParser: argparse.SubParser): void {\n    this._argumentParser = actionsSubParser.addParser(this.actionName, {\n      help: this.summary,\n      description: this.documentation\n    });\n\n    // Monkey-patch the error handling for the action parser\n    this._argumentParser.exit = (status: number, message: string) => {\n      throw new CommandLineParserExitError(status, message);\n    };\n    const originalArgumentParserErrorFn: (err: Error | string) => void = this._argumentParser.error.bind(\n      this._argumentParser\n    );\n    this._argumentParser.error = (err: Error | string) => {\n      // Ensure the ParserExitError bubbles up to the top without any special processing\n      if (err instanceof CommandLineParserExitError) {\n        throw err;\n      }\n      originalArgumentParserErrorFn(err);\n    };\n  }\n\n  /**\n   * Invoked by CommandLineParser.onExecute().\n   * @internal\n   */\n  public async _executeAsync(): Promise<void> {\n    await this.onExecuteAsync();\n  }\n\n  /**\n   * {@inheritDoc CommandLineParameterProvider._getArgumentParser}\n   * @internal\n   */\n  public override _getArgumentParser(): argparse.ArgumentParser {\n    if (!this._argumentParser) {\n      // We will improve this in the future\n      throw new Error('The CommandLineAction must be added to a CommandLineParser before it can be used');\n    }\n\n    return this._argumentParser;\n  }\n\n  /**\n   * Your subclass should implement this hook to perform the operation.\n   */\n  protected abstract onExecuteAsync(): Promise<void>;\n}\n"]}