{"version": 3, "file": "CommandLineParserExitError.js", "sourceRoot": "", "sources": ["../../src/providers/CommandLineParserExitError.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,mDAAqC;AAErC,MAAa,0BAA2B,SAAQ,KAAK;IAGnD,YAAmB,QAAgB,EAAE,OAAe;QAClD,KAAK,CAAC,OAAO,CAAC,CAAC;QAEf,sGAAsG;QACtG,6IAA6I;QAC7I,EAAE;QACF,4EAA4E;QAC3E,IAAY,CAAC,SAAS,GAAG,0BAA0B,CAAC,SAAS,CAAC,CAAC,yDAAyD;QAEzH,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;CACF;AAdD,gEAcC;AAED,MAAa,oBAAqB,SAAQ,QAAQ,CAAC,cAAc;IAC/C,IAAI,CAAC,MAAc,EAAE,OAAe;QAClD,MAAM,IAAI,0BAA0B,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACxD,CAAC;IAEe,KAAK,CAAC,GAAmB;QACvC,kFAAkF;QAClF,IAAI,GAAG,YAAY,0BAA0B,EAAE,CAAC;YAC9C,MAAM,GAAG,CAAC;QACZ,CAAC;QAED,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACnB,CAAC;CACF;AAbD,oDAaC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICEN<PERSON> in the project root for license information.\n\nimport * as argparse from 'argparse';\n\nexport class CommandLineParserExitError extends Error {\n  public readonly exitCode: number;\n\n  public constructor(exitCode: number, message: string) {\n    super(message);\n\n    // Manually set the prototype, as we can no longer extend built-in classes like Error, Array, Map, etc\n    // https://github.com/microsoft/TypeScript-wiki/blob/main/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    //\n    // Note: the prototype must also be set on any classes which extend this one\n    (this as any).__proto__ = CommandLineParserExitError.prototype; // eslint-disable-line @typescript-eslint/no-explicit-any\n\n    this.exitCode = exitCode;\n  }\n}\n\nexport class CustomArgumentParser extends argparse.ArgumentParser {\n  public override exit(status: number, message: string): void {\n    throw new CommandLineParserExitError(status, message);\n  }\n\n  public override error(err: Error | string): void {\n    // Ensure the ParserExitError bubbles up to the top without any special processing\n    if (err instanceof CommandLineParserExitError) {\n      throw err;\n    }\n\n    super.error(err);\n  }\n}\n"]}