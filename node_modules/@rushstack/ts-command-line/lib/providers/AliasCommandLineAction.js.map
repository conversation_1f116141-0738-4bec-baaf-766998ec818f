{"version": 3, "file": "AliasCommandLineAction.js", "sourceRoot": "", "sources": ["../../src/providers/AliasCommandLineAction.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,mDAAqC;AAErC,2DAAwD;AACxD,2DAImC;AA+BnC;;;;;;;;;GASG;AACH,MAAa,sBAAuB,SAAQ,qCAAiB;IAa3D,YAAmB,OAAuC;QACxD,MAAM,YAAY,GAAW,OAAO,CAAC,YAAY,CAAC;QAClD,MAAM,gBAAgB,GAAW,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC;QACjE,MAAM,uBAAuB,GAAW,CAAC,OAAO,CAAC,iBAAiB,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACpF,MAAM,OAAO,GAAW,iBAAiB,YAAY,IAAI,gBAAgB,GACvE,uBAAuB,CAAC,CAAC,CAAC,IAAI,uBAAuB,EAAE,CAAC,CAAC,CAAC,EAC5D,IAAI,CAAC;QAEL,KAAK,CAAC;YACJ,UAAU,EAAE,OAAO,CAAC,SAAS;YAC7B,OAAO;YACP,aAAa,EACX,GAAG,OAAO,oDAAoD;gBAC9D,IAAI,YAAY,IAAI,gBAAgB,WAAW;SAClD,CAAC,CAAC;QAhBG,qBAAgB,GAAwB,IAAI,GAAG,EAAE,CAAC;QAkBxD,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QACzC,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,IAAI,EAAE,CAAC;IAC3D,CAAC;IAED,gBAAgB;IACT,0BAA0B,CAAC,KAAsC;QACtE,cAAc;QACd,mGAAmG;QACnG,mBAAmB;QACnB,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,YAAY,CAAC,UAAoC,EAAE,CAAC;YAC/E,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,SAAS,CAAC;YAChD,IAAI,cAAwC,CAAC;YAC7C,MAAM,WAAW,GAA0E;gBACzF,iBAAiB,EAAE,QAAQ;gBAC3B,kBAAkB,EAAE,SAAS;aAC9B,CAAC;YACF,QAAQ,IAAI,EAAE,CAAC;gBACb,KAAK,sCAAwB,CAAC,MAAM;oBAClC,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC;wBAC1C,GAAG,WAAW;wBACd,GAAG,SAAS;wBACZ,YAAY,EAAE,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC;qBAC1C,CAAC,CAAC;oBACH,MAAM;gBACR,KAAK,sCAAwB,CAAC,UAAU;oBACtC,cAAc,GAAG,IAAI,CAAC,yBAAyB,CAAC;wBAC9C,GAAG,WAAW;wBACd,GAAG,SAAS;wBACZ,YAAY,EAAE,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC;qBAC1C,CAAC,CAAC;oBACH,MAAM;gBACR,KAAK,sCAAwB,CAAC,IAAI;oBAChC,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAC,EAAE,GAAG,WAAW,EAAE,GAAG,SAAS,EAAE,CAAC,CAAC;oBAC5E,MAAM;gBACR,KAAK,sCAAwB,CAAC,OAAO;oBACnC,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,EAAE,GAAG,WAAW,EAAE,GAAG,SAAS,EAAE,CAAC,CAAC;oBAC/E,MAAM;gBACR,KAAK,sCAAwB,CAAC,WAAW;oBACvC,cAAc,GAAG,IAAI,CAAC,0BAA0B,CAAC,EAAE,GAAG,WAAW,EAAE,GAAG,SAAS,EAAE,CAAC,CAAC;oBACnF,MAAM;gBACR,KAAK,sCAAwB,CAAC,MAAM;oBAClC,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,EAAE,GAAG,WAAW,EAAE,GAAG,SAAS,EAAE,CAAC,CAAC;oBAC9E,MAAM;gBACR,KAAK,sCAAwB,CAAC,UAAU;oBACtC,cAAc,GAAG,IAAI,CAAC,yBAAyB,CAAC,EAAE,GAAG,WAAW,EAAE,GAAG,SAAS,EAAE,CAAC,CAAC;oBAClF,MAAM;gBACR;oBACE,MAAM,IAAI,KAAK,CAAC,+BAA+B,IAAI,EAAE,CAAC,CAAC;YAC3D,CAAC;YAED,2FAA2F;YAC3F,uEAAuE;YACvE,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,cAAc,CAAC,UAAW,EAAE,SAAS,CAAC,UAAW,CAAC,CAAC;QAC/E,CAAC;QAED,4FAA4F;QAC5F,sCAAsC;QACtC,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;YAChC,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAC7D,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAChF,CAAC;QAED,gGAAgG;QAChG,iGAAiG;QACjG,mFAAmF;QACnF,IAAI,CAAC,YAAY,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;QACpD,KAAK,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;QAExC,+EAA+E;QAC/E,qCAAqC;QACrC,KAAK,MAAM,CAAC,sBAAsB,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,mCAAmC,EAAE,CAAC;YAC3F,MAAM,eAAe,GACnB,IAAI,CAAC,YAAY,CAAC,mCAAmC,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;YAEpF,yFAAyF;YACzF,IAAI,eAAe,EAAE,CAAC;gBACpB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,kBAAkB,CAAC,aAAwC,EAAE,IAA4B;QAC9F,oGAAoG;QACpG,MAAM,UAAU,GAA2B;YACzC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,UAAU;YACpC,WAAW,EAAE,IAAI,CAAC,MAAM;YACxB,kBAAkB,EAAE,IAAI,CAAC,aAAa;SACvC,CAAC;QACF,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YAChD,yFAAyF;YACzF,qFAAqF;YACrF,IAAI,GAAG,KAAK,QAAQ,EAAE,CAAC;gBACrB,SAAS;YACX,CAAC;YACD,MAAM,SAAS,GAAuB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACrE,UAAU,CAAC,SAAS,aAAT,SAAS,cAAT,SAAS,GAAI,GAAG,CAAC,GAAG,KAAK,CAAC;QACvC,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACgB,KAAK,CAAC,cAAc;QACrC,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;IAC1C,CAAC;CACF;AA3ID,wDA2IC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\n\nimport * as argparse from 'argparse';\n\nimport { CommandLineAction } from './CommandLineAction';\nimport {\n  CommandLineParameterKind,\n  type CommandLineParameterBase,\n  type CommandLineParameter\n} from '../parameters/BaseClasses';\nimport type { ICommandLineParserData, IRegisterDefinedParametersState } from './CommandLineParameterProvider';\nimport type { ICommandLineParserOptions } from './CommandLineParser';\n\n/**\n * Options for the AliasCommandLineAction constructor.\n * @public\n */\nexport interface IAliasCommandLineActionOptions {\n  /**\n   * The name of your tool when invoked from the command line. Used for generating help text.\n   */\n  toolFilename: string;\n\n  /**\n   * The name of the alias.  For example, if the tool is called \"example\",\n   * then the \"build\" alias might be invoked as: \"example build -q --some-other-option\"\n   */\n  aliasName: string;\n\n  /**\n   * A list of default parameters to pass to the target action.\n   */\n  defaultParameters?: string[];\n\n  /**\n   * The action that this alias invokes.\n   */\n  targetAction: CommandLineAction;\n}\n\n/**\n * Represents a sub-command that is part of the CommandLineParser command line.\n * The sub-command is an alias for another existing action.\n *\n * The alias name should be comprised of lower case words separated by hyphens\n * or colons. The name should include an English verb (e.g. \"deploy\"). Use a\n * hyphen to separate words (e.g. \"upload-docs\").\n *\n * @public\n */\nexport class AliasCommandLineAction extends CommandLineAction {\n  /**\n   * The action that this alias invokes.\n   */\n  public readonly targetAction: CommandLineAction;\n\n  /**\n   * A list of default arguments to pass to the target action.\n   */\n  public readonly defaultParameters: ReadonlyArray<string>;\n\n  private _parameterKeyMap: Map<string, string> = new Map();\n\n  public constructor(options: IAliasCommandLineActionOptions) {\n    const toolFilename: string = options.toolFilename;\n    const targetActionName: string = options.targetAction.actionName;\n    const defaultParametersString: string = (options.defaultParameters || []).join(' ');\n    const summary: string = `An alias for \"${toolFilename} ${targetActionName}${\n      defaultParametersString ? ` ${defaultParametersString}` : ''\n    }\".`;\n\n    super({\n      actionName: options.aliasName,\n      summary,\n      documentation:\n        `${summary} For more information on the aliased command, use ` +\n        `\"${toolFilename} ${targetActionName} --help\".`\n    });\n\n    this.targetAction = options.targetAction;\n    this.defaultParameters = options.defaultParameters || [];\n  }\n\n  /** @internal */\n  public _registerDefinedParameters(state: IRegisterDefinedParametersState): void {\n    /* override */\n    // All parameters are going to be defined by the target action. Re-use the target action parameters\n    // for this action.\n    for (const parameter of this.targetAction.parameters as CommandLineParameter[]) {\n      const { kind, longName, shortName } = parameter;\n      let aliasParameter: CommandLineParameterBase;\n      const nameOptions: { parameterLongName: string; parameterShortName: string | undefined } = {\n        parameterLongName: longName,\n        parameterShortName: shortName\n      };\n      switch (kind) {\n        case CommandLineParameterKind.Choice:\n          aliasParameter = this.defineChoiceParameter({\n            ...nameOptions,\n            ...parameter,\n            alternatives: [...parameter.alternatives]\n          });\n          break;\n        case CommandLineParameterKind.ChoiceList:\n          aliasParameter = this.defineChoiceListParameter({\n            ...nameOptions,\n            ...parameter,\n            alternatives: [...parameter.alternatives]\n          });\n          break;\n        case CommandLineParameterKind.Flag:\n          aliasParameter = this.defineFlagParameter({ ...nameOptions, ...parameter });\n          break;\n        case CommandLineParameterKind.Integer:\n          aliasParameter = this.defineIntegerParameter({ ...nameOptions, ...parameter });\n          break;\n        case CommandLineParameterKind.IntegerList:\n          aliasParameter = this.defineIntegerListParameter({ ...nameOptions, ...parameter });\n          break;\n        case CommandLineParameterKind.String:\n          aliasParameter = this.defineStringParameter({ ...nameOptions, ...parameter });\n          break;\n        case CommandLineParameterKind.StringList:\n          aliasParameter = this.defineStringListParameter({ ...nameOptions, ...parameter });\n          break;\n        default:\n          throw new Error(`Unsupported parameter kind: ${kind}`);\n      }\n\n      // We know the parserKey is defined because the underlying _defineParameter method sets it,\n      // and all parameters that we have access to have already been defined.\n      this._parameterKeyMap.set(aliasParameter._parserKey!, parameter._parserKey!);\n    }\n\n    // We also need to register the remainder parameter if the target action has one. The parser\n    // key for this parameter is constant.\n    if (this.targetAction.remainder) {\n      this.defineCommandLineRemainder(this.targetAction.remainder);\n      this._parameterKeyMap.set(argparse.Const.REMAINDER, argparse.Const.REMAINDER);\n    }\n\n    // Finally, register the parameters with the parser. We need to make sure that the target action\n    // is registered, since we need to re-use its parameters, and ambiguous parameters are discovered\n    // during registration. This will no-op if the target action is already registered.\n    this.targetAction._registerDefinedParameters(state);\n    super._registerDefinedParameters(state);\n\n    // We need to re-map the ambiguous parameters after they are defined by calling\n    // super._registerDefinedParameters()\n    for (const [ambiguousParameterName, parserKey] of this._ambiguousParameterParserKeysByName) {\n      const targetParserKey: string | undefined =\n        this.targetAction._ambiguousParameterParserKeysByName.get(ambiguousParameterName);\n\n      // If we have a mapping for the specified key, then use it. Otherwise, use the key as-is.\n      if (targetParserKey) {\n        this._parameterKeyMap.set(parserKey, targetParserKey);\n      }\n    }\n  }\n\n  /**\n   * {@inheritdoc CommandLineParameterProvider._processParsedData}\n   * @internal\n   */\n  public _processParsedData(parserOptions: ICommandLineParserOptions, data: ICommandLineParserData): void {\n    // Re-map the parsed data to the target action's parameters and execute the target action processor.\n    const targetData: ICommandLineParserData = {\n      action: this.targetAction.actionName,\n      aliasAction: data.action,\n      aliasDocumentation: this.documentation\n    };\n    for (const [key, value] of Object.entries(data)) {\n      // If we have a mapping for the specified key, then use it. Otherwise, use the key as-is.\n      // Skip over the action key though, since we've already re-mapped it to \"aliasAction\"\n      if (key === 'action') {\n        continue;\n      }\n      const targetKey: string | undefined = this._parameterKeyMap.get(key);\n      targetData[targetKey ?? key] = value;\n    }\n    this.targetAction._processParsedData(parserOptions, targetData);\n  }\n\n  /**\n   * Executes the target action.\n   */\n  protected override async onExecuteAsync(): Promise<void> {\n    await this.targetAction._executeAsync();\n  }\n}\n"]}