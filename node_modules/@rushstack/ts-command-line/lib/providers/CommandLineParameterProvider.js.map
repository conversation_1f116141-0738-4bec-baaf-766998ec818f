{"version": 3, "file": "CommandLineParameterProvider.js", "sourceRoot": "", "sources": ["../../src/providers/CommandLineParameterProvider.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,mDAAqC;AAarC,2DAKmC;AACnC,yFAGkD;AAClD,iGAA8F;AAC9F,2FAGmD;AACnD,mGAAgG;AAChG,qFAAkF;AAClF,yFAGkD;AAClD,iGAA8F;AAC9F,6EAA0E;AAC1E,4CAAuD;AACvD,6EAA0E;AA6C1E,MAAM,gBAAgB,GAAW,OAAO,CAAC;AACzC,MAAM,oBAAoB,GAAW,UAAU,CAAC;AAChD,MAAM,gCAAgC,GACpC,gFAAgF,CAAC;AAanF;;;;;GAKG;AACH,MAAsB,4BAA4B;IAmBhD,gBAAgB;IAChB,0EAA0E;IAC1E;QACE,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,qBAAqB,GAAG,IAAI,GAAG,EAAE,CAAC;QACvC,IAAI,CAAC,sBAAsB,GAAG,IAAI,GAAG,EAAE,CAAC;QACxC,IAAI,CAAC,sBAAsB,GAAG,IAAI,GAAG,EAAE,CAAC;QACxC,IAAI,CAAC,mCAAmC,GAAG,IAAI,GAAG,EAAE,CAAC;QACrD,IAAI,CAAC,oCAAoC,GAAG,IAAI,GAAG,EAAE,CAAC;QACtD,IAAI,CAAC,6BAA6B,GAAG,KAAK,CAAC;QAC3C,IAAI,CAAC,4BAA4B,GAAG,KAAK,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAW,mBAAmB;QAC5B,OAAO,IAAI,CAAC,4BAA4B,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACH,IAAW,SAAS;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAoCM,qBAAqB,CAC1B,UAAiD;QAEjD,MAAM,SAAS,GAAwC,IAAI,uDAA0B,CAAC,UAAU,CAAC,CAAC;QAClG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QACjC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,iBAAyB,EAAE,cAAuB;QAC1E,OAAO,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,sCAAwB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;IAChG,CAAC;IAED;;;;;;;;;;OAUG;IACI,yBAAyB,CAC9B,UAAqD;QAErD,MAAM,SAAS,GAA4C,IAAI,+DAA8B,CAAC,UAAU,CAAC,CAAC;QAC1G,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QACjC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACI,sBAAsB,CAC3B,iBAAyB,EACzB,cAAuB;QAEvB,OAAO,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,sCAAwB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;IACpG,CAAC;IAED;;;;;;;;;OASG;IACI,mBAAmB,CAAC,UAAsC;QAC/D,MAAM,SAAS,GAA6B,IAAI,mDAAwB,CAAC,UAAU,CAAC,CAAC;QACrF,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QACjC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,iBAAyB,EAAE,cAAuB;QACxE,OAAO,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,sCAAwB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;IAC9F,CAAC;IA8BM,sBAAsB,CAAC,UAAyC;QACrE,MAAM,SAAS,GAAgC,IAAI,yDAA2B,CAAC,UAAU,CAAC,CAAC;QAC3F,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QACjC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CACxB,iBAAyB,EACzB,cAAuB;QAEvB,OAAO,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,sCAAwB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;IACjG,CAAC;IAED;;;;;;;;;OASG;IACI,0BAA0B,CAC/B,UAA6C;QAE7C,MAAM,SAAS,GAAoC,IAAI,iEAA+B,CAAC,UAAU,CAAC,CAAC;QACnG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QACjC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACI,uBAAuB,CAC5B,iBAAyB,EACzB,cAAuB;QAEvB,OAAO,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,sCAAwB,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;IACrG,CAAC;IA8BM,qBAAqB,CAAC,UAAwC;QACnE,MAAM,SAAS,GAA+B,IAAI,uDAA0B,CAAC,UAAU,CAAC,CAAC;QACzF,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QACjC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,iBAAyB,EAAE,cAAuB;QAC1E,OAAO,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,sCAAwB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;IAChG,CAAC;IAED;;;;;;;;;OASG;IACI,yBAAyB,CAC9B,UAA4C;QAE5C,MAAM,SAAS,GAAmC,IAAI,+DAA8B,CAAC,UAAU,CAAC,CAAC;QACjG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QACjC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACI,0BAA0B,CAAC,UAA2C;QAC3E,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAC;QAC1F,CAAC;QACD,IAAI,CAAC,UAAU,GAAG,IAAI,2CAAoB,CAAC,UAAU,CAAC,CAAC;QACvD,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACI,sBAAsB,CAC3B,iBAAyB,EACzB,cAAuB;QAEvB,OAAO,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,sCAAwB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;IACpG,CAAC;IAED;;OAEG;IACI,cAAc;QACnB,MAAM,YAAY,GAAoC;YACpD,oBAAoB,EAAE,IAAI,GAAG,EAAE;SAChC,CAAC;QACF,IAAI,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,UAAU,EAAE,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,eAAe;QACpB,MAAM,YAAY,GAAoC;YACpD,oBAAoB,EAAE,IAAI,GAAG,EAAE;SAChC,CAAC;QACF,IAAI,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,WAAW,EAAE,CAAC;IACjD,CAAC;IAED;;;;OAIG;IACI,qBAAqB;QAC1B,MAAM,YAAY,GAA2B,EAAE,CAAC;QAChD,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,MAAM,aAAa,GAAW,SAAS,CAAC,cAAc,IAAI,SAAS,CAAC,QAAQ,CAAC;YAC7E,QAAQ,SAAS,CAAC,IAAI,EAAE,CAAC;gBACvB,KAAK,sCAAwB,CAAC,IAAI,CAAC;gBACnC,KAAK,sCAAwB,CAAC,MAAM,CAAC;gBACrC,KAAK,sCAAwB,CAAC,MAAM,CAAC;gBACrC,KAAK,sCAAwB,CAAC,OAAO;oBACnC,YAAY,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,SAAS,CAExC,SAKD,CAAC,KAAK,CACR,CAAC;oBACF,MAAM;gBACR,KAAK,sCAAwB,CAAC,UAAU,CAAC;gBACzC,KAAK,sCAAwB,CAAC,WAAW,CAAC;gBAC1C,KAAK,sCAAwB,CAAC,UAAU;oBACtC,MAAM,UAAU,GACd,SAID,CAAC,MAAM,CAAC;oBACT,YAAY,CAAC,aAAa,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACrE,MAAM;YACV,CAAC;QACH,CAAC;QACD,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,cAAsB;QAC/C,MAAM,MAAM,GAA2B,gCAAgC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC7F,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,4BAA4B,cAAc,iBAAiB,CAAC,CAAC;QAC/E,CAAC;QACD,OAAO;YACL,QAAQ,EAAE,KAAK,MAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE;YACpD,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;SACvC,CAAC;IACJ,CAAC;IAED,gBAAgB;IACT,0BAA0B,CAAC,KAAsC;QACtE,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACvC,mGAAmG;YACnG,gEAAgE;YAChE,OAAO;QACT,CAAC;QAED,4GAA4G;QAC5G,2GAA2G;QAC3G,0DAA0D;QAC1D,MAAM,iCAAiC,GAAkC,IAAI,GAAG,EAAE,CAAC;QACnF,KAAK,MAAM,CAAC,SAAS,EAAE,mBAAmB,CAAC,IAAI,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,EAAE,CAAC;YACrF,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACnC,KAAK,MAAM,SAAS,IAAI,mBAAmB,EAAE,CAAC;oBAC5C,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC;oBAC1C,iCAAiC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBACnD,CAAC;YACH,CAAC;QACH,CAAC;QAED,0GAA0G;QAC1G,2GAA2G;QAC3G,oCAAoC;QACpC,KAAK,MAAM,kBAAkB,IAAI,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,EAAE,CAAC;YACrE,MAAM,iBAAiB,GAAY,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC;YACjE,KAAK,MAAM,SAAS,IAAI,kBAAkB,EAAE,CAAC;gBAC3C,IAAI,iBAAiB,EAAE,CAAC;oBACtB,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;wBAC9B,MAAM,IAAI,KAAK,CACb,kBAAkB,SAAS,CAAC,QAAQ,uDAAuD;4BACzF,yDAAyD,CAC5D,CAAC;oBACJ,CAAC;oBACD,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;gBACrD,CAAC;gBAED,MAAM,eAAe,GAAY,iCAAiC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBAClF,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,iBAAiB,EAAE,eAAe,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;QAED,gGAAgG;QAChG,iBAAiB;QACjB,MAAM,EAAE,oBAAoB,EAAE,GAAG,KAAK,CAAC;QACvC,KAAK,MAAM,mBAAmB,IAAI,oBAAoB,EAAE,CAAC;YACvD,IAAI,CAAC,yBAAyB,CAAC,mBAAmB,CAAC,CAAC;QACtD,CAAC;QAED,0GAA0G;QAC1G,8CAA8C;QAC9C,KAAK,MAAM,CAAC,sBAAsB,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,mCAAmC,EAAE,CAAC;YAC3F,yGAAyG;YACzG,sGAAsG;YACtG,yDAAyD;YACzD,IAAI,CAAC,IAAI,CAAC,oCAAoC,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,CAAC;gBAC3E,IAAI,CAAC,2BAA2B,CAAC,sBAAsB,EAAE,SAAS,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;QAED,2CAA2C;QAC3C,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,MAAM,eAAe,GAA6B;gBAChD,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW;gBACjC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,SAAS;gBAC/B,OAAO,EAAE,OAAO;aACjB,CAAC;YAEF,IAAI,CAAC,kBAAkB,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;QACnF,CAAC;QAED,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;IAC5C,CAAC;IAQD;;;OAGG;IACI,SAAS;;QACd,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACzC,MAAA,SAAS,CAAC,SAAS,yDAAI,CAAC;QAC1B,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,UAAU;;QACf,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACzC,MAAA,SAAS,CAAC,UAAU,yDAAI,CAAC;QAC3B,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,kBAAkB,CAAC,aAAwC,EAAE,IAA4B;;QAC9F,IAAI,CAAC,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACxC,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAED,0EAA0E;QAC1E,KAAK,MAAM,CAAC,aAAa,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,mCAAmC,EAAE,CAAC;YAClF,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;gBACpB,mGAAmG;gBACnG,mDAAmD;gBACnD,IAAI,IAAI,CAAC,oCAAoC,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,SAAS,EAAE,CAAC;oBAC/E,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,EAAE,sBAAsB,aAAa,IAAI,CAAC,CAAC;gBAC9F,CAAC;gBAED,oGAAoG;gBACpG,gDAAgD;gBAChD,MAAM,4BAA4B,GAChC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBACjD,IAAI,4BAA4B,EAAE,CAAC;oBACjC,8FAA8F;oBAC9F,wDAAwD;oBACxD,MAAM,qBAAqB,GAAa,EAAE,CAAC;oBAC3C,KAAK,MAAM,SAAS,IAAI,4BAA4B,EAAE,CAAC;wBACrD,MAAM,0BAA0B,GAC9B,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;wBACrD,IAAI,CAAC,CAAA,0BAA0B,aAA1B,0BAA0B,uBAA1B,0BAA0B,CAAE,MAAM,CAAA,EAAE,CAAC;4BACxC,2BAA2B;4BAC3B,MAAM,IAAI,KAAK,CACb,2EAA2E,aAAa,IAAI,CAC7F,CAAC;wBACJ,CAAC;wBACD,+FAA+F;wBAC/F,sFAAsF;wBACtF,IAAI,0BAA0B,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BAC1C,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;gCAC9B,2BAA2B;gCAC3B,MAAM,IAAI,KAAK,CACb,uEAAuE,aAAa,IAAI,CACzF,CAAC;4BACJ,CAAC;4BACD,qBAAqB,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;wBACvD,CAAC;6BAAM,CAAC;4BACN,qBAAqB,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;wBACjD,CAAC;oBACH,CAAC;oBAED,mGAAmG;oBACnG,kBAAkB;oBAClB,kEAAkE;oBAClE,IAAI,CAAC,qBAAqB,CACxB,aAAa,EACb,IAAI,EACJ,CAAC,EACD,sBAAsB,aAAa,iBAAiB,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CACxF,CAAC;gBACJ,CAAC;gBAED,MAAM,2BAA2B,GAC/B,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBAChD,IAAI,2BAA2B,EAAE,CAAC;oBAChC,MAAM,qBAAqB,GAAa,2BAA2B,CAAC,GAAG,CACrE,CAAC,CAA2B,EAAE,EAAE;wBAC9B,iDAAiD;wBACjD,IAAI,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC;4BACtB,2BAA2B;4BAC3B,MAAM,IAAI,KAAK,CACb,sEAAsE,aAAa,IAAI,CACxF,CAAC;wBACJ,CAAC;wBACD,OAAO,CAAC,CAAC,cAAc,CAAC;oBAC1B,CAAC,CACF,CAAC;oBAEF,gGAAgG;oBAChG,2BAA2B;oBAC3B,gFAAgF;oBAChF,IAAI,CAAC,qBAAqB,CACxB,aAAa,EACb,IAAI,EACJ,CAAC,EACD,sBAAsB,aAAa,iBAAiB,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CACxF,CAAC;gBACJ,CAAC;gBAED,6FAA6F;gBAC7F,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,EAAE,sBAAsB,aAAa,IAAI,CAAC,CAAC;YAC9F,CAAC;QACH,CAAC;QAED,wCAAwC;QACxC,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,KAAK,GAAY,IAAI,CAAC,SAAS,CAAC,UAAW,CAAC,CAAC;YACnD,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC3B,MAAA,SAAS,CAAC,cAAc,yDAAI,CAAC;QAC/B,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC;IAC3C,CAAC;IAED,gBAAgB;IACN,gBAAgB,CAAC,SAA+B;QACxD,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;QAC/E,CAAC;QAED,qDAAqD;QACrD,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAE3C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEjC,uGAAuG;QACvG,IAAI,kBAAkB,GAAuC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CACzF,SAAS,CAAC,QAAQ,CACnB,CAAC;QACF,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxB,kBAAkB,GAAG,EAAE,CAAC;YACxB,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;QACzE,CAAC;QACD,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEnC,wGAAwG;QACxG,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;YACxB,IAAI,mBAAmB,GAAuC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAC3F,SAAS,CAAC,SAAS,CACpB,CAAC;YACF,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACzB,mBAAmB,GAAG,EAAE,CAAC;gBACzB,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;YAC5E,CAAC;YACD,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED,gBAAgB;IACN,yBAAyB,CAAC,IAAY;QAC9C,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;QAC/E,CAAC;QAED,qFAAqF;QACrF,oEAAoE;QACpE,IAAI,iBAAiB,GACnB,IAAI,CAAC,oCAAoC,CAAC,GAAG,CAAC,IAAI,CAAC;YACnD,IAAI,CAAC,mCAAmC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACrD,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,iBAAiB,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,mCAAmC,CAAC,GAAG,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC;QACtE,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,gBAAgB;IACN,kBAAkB,CAC1B,SAA+B,EAC/B,iBAA0B,EAC1B,eAAwB;;QAExB,MAAM,EACJ,SAAS,EACT,QAAQ,EACR,cAAc,EACd,WAAW,EACX,IAAI,EACJ,QAAQ,EACR,mBAAmB,EACnB,cAAc,EACd,oBAAoB,EACpB,UAAU,EAAE,SAAS,EACtB,GAAG,SAAS,CAAC;QAEd,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,IAAI,SAAS,IAAI,CAAC,eAAe,EAAE,CAAC;YAClC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACxB,CAAC;QAED,wDAAwD;QACxD,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvB,CAAC;QAED,wCAAwC;QACxC,IAAI,cAAc,EAAE,CAAC;YACnB,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC7B,CAAC;QAED,IAAI,gBAAgB,GAAW,WAAW,CAAC;QAE3C,MAAM,kBAAkB,GAAa,EAAE,CAAC;QACxC,SAAS,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,CAAC;QACrD,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,uEAAuE;YACvE,IAAI,gBAAgB,CAAC,KAAK,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBAC9C,gBAAgB,GAAG,gBAAgB,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC;YACtD,CAAC;YACD,gCAAgC;YAChC,gBAAgB,IAAI,GAAG,GAAG,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,OAA6B,CAAC;QAClC,IAAI,MAA0B,CAAC;QAC/B,IAAI,IAAwB,CAAC;QAC7B,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,sCAAwB,CAAC,MAAM,CAAC,CAAC,CAAC;gBACrC,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;gBAC7C,MAAM;YACR,CAAC;YACD,KAAK,sCAAwB,CAAC,UAAU,CAAC,CAAC,CAAC;gBACzC,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;gBAC7C,MAAM,GAAG,QAAQ,CAAC;gBAClB,MAAM;YACR,CAAC;YACD,KAAK,sCAAwB,CAAC,IAAI;gBAChC,MAAM,GAAG,WAAW,CAAC;gBACrB,MAAM;YACR,KAAK,sCAAwB,CAAC,OAAO;gBACnC,IAAI,GAAG,KAAK,CAAC;gBACb,MAAM;YACR,KAAK,sCAAwB,CAAC,WAAW;gBACvC,IAAI,GAAG,KAAK,CAAC;gBACb,MAAM,GAAG,QAAQ,CAAC;gBAClB,MAAM;YACR,KAAK,sCAAwB,CAAC,MAAM;gBAClC,MAAM;YACR,KAAK,sCAAwB,CAAC,UAAU;gBACtC,MAAM,GAAG,QAAQ,CAAC;gBAClB,MAAM;QACV,CAAC;QAED,2FAA2F;QAC3F,0CAA0C;QAC1C,MAAM,eAAe,GAA6B;YAChD,IAAI,EAAE,gBAAgB;YACtB,IAAI,EAAE,SAAS;YACf,OAAO,EAAG,SAA8C,CAAC,YAAY;YACrE,QAAQ;YACR,OAAO;YACP,MAAM;YACN,IAAI;SACL,CAAC;QAEF,MAAM,cAAc,GAA4B,IAAI,CAAC,kBAAkB,EAA6B,CAAC;QACrG,IAAI,aAAiD,CAAC;QACtD,IAAI,cAAc,EAAE,CAAC;YACnB,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAChE,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,IAAI,kBAA0B,CAAC;gBAC/B,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE,CAAC;oBACvC,kBAAkB,GAAG,cAAc,CAAC;gBACtC,CAAC;qBAAM,IAAI,cAAc,KAAK,mCAAuB,EAAE,CAAC;oBACtD,kBAAkB,GAAG,SAAS,CAAC;gBACjC,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,8BAA8B,GAAG,cAAc,CAAC,CAAC;gBACnE,CAAC;gBAED,aAAa,GAAG,cAAc,CAAC,gBAAgB,CAAC;oBAC9C,KAAK,EAAE,YAAY,kBAAkB,YAAY;iBAClD,CAAC,CAAC;gBACH,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;aAAM,CAAC;YACN,aAAa,GAAG,cAAc,CAAC;QACjC,CAAC;QAED,MAAM,gBAAgB,GAA8B,aAAwC,CAAC,WAAW,CACtG,KAAK,EACL,eAAe,CAChB,CAAC;QACF,IAAI,QAAQ,IAAI,mBAAmB,EAAE,CAAC;YACpC,wFAAwF;YAExF,MAAM,gBAAgB,GAA6B,MAAA,SAAS,CAAC,SAAS,0CAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YACxF,SAAS,CAAC,SAAS,GAAG,GAAG,EAAE;gBACzB,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,EAAI,CAAC;gBACrB,6EAA6E;gBAC7E,gBAAgB,CAAC,QAAQ,GAAG,KAAK,CAAC;YACpC,CAAC,CAAC;YAEF,MAAM,iBAAiB,GAA6B,MAAA,SAAS,CAAC,UAAU,0CAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1F,SAAS,CAAC,UAAU,GAAG,GAAG,EAAE;gBAC1B,0DAA0D;gBAC1D,gBAAgB,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACjC,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,EAAI,CAAC;YACxB,CAAC,CAAC;YAEF,SAAS,0BAA0B;gBACjC,cAAc,CAAC,KAAK,CAAC,aAAa,QAAQ,eAAe,CAAC,CAAC;YAC7D,CAAC;YAED,MAAM,qBAAqB,GAA6B,MAAA,SAAS,CAAC,cAAc,0CAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAClG,qFAAqF;YACrF,0FAA0F;YAC1F,iCAAiC;YACjC,QAAQ,IAAI,EAAE,CAAC;gBACb,KAAK,sCAAwB,CAAC,MAAM,CAAC;gBACrC,KAAK,sCAAwB,CAAC,OAAO,CAAC;gBACtC,KAAK,sCAAwB,CAAC,MAAM;oBAClC,SAAS,CAAC,cAAc,GAAG;wBACzB,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;4BACpD,0BAA0B,EAAE,CAAC;wBAC/B,CAAC;wBAED,qBAAqB,aAArB,qBAAqB,uBAArB,qBAAqB,EAAI,CAAC;oBAC5B,CAAC,CAAC;oBACF,MAAM;gBACR,KAAK,sCAAwB,CAAC,UAAU,CAAC;gBACzC,KAAK,sCAAwB,CAAC,WAAW,CAAC;gBAC1C,KAAK,sCAAwB,CAAC,UAAU;oBACtC,SAAS,CAAC,cAAc,GAAG;wBACzB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;4BAC7B,0BAA0B,EAAE,CAAC;wBAC/B,CAAC;wBAED,qBAAqB,aAArB,qBAAqB,uBAArB,qBAAqB,EAAI,CAAC;oBAC5B,CAAC,CAAC;oBACF,MAAM;YACV,CAAC;QACH,CAAC;QAED,IAAI,oBAAoB,aAApB,oBAAoB,uBAApB,oBAAoB,CAAE,MAAM,EAAE,CAAC;YACjC,aAAa,CAAC,WAAW,CAAC,oBAAoB,EAAE;gBAC9C,GAAG,eAAe;gBAClB,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ;aAC9B,CAAC,CAAC;QACL,CAAC;QAED,0EAA0E;QAC1E,KAAK,MAAM,IAAI,IAAI,CAAC,GAAG,KAAK,EAAE,GAAG,CAAC,oBAAoB,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;YAC/D,IAAI,CAAC,oCAAoC,CAAC,GAAG,CAAC,IAAI,EAAE,SAAU,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAES,2BAA2B,CAAC,IAAY,EAAE,SAAiB;QACnE,IAAI,CAAC,kBAAkB,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE;YAC1C,IAAI,EAAE,SAAS;YACf,kGAAkG;YAClG,KAAK,EAAE,GAAG;YACV,mGAAmG;YACnG,2DAA2D;YAC3D,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ;SAC9B,CAAC,CAAC;IACL,CAAC;IAEO,YAAY;QAClB,OAAO,MAAM,GAAG,CAAC,4BAA4B,CAAC,WAAW,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;IAC1E,CAAC;IAEO,aAAa,CACnB,iBAAyB,EACzB,YAAsC,EACtC,cAAuB;QAEvB,gEAAgE;QAChE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;QACxE,iBAAiB,GAAG,QAAQ,CAAC;QAC7B,cAAc,GAAG,KAAK,IAAI,cAAc,CAAC;QAEzC,MAAM,UAAU,GACd,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACpD,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,kBAAkB,iBAAiB,kBAAkB,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,SAAS,GAAyC,UAAU,CAAC,IAAI,CACnE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc,KAAK,cAAc,CAC3C,CAAC;QACF,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;gBACjC,MAAM,IAAI,KAAK,CACb,kBAAkB,iBAAiB,iBAAiB,cAAc,mBAAmB,CACtF,CAAC;YACJ,CAAC;YACD,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,kBAAkB,iBAAiB,2CAA2C,CAAC,CAAC;YAClG,CAAC;YACD,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;QAED,IAAI,SAAS,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CACb,kBAAkB,iBAAiB,iBAAiB,sCAAwB,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG;gBAC7F,sCAAsC,sCAAwB,CAAC,YAAY,CAAC,IAAI,CACnF,CAAC;QACJ,CAAC;QAED,OAAO,SAAc,CAAC;IACxB,CAAC;IAEO,qBAAqB,CAC3B,aAAwC,EACxC,IAA4B,EAC5B,SAAiB,EACjB,OAAe;QAEf,6FAA6F;QAC7F,MAAM,gBAAgB,GAAW,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;QACvE,MAAM,WAAW,GACf,UAAU,aAAa,CAAC,YAAY,EAAE;YACtC,8DAA8D;YAC9D,GAAG,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,gBAAgB,WAAW,CAAC;QAE/D,sCAAsC;QACtC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;QACpC,MAAM,IAAI,uDAA0B,CAAC,SAAS,EAAE,GAAG,WAAW,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACtG,CAAC;;AA75BH,oEA85BC;AA75BgB,wCAAW,GAAW,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport * as argparse from 'argparse';\n\nimport type {\n  ICommandLineChoiceDefinition,\n  ICommandLineChoiceListDefinition,\n  ICommandLineIntegerDefinition,\n  ICommandLineIntegerListDefinition,\n  ICommandLineFlagDefinition,\n  ICommandLineStringDefinition,\n  ICommandLineStringListDefinition,\n  ICommandLineRemainderDefinition\n} from '../parameters/CommandLineDefinition';\nimport type { ICommandLineParserOptions } from './CommandLineParser';\nimport {\n  type CommandLineParameterBase,\n  type CommandLineParameterWithArgument,\n  CommandLineParameterKind,\n  type CommandLineParameter\n} from '../parameters/BaseClasses';\nimport {\n  CommandLineChoiceParameter,\n  type IRequiredCommandLineChoiceParameter\n} from '../parameters/CommandLineChoiceParameter';\nimport { CommandLineChoiceListParameter } from '../parameters/CommandLineChoiceListParameter';\nimport {\n  CommandLineIntegerParameter,\n  type IRequiredCommandLineIntegerParameter\n} from '../parameters/CommandLineIntegerParameter';\nimport { CommandLineIntegerListParameter } from '../parameters/CommandLineIntegerListParameter';\nimport { CommandLineFlagParameter } from '../parameters/CommandLineFlagParameter';\nimport {\n  CommandLineStringParameter,\n  type IRequiredCommandLineStringParameter\n} from '../parameters/CommandLineStringParameter';\nimport { CommandLineStringListParameter } from '../parameters/CommandLineStringListParameter';\nimport { CommandLineRemainder } from '../parameters/CommandLineRemainder';\nimport { SCOPING_PARAMETER_GROUP } from '../Constants';\nimport { CommandLineParserExitError } from './CommandLineParserExitError';\n\n/**\n * The result containing the parsed parameter long name and scope. Returned when calling\n * {@link CommandLineParameterProvider.parseScopedLongName}.\n *\n * @public\n */\nexport interface IScopedLongNameParseResult {\n  /**\n   * The long name parsed from the scoped long name, e.g. \"--my-scope:my-parameter\" -\\> \"--my-parameter\"\n   */\n  longName: string;\n\n  /**\n   * The scope parsed from the scoped long name or undefined if no scope was found,\n   * e.g. \"--my-scope:my-parameter\" -\\> \"my-scope\"\n   */\n  scope: string | undefined;\n}\n\n/**\n * An object containing the state of the\n *\n * @internal\n */\nexport interface IRegisterDefinedParametersState {\n  /**\n   * A set of all defined parameter names registered by parent {@link CommandLineParameterProvider}\n   * objects.\n   */\n  parentParameterNames: Set<string>;\n}\n\n/**\n * This is the argparse result data object\n * @internal\n */\nexport interface ICommandLineParserData {\n  action: string;\n  aliasAction?: string;\n  aliasDocumentation?: string;\n  [key: string]: any; // eslint-disable-line @typescript-eslint/no-explicit-any\n}\n\nconst SCOPE_GROUP_NAME: string = 'scope';\nconst LONG_NAME_GROUP_NAME: string = 'longName';\nconst POSSIBLY_SCOPED_LONG_NAME_REGEXP: RegExp =\n  /^--((?<scope>[a-z0-9]+(-[a-z0-9]+)*):)?(?<longName>[a-z0-9]+((-[a-z0-9]+)+)?)$/;\n\ninterface IExtendedArgumentGroup extends argparse.ArgumentGroup {\n  // The types are incorrect - this function returns the constructed argument\n  // object, which looks like the argument options type.\n  addArgument(nameOrFlags: string | string[], options: argparse.ArgumentOptions): argparse.ArgumentOptions;\n}\n\ninterface IExtendedArgumentParser extends argparse.ArgumentParser {\n  // This function throws\n  error(message: string): never;\n}\n\n/**\n * This is the common base class for CommandLineAction and CommandLineParser\n * that provides functionality for defining command-line parameters.\n *\n * @public\n */\nexport abstract class CommandLineParameterProvider {\n  private static _keyCounter: number = 0;\n\n  /** @internal */\n  public readonly _ambiguousParameterParserKeysByName: Map<string, string>;\n  /** @internal */\n  protected readonly _registeredParameterParserKeysByName: Map<string, string>;\n\n  private readonly _parameters: CommandLineParameter[];\n  private readonly _parametersByLongName: Map<string, CommandLineParameter[]>;\n  private readonly _parametersByShortName: Map<string, CommandLineParameter[]>;\n  private readonly _parameterGroupsByName: Map<\n    string | typeof SCOPING_PARAMETER_GROUP,\n    argparse.ArgumentGroup\n  >;\n  private _parametersHaveBeenRegistered: boolean;\n  private _parametersHaveBeenProcessed: boolean;\n  private _remainder: CommandLineRemainder | undefined;\n\n  /** @internal */\n  // Third party code should not inherit subclasses or call this constructor\n  public constructor() {\n    this._parameters = [];\n    this._parametersByLongName = new Map();\n    this._parametersByShortName = new Map();\n    this._parameterGroupsByName = new Map();\n    this._ambiguousParameterParserKeysByName = new Map();\n    this._registeredParameterParserKeysByName = new Map();\n    this._parametersHaveBeenRegistered = false;\n    this._parametersHaveBeenProcessed = false;\n  }\n\n  /**\n   * Returns a collection of the parameters that were defined for this object.\n   */\n  public get parameters(): ReadonlyArray<CommandLineParameter> {\n    return this._parameters;\n  }\n\n  /**\n   * Informs the caller if the argparse data has been processed into parameters.\n   */\n  public get parametersProcessed(): boolean {\n    return this._parametersHaveBeenProcessed;\n  }\n\n  /**\n   * If {@link CommandLineParameterProvider.defineCommandLineRemainder} was called,\n   * this object captures any remaining command line arguments after the recognized portion.\n   */\n  public get remainder(): CommandLineRemainder | undefined {\n    return this._remainder;\n  }\n\n  /**\n   * Defines a command-line parameter whose value must be a string from a fixed set of\n   * allowable choices (similar to an enum).\n   *\n   * @remarks\n   * Example of a choice parameter:\n   * ```\n   * example-tool --log-level warn\n   * ```\n   */\n  public defineChoiceParameter<TChoice extends string = string>(\n    definition: ICommandLineChoiceDefinition<TChoice> & {\n      required: false | undefined;\n      defaultValue: undefined;\n    }\n  ): CommandLineChoiceParameter<TChoice>;\n  /**\n   * {@inheritdoc CommandLineParameterProvider.(defineChoiceParameter:1)}\n   */\n  public defineChoiceParameter<TChoice extends string = string>(\n    definition: ICommandLineChoiceDefinition<TChoice> & { required: true }\n  ): IRequiredCommandLineChoiceParameter<TChoice>;\n  /**\n   * {@inheritdoc CommandLineParameterProvider.(defineChoiceParameter:1)}\n   */\n  public defineChoiceParameter<TChoice extends string = string>(\n    definition: ICommandLineChoiceDefinition<TChoice> & { defaultValue: TChoice }\n  ): IRequiredCommandLineChoiceParameter<TChoice>;\n  /**\n   * {@inheritdoc CommandLineParameterProvider.(defineChoiceParameter:1)}\n   */\n  public defineChoiceParameter<TChoice extends string = string>(\n    definition: ICommandLineChoiceDefinition<TChoice>\n  ): CommandLineChoiceParameter<TChoice>;\n  public defineChoiceParameter<TChoice extends string = string>(\n    definition: ICommandLineChoiceDefinition<TChoice>\n  ): CommandLineChoiceParameter<TChoice> {\n    const parameter: CommandLineChoiceParameter<TChoice> = new CommandLineChoiceParameter(definition);\n    this._defineParameter(parameter);\n    return parameter;\n  }\n\n  /**\n   * Returns the CommandLineChoiceParameter with the specified long name.\n   * @remarks\n   * This method throws an exception if the parameter is not defined.\n   */\n  public getChoiceParameter(parameterLongName: string, parameterScope?: string): CommandLineChoiceParameter {\n    return this._getParameter(parameterLongName, CommandLineParameterKind.Choice, parameterScope);\n  }\n\n  /**\n   * Defines a command-line parameter whose value must be a string from a fixed set of\n   * allowable choices (similar to an enum). The parameter can be specified multiple times to\n   * build a list.\n   *\n   * @remarks\n   * Example of a choice list parameter:\n   * ```\n   * example-tool --allow-color red --allow-color green\n   * ```\n   */\n  public defineChoiceListParameter<TChoice extends string = string>(\n    definition: ICommandLineChoiceListDefinition<TChoice>\n  ): CommandLineChoiceListParameter<TChoice> {\n    const parameter: CommandLineChoiceListParameter<TChoice> = new CommandLineChoiceListParameter(definition);\n    this._defineParameter(parameter);\n    return parameter;\n  }\n\n  /**\n   * Returns the CommandLineChoiceListParameter with the specified long name.\n   * @remarks\n   * This method throws an exception if the parameter is not defined.\n   */\n  public getChoiceListParameter(\n    parameterLongName: string,\n    parameterScope?: string\n  ): CommandLineChoiceListParameter {\n    return this._getParameter(parameterLongName, CommandLineParameterKind.ChoiceList, parameterScope);\n  }\n\n  /**\n   * Defines a command-line switch whose boolean value is true if the switch is provided,\n   * and false otherwise.\n   *\n   * @remarks\n   * Example usage of a flag parameter:\n   * ```\n   * example-tool --debug\n   * ```\n   */\n  public defineFlagParameter(definition: ICommandLineFlagDefinition): CommandLineFlagParameter {\n    const parameter: CommandLineFlagParameter = new CommandLineFlagParameter(definition);\n    this._defineParameter(parameter);\n    return parameter;\n  }\n\n  /**\n   * Returns the CommandLineFlagParameter with the specified long name.\n   * @remarks\n   * This method throws an exception if the parameter is not defined.\n   */\n  public getFlagParameter(parameterLongName: string, parameterScope?: string): CommandLineFlagParameter {\n    return this._getParameter(parameterLongName, CommandLineParameterKind.Flag, parameterScope);\n  }\n\n  /**\n   * Defines a command-line parameter whose argument is an integer.\n   *\n   * @remarks\n   * Example usage of an integer parameter:\n   * ```\n   * example-tool --max-attempts 5\n   * ```\n   */\n  public defineIntegerParameter(\n    definition: ICommandLineIntegerDefinition & { required: false | undefined; defaultValue: undefined }\n  ): CommandLineIntegerParameter;\n  /**\n   * {@inheritdoc CommandLineParameterProvider.(defineIntegerParameter:1)}\n   */\n  public defineIntegerParameter(\n    definition: ICommandLineIntegerDefinition & { required: true }\n  ): IRequiredCommandLineIntegerParameter;\n  /**\n   * {@inheritdoc CommandLineParameterProvider.(defineIntegerParameter:1)}\n   */\n  public defineIntegerParameter(\n    definition: ICommandLineIntegerDefinition & { defaultValue: number }\n  ): IRequiredCommandLineIntegerParameter;\n  /**\n   * {@inheritdoc CommandLineParameterProvider.(defineIntegerParameter:1)}\n   */\n  public defineIntegerParameter(definition: ICommandLineIntegerDefinition): CommandLineIntegerParameter;\n  public defineIntegerParameter(definition: ICommandLineIntegerDefinition): CommandLineIntegerParameter {\n    const parameter: CommandLineIntegerParameter = new CommandLineIntegerParameter(definition);\n    this._defineParameter(parameter);\n    return parameter;\n  }\n\n  /**\n   * Returns the CommandLineIntegerParameter with the specified long name.\n   * @remarks\n   * This method throws an exception if the parameter is not defined.\n   */\n  public getIntegerParameter(\n    parameterLongName: string,\n    parameterScope?: string\n  ): CommandLineIntegerParameter {\n    return this._getParameter(parameterLongName, CommandLineParameterKind.Integer, parameterScope);\n  }\n\n  /**\n   * Defines a command-line parameter whose argument is an integer. The parameter can be specified\n   * multiple times to build a list.\n   *\n   * @remarks\n   * Example usage of an integer list parameter:\n   * ```\n   * example-tool --avoid 4 --avoid 13\n   * ```\n   */\n  public defineIntegerListParameter(\n    definition: ICommandLineIntegerListDefinition\n  ): CommandLineIntegerListParameter {\n    const parameter: CommandLineIntegerListParameter = new CommandLineIntegerListParameter(definition);\n    this._defineParameter(parameter);\n    return parameter;\n  }\n\n  /**\n   * Returns the CommandLineIntegerParameter with the specified long name.\n   * @remarks\n   * This method throws an exception if the parameter is not defined.\n   */\n  public getIntegerListParameter(\n    parameterLongName: string,\n    parameterScope?: string\n  ): CommandLineIntegerListParameter {\n    return this._getParameter(parameterLongName, CommandLineParameterKind.IntegerList, parameterScope);\n  }\n\n  /**\n   * Defines a command-line parameter whose argument is a single text string.\n   *\n   * @remarks\n   * Example usage of a string parameter:\n   * ```\n   * example-tool --message \"Hello, world!\"\n   * ```\n   */\n  public defineStringParameter(\n    definition: ICommandLineStringDefinition & { required: false | undefined; defaultValue: undefined }\n  ): CommandLineStringParameter;\n  /**\n   * {@inheritdoc CommandLineParameterProvider.(defineStringParameter:1)}\n   */\n  public defineStringParameter(\n    definition: ICommandLineStringDefinition & { required: true }\n  ): IRequiredCommandLineStringParameter;\n  /**\n   * {@inheritdoc CommandLineParameterProvider.(defineStringParameter:1)}\n   */\n  public defineStringParameter(\n    definition: ICommandLineStringDefinition & { defaultValue: string }\n  ): IRequiredCommandLineStringParameter;\n  /**\n   * {@inheritdoc CommandLineParameterProvider.(defineStringParameter:1)}\n   */\n  public defineStringParameter(definition: ICommandLineStringDefinition): CommandLineStringParameter;\n  public defineStringParameter(definition: ICommandLineStringDefinition): CommandLineStringParameter {\n    const parameter: CommandLineStringParameter = new CommandLineStringParameter(definition);\n    this._defineParameter(parameter);\n    return parameter;\n  }\n\n  /**\n   * Returns the CommandLineStringParameter with the specified long name.\n   * @remarks\n   * This method throws an exception if the parameter is not defined.\n   */\n  public getStringParameter(parameterLongName: string, parameterScope?: string): CommandLineStringParameter {\n    return this._getParameter(parameterLongName, CommandLineParameterKind.String, parameterScope);\n  }\n\n  /**\n   * Defines a command-line parameter whose argument is a single text string.  The parameter can be\n   * specified multiple times to build a list.\n   *\n   * @remarks\n   * Example usage of a string list parameter:\n   * ```\n   * example-tool --add file1.txt --add file2.txt --add file3.txt\n   * ```\n   */\n  public defineStringListParameter(\n    definition: ICommandLineStringListDefinition\n  ): CommandLineStringListParameter {\n    const parameter: CommandLineStringListParameter = new CommandLineStringListParameter(definition);\n    this._defineParameter(parameter);\n    return parameter;\n  }\n\n  /**\n   * Defines a rule that captures any remaining command line arguments after the recognized portion.\n   *\n   * @remarks\n   * This feature is useful for commands that pass their arguments along to an external tool, relying on\n   * that tool to perform validation.  (It could also be used to parse parameters without any validation\n   * or documentation, but that is not recommended.)\n   *\n   * Example of capturing the remainder after an optional flag parameter.\n   * ```\n   * example-tool --my-flag this is the remainder\n   * ```\n   *\n   * In the \"--help\" documentation, the remainder rule will be represented as \"...\".\n   */\n  public defineCommandLineRemainder(definition: ICommandLineRemainderDefinition): CommandLineRemainder {\n    if (this._remainder) {\n      throw new Error('defineRemainingArguments() has already been called for this provider');\n    }\n    this._remainder = new CommandLineRemainder(definition);\n    return this._remainder;\n  }\n\n  /**\n   * Returns the CommandLineStringListParameter with the specified long name.\n   * @remarks\n   * This method throws an exception if the parameter is not defined.\n   */\n  public getStringListParameter(\n    parameterLongName: string,\n    parameterScope?: string\n  ): CommandLineStringListParameter {\n    return this._getParameter(parameterLongName, CommandLineParameterKind.StringList, parameterScope);\n  }\n\n  /**\n   * Generates the command-line help text.\n   */\n  public renderHelpText(): string {\n    const initialState: IRegisterDefinedParametersState = {\n      parentParameterNames: new Set()\n    };\n    this._registerDefinedParameters(initialState);\n    return this._getArgumentParser().formatHelp();\n  }\n\n  /**\n   * Generates the command-line usage text.\n   */\n  public renderUsageText(): string {\n    const initialState: IRegisterDefinedParametersState = {\n      parentParameterNames: new Set()\n    };\n    this._registerDefinedParameters(initialState);\n    return this._getArgumentParser().formatUsage();\n  }\n\n  /**\n   * Returns a object which maps the long name of each parameter in this.parameters\n   * to the stringified form of its value. This is useful for logging telemetry, but\n   * it is not the proper way of accessing parameters or their values.\n   */\n  public getParameterStringMap(): Record<string, string> {\n    const parameterMap: Record<string, string> = {};\n    for (const parameter of this.parameters) {\n      const parameterName: string = parameter.scopedLongName || parameter.longName;\n      switch (parameter.kind) {\n        case CommandLineParameterKind.Flag:\n        case CommandLineParameterKind.Choice:\n        case CommandLineParameterKind.String:\n        case CommandLineParameterKind.Integer:\n          parameterMap[parameterName] = JSON.stringify(\n            (\n              parameter as\n                | CommandLineFlagParameter\n                | CommandLineIntegerParameter\n                | CommandLineChoiceParameter\n                | CommandLineStringParameter\n            ).value\n          );\n          break;\n        case CommandLineParameterKind.StringList:\n        case CommandLineParameterKind.IntegerList:\n        case CommandLineParameterKind.ChoiceList:\n          const arrayValue: ReadonlyArray<string | number> | undefined = (\n            parameter as\n              | CommandLineIntegerListParameter\n              | CommandLineStringListParameter\n              | CommandLineChoiceListParameter\n          ).values;\n          parameterMap[parameterName] = arrayValue ? arrayValue.join(',') : '';\n          break;\n      }\n    }\n    return parameterMap;\n  }\n\n  /**\n   * Returns an object with the parsed scope (if present) and the long name of the parameter.\n   */\n  public parseScopedLongName(scopedLongName: string): IScopedLongNameParseResult {\n    const result: RegExpExecArray | null = POSSIBLY_SCOPED_LONG_NAME_REGEXP.exec(scopedLongName);\n    if (!result || !result.groups) {\n      throw new Error(`The parameter long name \"${scopedLongName}\" is not valid.`);\n    }\n    return {\n      longName: `--${result.groups[LONG_NAME_GROUP_NAME]}`,\n      scope: result.groups[SCOPE_GROUP_NAME]\n    };\n  }\n\n  /** @internal */\n  public _registerDefinedParameters(state: IRegisterDefinedParametersState): void {\n    if (this._parametersHaveBeenRegistered) {\n      // We prevent new parameters from being defined after the first call to _registerDefinedParameters,\n      // so we can already ensure that all parameters were registered.\n      return;\n    }\n\n    // First, loop through all parameters with short names. If there are any duplicates, disable the short names\n    // since we can't prefix scopes to short names in order to deduplicate them. The duplicate short names will\n    // be reported as errors if the user attempts to use them.\n    const parametersWithDuplicateShortNames: Set<CommandLineParameterBase> = new Set();\n    for (const [shortName, shortNameParameters] of this._parametersByShortName.entries()) {\n      if (shortNameParameters.length > 1) {\n        for (const parameter of shortNameParameters) {\n          this._defineAmbiguousParameter(shortName);\n          parametersWithDuplicateShortNames.add(parameter);\n        }\n      }\n    }\n\n    // Then, loop through all parameters and register them. If there are any duplicates, ensure that they have\n    // provided a scope and register them with the scope. The duplicate long names will be reported as an error\n    // if the user attempts to use them.\n    for (const longNameParameters of this._parametersByLongName.values()) {\n      const useScopedLongName: boolean = longNameParameters.length > 1;\n      for (const parameter of longNameParameters) {\n        if (useScopedLongName) {\n          if (!parameter.parameterScope) {\n            throw new Error(\n              `The parameter \"${parameter.longName}\" is defined multiple times with the same long name. ` +\n                'Parameters with the same long name must define a scope.'\n            );\n          }\n          this._defineAmbiguousParameter(parameter.longName);\n        }\n\n        const ignoreShortName: boolean = parametersWithDuplicateShortNames.has(parameter);\n        this._registerParameter(parameter, useScopedLongName, ignoreShortName);\n      }\n    }\n\n    // Register the existing parameters as ambiguous parameters. These are generally provided by the\n    // parent action.\n    const { parentParameterNames } = state;\n    for (const parentParameterName of parentParameterNames) {\n      this._defineAmbiguousParameter(parentParameterName);\n    }\n\n    // We also need to loop through the defined ambiguous parameters and register them. These will be reported\n    // as errors if the user attempts to use them.\n    for (const [ambiguousParameterName, parserKey] of this._ambiguousParameterParserKeysByName) {\n      // Only register the ambiguous parameter if it hasn't already been registered. We will still handle these\n      // already-registered parameters as ambiguous, but by avoiding registering again, we will defer errors\n      // until the user actually attempts to use the parameter.\n      if (!this._registeredParameterParserKeysByName.has(ambiguousParameterName)) {\n        this._registerAmbiguousParameter(ambiguousParameterName, parserKey);\n      }\n    }\n\n    // Need to add the remainder parameter last\n    if (this._remainder) {\n      const argparseOptions: argparse.ArgumentOptions = {\n        help: this._remainder.description,\n        nargs: argparse.Const.REMAINDER,\n        metavar: '\"...\"'\n      };\n\n      this._getArgumentParser().addArgument(argparse.Const.REMAINDER, argparseOptions);\n    }\n\n    this._parametersHaveBeenRegistered = true;\n  }\n\n  /**\n   * Retrieves the argparse object.\n   * @internal\n   */\n  protected abstract _getArgumentParser(): argparse.ArgumentParser;\n\n  /**\n   * This is called internally by {@link CommandLineParser.executeAsync}\n   * @internal\n   */\n  public _preParse(): void {\n    for (const parameter of this._parameters) {\n      parameter._preParse?.();\n    }\n  }\n\n  /**\n   * This is called internally by {@link CommandLineParser.executeAsync} before `printUsage` is called\n   * @internal\n   */\n  public _postParse(): void {\n    for (const parameter of this._parameters) {\n      parameter._postParse?.();\n    }\n  }\n\n  /**\n   * This is called internally by {@link CommandLineParser.executeAsync}\n   * @internal\n   */\n  public _processParsedData(parserOptions: ICommandLineParserOptions, data: ICommandLineParserData): void {\n    if (!this._parametersHaveBeenRegistered) {\n      throw new Error('Parameters have not been registered');\n    }\n\n    if (this._parametersHaveBeenProcessed) {\n      throw new Error('Command Line Parser Data was already processed');\n    }\n\n    // Search for any ambiguous parameters and throw an error if any are found\n    for (const [parameterName, parserKey] of this._ambiguousParameterParserKeysByName) {\n      if (data[parserKey]) {\n        // When the parser key matches the actually registered parameter, we know that this is an ambiguous\n        // parameter sourced from the parent action or tool\n        if (this._registeredParameterParserKeysByName.get(parameterName) === parserKey) {\n          this._throwParserExitError(parserOptions, data, 1, `Ambiguous option: \"${parameterName}\".`);\n        }\n\n        // Determine if the ambiguous parameter is a short name or a long name, since the process of finding\n        // the non-ambiguous name is different for each.\n        const duplicateShortNameParameters: CommandLineParameterBase[] | undefined =\n          this._parametersByShortName.get(parameterName);\n        if (duplicateShortNameParameters) {\n          // We also need to make sure we get the non-ambiguous long name for the parameter, since it is\n          // possible for that the long name is ambiguous as well.\n          const nonAmbiguousLongNames: string[] = [];\n          for (const parameter of duplicateShortNameParameters) {\n            const matchingLongNameParameters: CommandLineParameterBase[] | undefined =\n              this._parametersByLongName.get(parameter.longName);\n            if (!matchingLongNameParameters?.length) {\n              // This should never happen\n              throw new Error(\n                `Unable to find long name parameters for ambiguous short name parameter \"${parameterName}\".`\n              );\n            }\n            // If there is more than one matching long name parameter, then we know that we need to use the\n            // scoped long name for the parameter. The scoped long name should always be provided.\n            if (matchingLongNameParameters.length > 1) {\n              if (!parameter.scopedLongName) {\n                // This should never happen\n                throw new Error(\n                  `Unable to find scoped long name for ambiguous short name parameter \"${parameterName}\".`\n                );\n              }\n              nonAmbiguousLongNames.push(parameter.scopedLongName);\n            } else {\n              nonAmbiguousLongNames.push(parameter.longName);\n            }\n          }\n\n          // Throw an error including the non-ambiguous long names for the parameters that have the ambiguous\n          // short name, ex.\n          // Error: Ambiguous option \"-p\" could match \"--param1\", \"--param2\"\n          this._throwParserExitError(\n            parserOptions,\n            data,\n            1,\n            `Ambiguous option: \"${parameterName}\" could match ${nonAmbiguousLongNames.join(', ')}.`\n          );\n        }\n\n        const duplicateLongNameParameters: CommandLineParameterBase[] | undefined =\n          this._parametersByLongName.get(parameterName);\n        if (duplicateLongNameParameters) {\n          const nonAmbiguousLongNames: string[] = duplicateLongNameParameters.map(\n            (p: CommandLineParameterBase) => {\n              // The scoped long name should always be provided\n              if (!p.scopedLongName) {\n                // This should never happen\n                throw new Error(\n                  `Unable to find scoped long name for ambiguous long name parameter \"${parameterName}\".`\n                );\n              }\n              return p.scopedLongName;\n            }\n          );\n\n          // Throw an error including the non-ambiguous scoped long names for the parameters that have the\n          // ambiguous long name, ex.\n          // Error: Ambiguous option: \"--param\" could match --scope1:param, --scope2:param\n          this._throwParserExitError(\n            parserOptions,\n            data,\n            1,\n            `Ambiguous option: \"${parameterName}\" could match ${nonAmbiguousLongNames.join(', ')}.`\n          );\n        }\n\n        // This shouldn't happen, but we also shouldn't allow the user to use the ambiguous parameter\n        this._throwParserExitError(parserOptions, data, 1, `Ambiguous option: \"${parameterName}\".`);\n      }\n    }\n\n    // Fill in the values for the parameters\n    for (const parameter of this._parameters) {\n      const value: unknown = data[parameter._parserKey!];\n      parameter._setValue(value);\n      parameter._validateValue?.();\n    }\n\n    if (this.remainder) {\n      this.remainder._setValue(data[argparse.Const.REMAINDER]);\n    }\n\n    this._parametersHaveBeenProcessed = true;\n  }\n\n  /** @internal */\n  protected _defineParameter(parameter: CommandLineParameter): void {\n    if (this._parametersHaveBeenRegistered) {\n      throw new Error('Parameters have already been registered for this provider');\n    }\n\n    // Generate and set the parser key at definition time\n    parameter._parserKey = this._generateKey();\n\n    this._parameters.push(parameter);\n\n    // Collect all parameters with the same long name. We will perform conflict resolution at registration.\n    let longNameParameters: CommandLineParameter[] | undefined = this._parametersByLongName.get(\n      parameter.longName\n    );\n    if (!longNameParameters) {\n      longNameParameters = [];\n      this._parametersByLongName.set(parameter.longName, longNameParameters);\n    }\n    longNameParameters.push(parameter);\n\n    // Collect all parameters with the same short name. We will perform conflict resolution at registration.\n    if (parameter.shortName) {\n      let shortNameParameters: CommandLineParameter[] | undefined = this._parametersByShortName.get(\n        parameter.shortName\n      );\n      if (!shortNameParameters) {\n        shortNameParameters = [];\n        this._parametersByShortName.set(parameter.shortName, shortNameParameters);\n      }\n      shortNameParameters.push(parameter);\n    }\n  }\n\n  /** @internal */\n  protected _defineAmbiguousParameter(name: string): string {\n    if (this._parametersHaveBeenRegistered) {\n      throw new Error('Parameters have already been registered for this provider');\n    }\n\n    // Only generate a new parser key if the ambiguous parameter hasn't been defined yet,\n    // either as an existing parameter or as another ambiguous parameter\n    let existingParserKey: string | undefined =\n      this._registeredParameterParserKeysByName.get(name) ||\n      this._ambiguousParameterParserKeysByName.get(name);\n    if (!existingParserKey) {\n      existingParserKey = this._generateKey();\n    }\n\n    this._ambiguousParameterParserKeysByName.set(name, existingParserKey);\n    return existingParserKey;\n  }\n\n  /** @internal */\n  protected _registerParameter(\n    parameter: CommandLineParameter,\n    useScopedLongName: boolean,\n    ignoreShortName: boolean\n  ): void {\n    const {\n      shortName,\n      longName,\n      scopedLongName,\n      description,\n      kind,\n      required,\n      environmentVariable,\n      parameterGroup,\n      undocumentedSynonyms,\n      _parserKey: parserKey\n    } = parameter;\n\n    const names: string[] = [];\n    if (shortName && !ignoreShortName) {\n      names.push(shortName);\n    }\n\n    // Use the original long name unless otherwise requested\n    if (!useScopedLongName) {\n      names.push(longName);\n    }\n\n    // Add the scoped long name if it exists\n    if (scopedLongName) {\n      names.push(scopedLongName);\n    }\n\n    let finalDescription: string = description;\n\n    const supplementaryNotes: string[] = [];\n    parameter._getSupplementaryNotes(supplementaryNotes);\n    if (supplementaryNotes.length > 0) {\n      // If they left the period off the end of their sentence, then add one.\n      if (finalDescription.match(/[a-z0-9]\"?\\s*$/i)) {\n        finalDescription = finalDescription.trimEnd() + '.';\n      }\n      // Append the supplementary text\n      finalDescription += ' ' + supplementaryNotes.join(' ');\n    }\n\n    let choices: string[] | undefined;\n    let action: string | undefined;\n    let type: string | undefined;\n    switch (kind) {\n      case CommandLineParameterKind.Choice: {\n        choices = Array.from(parameter.alternatives);\n        break;\n      }\n      case CommandLineParameterKind.ChoiceList: {\n        choices = Array.from(parameter.alternatives);\n        action = 'append';\n        break;\n      }\n      case CommandLineParameterKind.Flag:\n        action = 'storeTrue';\n        break;\n      case CommandLineParameterKind.Integer:\n        type = 'int';\n        break;\n      case CommandLineParameterKind.IntegerList:\n        type = 'int';\n        action = 'append';\n        break;\n      case CommandLineParameterKind.String:\n        break;\n      case CommandLineParameterKind.StringList:\n        action = 'append';\n        break;\n    }\n\n    // NOTE: Our \"environmentVariable\" feature takes precedence over argparse's \"defaultValue\",\n    // so we have to reimplement that feature.\n    const argparseOptions: argparse.ArgumentOptions = {\n      help: finalDescription,\n      dest: parserKey,\n      metavar: (parameter as CommandLineParameterWithArgument).argumentName,\n      required,\n      choices,\n      action,\n      type\n    };\n\n    const argumentParser: IExtendedArgumentParser = this._getArgumentParser() as IExtendedArgumentParser;\n    let argumentGroup: argparse.ArgumentGroup | undefined;\n    if (parameterGroup) {\n      argumentGroup = this._parameterGroupsByName.get(parameterGroup);\n      if (!argumentGroup) {\n        let parameterGroupName: string;\n        if (typeof parameterGroup === 'string') {\n          parameterGroupName = parameterGroup;\n        } else if (parameterGroup === SCOPING_PARAMETER_GROUP) {\n          parameterGroupName = 'scoping';\n        } else {\n          throw new Error('Unexpected parameter group: ' + parameterGroup);\n        }\n\n        argumentGroup = argumentParser.addArgumentGroup({\n          title: `Optional ${parameterGroupName} arguments`\n        });\n        this._parameterGroupsByName.set(parameterGroup, argumentGroup);\n      }\n    } else {\n      argumentGroup = argumentParser;\n    }\n\n    const argparseArgument: argparse.ArgumentOptions = (argumentGroup as IExtendedArgumentGroup).addArgument(\n      names,\n      argparseOptions\n    );\n    if (required && environmentVariable) {\n      // Add some special-cased logic to handle required parameters with environment variables\n\n      const originalPreParse: (() => void) | undefined = parameter._preParse?.bind(parameter);\n      parameter._preParse = () => {\n        originalPreParse?.();\n        // Set the value as non-required before parsing. We'll validate it explicitly\n        argparseArgument.required = false;\n      };\n\n      const originalPostParse: (() => void) | undefined = parameter._postParse?.bind(parameter);\n      parameter._postParse = () => {\n        // Reset the required value to make the usage text correct\n        argparseArgument.required = true;\n        originalPostParse?.();\n      };\n\n      function throwMissingParameterError(): never {\n        argumentParser.error(`Argument \"${longName}\" is required`);\n      }\n\n      const originalValidateValue: (() => void) | undefined = parameter._validateValue?.bind(parameter);\n      // For these values, we have to perform explicit validation because they're requested\n      // as required, but we disabled argparse's required flag to allow the environment variable\n      // to potentially fill the value.\n      switch (kind) {\n        case CommandLineParameterKind.Choice:\n        case CommandLineParameterKind.Integer:\n        case CommandLineParameterKind.String:\n          parameter._validateValue = function () {\n            if (this.value === undefined || this.value === null) {\n              throwMissingParameterError();\n            }\n\n            originalValidateValue?.();\n          };\n          break;\n        case CommandLineParameterKind.ChoiceList:\n        case CommandLineParameterKind.IntegerList:\n        case CommandLineParameterKind.StringList:\n          parameter._validateValue = function () {\n            if (this.values.length === 0) {\n              throwMissingParameterError();\n            }\n\n            originalValidateValue?.();\n          };\n          break;\n      }\n    }\n\n    if (undocumentedSynonyms?.length) {\n      argumentGroup.addArgument(undocumentedSynonyms, {\n        ...argparseOptions,\n        help: argparse.Const.SUPPRESS\n      });\n    }\n\n    // Register the parameter names so that we can detect ambiguous parameters\n    for (const name of [...names, ...(undocumentedSynonyms || [])]) {\n      this._registeredParameterParserKeysByName.set(name, parserKey!);\n    }\n  }\n\n  protected _registerAmbiguousParameter(name: string, parserKey: string): void {\n    this._getArgumentParser().addArgument(name, {\n      dest: parserKey,\n      // We don't know if this argument takes parameters or not, so we need to accept any number of args\n      nargs: '*',\n      // Ensure that the argument is not shown in the help text, since these parameters are only included\n      // to inform the user that ambiguous parameters are present\n      help: argparse.Const.SUPPRESS\n    });\n  }\n\n  private _generateKey(): string {\n    return 'key_' + (CommandLineParameterProvider._keyCounter++).toString();\n  }\n\n  private _getParameter<T extends CommandLineParameterBase>(\n    parameterLongName: string,\n    expectedKind: CommandLineParameterKind,\n    parameterScope?: string\n  ): T {\n    // Support the parameter long name being prefixed with the scope\n    const { scope, longName } = this.parseScopedLongName(parameterLongName);\n    parameterLongName = longName;\n    parameterScope = scope || parameterScope;\n\n    const parameters: CommandLineParameterBase[] | undefined =\n      this._parametersByLongName.get(parameterLongName);\n    if (!parameters) {\n      throw new Error(`The parameter \"${parameterLongName}\" is not defined`);\n    }\n\n    let parameter: CommandLineParameterBase | undefined = parameters.find(\n      (p) => p.parameterScope === parameterScope\n    );\n    if (!parameter) {\n      if (parameterScope !== undefined) {\n        throw new Error(\n          `The parameter \"${parameterLongName}\" with scope \"${parameterScope}\" is not defined.`\n        );\n      }\n      if (parameters.length !== 1) {\n        throw new Error(`The parameter \"${parameterLongName}\" is ambiguous. You must specify a scope.`);\n      }\n      parameter = parameters[0];\n    }\n\n    if (parameter.kind !== expectedKind) {\n      throw new Error(\n        `The parameter \"${parameterLongName}\" is of type \"${CommandLineParameterKind[parameter.kind]}\"` +\n          ` whereas the caller was expecting \"${CommandLineParameterKind[expectedKind]}\".`\n      );\n    }\n\n    return parameter as T;\n  }\n\n  private _throwParserExitError(\n    parserOptions: ICommandLineParserOptions,\n    data: ICommandLineParserData,\n    errorCode: number,\n    message: string\n  ): never {\n    // Write out the usage text to make it easier for the user to find the correct parameter name\n    const targetActionName: string = data.aliasAction || data.action || '';\n    const errorPrefix: string =\n      `Error: ${parserOptions.toolFilename}` +\n      // Handle aliases, actions, and actionless parameter providers\n      `${targetActionName ? ' ' : ''}${targetActionName}: error: `;\n\n    // eslint-disable-next-line no-console\n    console.log(this.renderUsageText());\n    throw new CommandLineParserExitError(errorCode, `${errorPrefix}${message.trimStart().trimEnd()}\\n`);\n  }\n}\n"]}