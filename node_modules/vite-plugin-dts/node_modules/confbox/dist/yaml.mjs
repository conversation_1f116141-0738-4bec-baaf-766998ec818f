import{s as Ke,g as qe}from"./shared/confbox.DA7CpUDY.mjs";/*! js-yaml 4.1.0 https://github.com/nodeca/js-yaml @license MIT */function oe(e){return typeof e>"u"||e===null}function Ge(e){return typeof e=="object"&&e!==null}function We(e){return Array.isArray(e)?e:oe(e)?[]:[e]}function $e(e,n){var i,l,r,u;if(n)for(u=Object.keys(n),i=0,l=u.length;i<l;i+=1)r=u[i],e[r]=n[r];return e}function Qe(e,n){var i="",l;for(l=0;l<n;l+=1)i+=e;return i}function Ve(e){return e===0&&Number.NEGATIVE_INFINITY===1/e}var Xe=oe,Ze=Ge,ze=We,Je=Qe,en=Ve,nn=$e,y={isNothing:Xe,isObject:Ze,toArray:ze,repeat:Je,isNegativeZero:en,extend:nn};function ue(e,n){var i="",l=e.reason||"(unknown reason)";return e.mark?(e.mark.name&&(i+='in "'+e.mark.name+'" '),i+="("+(e.mark.line+1)+":"+(e.mark.column+1)+")",!n&&e.mark.snippet&&(i+=`

`+e.mark.snippet),l+" "+i):l}function M(e,n){Error.call(this),this.name="YAMLException",this.reason=e,this.mark=n,this.message=ue(this,!1),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack||""}M.prototype=Object.create(Error.prototype),M.prototype.constructor=M,M.prototype.toString=function(n){return this.name+": "+ue(this,n)};var w=M;function $(e,n,i,l,r){var u="",o="",f=Math.floor(r/2)-1;return l-n>f&&(u=" ... ",n=l-f+u.length),i-l>f&&(o=" ...",i=l+f-o.length),{str:u+e.slice(n,i).replace(/\t/g,"\u2192")+o,pos:l-n+u.length}}function Q(e,n){return y.repeat(" ",n-e.length)+e}function rn(e,n){if(n=Object.create(n||null),!e.buffer)return null;n.maxLength||(n.maxLength=79),typeof n.indent!="number"&&(n.indent=1),typeof n.linesBefore!="number"&&(n.linesBefore=3),typeof n.linesAfter!="number"&&(n.linesAfter=2);for(var i=/\r?\n|\r|\0/g,l=[0],r=[],u,o=-1;u=i.exec(e.buffer);)r.push(u.index),l.push(u.index+u[0].length),e.position<=u.index&&o<0&&(o=l.length-2);o<0&&(o=l.length-1);var f="",c,a,t=Math.min(e.line+n.linesAfter,r.length).toString().length,p=n.maxLength-(n.indent+t+3);for(c=1;c<=n.linesBefore&&!(o-c<0);c++)a=$(e.buffer,l[o-c],r[o-c],e.position-(l[o]-l[o-c]),p),f=y.repeat(" ",n.indent)+Q((e.line-c+1).toString(),t)+" | "+a.str+`
`+f;for(a=$(e.buffer,l[o],r[o],e.position,p),f+=y.repeat(" ",n.indent)+Q((e.line+1).toString(),t)+" | "+a.str+`
`,f+=y.repeat("-",n.indent+t+3+a.pos)+`^
`,c=1;c<=n.linesAfter&&!(o+c>=r.length);c++)a=$(e.buffer,l[o+c],r[o+c],e.position-(l[o]-l[o+c]),p),f+=y.repeat(" ",n.indent)+Q((e.line+c+1).toString(),t)+" | "+a.str+`
`;return f.replace(/\n$/,"")}var ln=rn,on=["kind","multi","resolve","construct","instanceOf","predicate","represent","representName","defaultStyle","styleAliases"],un=["scalar","sequence","mapping"];function fn(e){var n={};return e!==null&&Object.keys(e).forEach(function(i){e[i].forEach(function(l){n[String(l)]=i})}),n}function cn(e,n){if(n=n||{},Object.keys(n).forEach(function(i){if(on.indexOf(i)===-1)throw new w('Unknown option "'+i+'" is met in definition of "'+e+'" YAML type.')}),this.options=n,this.tag=e,this.kind=n.kind||null,this.resolve=n.resolve||function(){return!0},this.construct=n.construct||function(i){return i},this.instanceOf=n.instanceOf||null,this.predicate=n.predicate||null,this.represent=n.represent||null,this.representName=n.representName||null,this.defaultStyle=n.defaultStyle||null,this.multi=n.multi||!1,this.styleAliases=fn(n.styleAliases||null),un.indexOf(this.kind)===-1)throw new w('Unknown kind "'+this.kind+'" is specified for "'+e+'" YAML type.')}var C=cn;function fe(e,n){var i=[];return e[n].forEach(function(l){var r=i.length;i.forEach(function(u,o){u.tag===l.tag&&u.kind===l.kind&&u.multi===l.multi&&(r=o)}),i[r]=l}),i}function an(){var e={scalar:{},sequence:{},mapping:{},fallback:{},multi:{scalar:[],sequence:[],mapping:[],fallback:[]}},n,i;function l(r){r.multi?(e.multi[r.kind].push(r),e.multi.fallback.push(r)):e[r.kind][r.tag]=e.fallback[r.tag]=r}for(n=0,i=arguments.length;n<i;n+=1)arguments[n].forEach(l);return e}function V(e){return this.extend(e)}V.prototype.extend=function(n){var i=[],l=[];if(n instanceof C)l.push(n);else if(Array.isArray(n))l=l.concat(n);else if(n&&(Array.isArray(n.implicit)||Array.isArray(n.explicit)))n.implicit&&(i=i.concat(n.implicit)),n.explicit&&(l=l.concat(n.explicit));else throw new w("Schema.extend argument should be a Type, [ Type ], or a schema definition ({ implicit: [...], explicit: [...] })");i.forEach(function(u){if(!(u instanceof C))throw new w("Specified list of YAML types (or a single Type object) contains a non-Type object.");if(u.loadKind&&u.loadKind!=="scalar")throw new w("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.");if(u.multi)throw new w("There is a multi type in the implicit list of a schema. Multi tags can only be listed as explicit.")}),l.forEach(function(u){if(!(u instanceof C))throw new w("Specified list of YAML types (or a single Type object) contains a non-Type object.")});var r=Object.create(V.prototype);return r.implicit=(this.implicit||[]).concat(i),r.explicit=(this.explicit||[]).concat(l),r.compiledImplicit=fe(r,"implicit"),r.compiledExplicit=fe(r,"explicit"),r.compiledTypeMap=an(r.compiledImplicit,r.compiledExplicit),r};var pn=V,tn=new C("tag:yaml.org,2002:str",{kind:"scalar",construct:function(e){return e!==null?e:""}}),hn=new C("tag:yaml.org,2002:seq",{kind:"sequence",construct:function(e){return e!==null?e:[]}}),dn=new C("tag:yaml.org,2002:map",{kind:"mapping",construct:function(e){return e!==null?e:{}}}),sn=new pn({explicit:[tn,hn,dn]});function xn(e){if(e===null)return!0;var n=e.length;return n===1&&e==="~"||n===4&&(e==="null"||e==="Null"||e==="NULL")}function mn(){return null}function gn(e){return e===null}var An=new C("tag:yaml.org,2002:null",{kind:"scalar",resolve:xn,construct:mn,predicate:gn,represent:{canonical:function(){return"~"},lowercase:function(){return"null"},uppercase:function(){return"NULL"},camelcase:function(){return"Null"},empty:function(){return""}},defaultStyle:"lowercase"});function vn(e){if(e===null)return!1;var n=e.length;return n===4&&(e==="true"||e==="True"||e==="TRUE")||n===5&&(e==="false"||e==="False"||e==="FALSE")}function yn(e){return e==="true"||e==="True"||e==="TRUE"}function Cn(e){return Object.prototype.toString.call(e)==="[object Boolean]"}var _n=new C("tag:yaml.org,2002:bool",{kind:"scalar",resolve:vn,construct:yn,predicate:Cn,represent:{lowercase:function(e){return e?"true":"false"},uppercase:function(e){return e?"TRUE":"FALSE"},camelcase:function(e){return e?"True":"False"}},defaultStyle:"lowercase"});function wn(e){return 48<=e&&e<=57||65<=e&&e<=70||97<=e&&e<=102}function Fn(e){return 48<=e&&e<=55}function bn(e){return 48<=e&&e<=57}function Sn(e){if(e===null)return!1;var n=e.length,i=0,l=!1,r;if(!n)return!1;if(r=e[i],(r==="-"||r==="+")&&(r=e[++i]),r==="0"){if(i+1===n)return!0;if(r=e[++i],r==="b"){for(i++;i<n;i++)if(r=e[i],r!=="_"){if(r!=="0"&&r!=="1")return!1;l=!0}return l&&r!=="_"}if(r==="x"){for(i++;i<n;i++)if(r=e[i],r!=="_"){if(!wn(e.charCodeAt(i)))return!1;l=!0}return l&&r!=="_"}if(r==="o"){for(i++;i<n;i++)if(r=e[i],r!=="_"){if(!Fn(e.charCodeAt(i)))return!1;l=!0}return l&&r!=="_"}}if(r==="_")return!1;for(;i<n;i++)if(r=e[i],r!=="_"){if(!bn(e.charCodeAt(i)))return!1;l=!0}return!(!l||r==="_")}function En(e){var n=e,i=1,l;if(n.indexOf("_")!==-1&&(n=n.replace(/_/g,"")),l=n[0],(l==="-"||l==="+")&&(l==="-"&&(i=-1),n=n.slice(1),l=n[0]),n==="0")return 0;if(l==="0"){if(n[1]==="b")return i*parseInt(n.slice(2),2);if(n[1]==="x")return i*parseInt(n.slice(2),16);if(n[1]==="o")return i*parseInt(n.slice(2),8)}return i*parseInt(n,10)}function Tn(e){return Object.prototype.toString.call(e)==="[object Number]"&&e%1===0&&!y.isNegativeZero(e)}var On=new C("tag:yaml.org,2002:int",{kind:"scalar",resolve:Sn,construct:En,predicate:Tn,represent:{binary:function(e){return e>=0?"0b"+e.toString(2):"-0b"+e.toString(2).slice(1)},octal:function(e){return e>=0?"0o"+e.toString(8):"-0o"+e.toString(8).slice(1)},decimal:function(e){return e.toString(10)},hexadecimal:function(e){return e>=0?"0x"+e.toString(16).toUpperCase():"-0x"+e.toString(16).toUpperCase().slice(1)}},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}}),In=new RegExp("^(?:[-+]?(?:[0-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$");function kn(e){return!(e===null||!In.test(e)||e[e.length-1]==="_")}function Ln(e){var n,i;return n=e.replace(/_/g,"").toLowerCase(),i=n[0]==="-"?-1:1,"+-".indexOf(n[0])>=0&&(n=n.slice(1)),n===".inf"?i===1?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:n===".nan"?NaN:i*parseFloat(n,10)}var Nn=/^[-+]?[0-9]+e/;function Rn(e,n){var i;if(isNaN(e))switch(n){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===e)switch(n){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===e)switch(n){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(y.isNegativeZero(e))return"-0.0";return i=e.toString(10),Nn.test(i)?i.replace("e",".e"):i}function Dn(e){return Object.prototype.toString.call(e)==="[object Number]"&&(e%1!==0||y.isNegativeZero(e))}var Mn=new C("tag:yaml.org,2002:float",{kind:"scalar",resolve:kn,construct:Ln,predicate:Dn,represent:Rn,defaultStyle:"lowercase"}),Yn=sn.extend({implicit:[An,_n,On,Mn]}),Bn=Yn,ce=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),ae=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$");function Pn(e){return e===null?!1:ce.exec(e)!==null||ae.exec(e)!==null}function jn(e){var n,i,l,r,u,o,f,c=0,a=null,t,p,d;if(n=ce.exec(e),n===null&&(n=ae.exec(e)),n===null)throw new Error("Date resolve error");if(i=+n[1],l=+n[2]-1,r=+n[3],!n[4])return new Date(Date.UTC(i,l,r));if(u=+n[4],o=+n[5],f=+n[6],n[7]){for(c=n[7].slice(0,3);c.length<3;)c+="0";c=+c}return n[9]&&(t=+n[10],p=+(n[11]||0),a=(t*60+p)*6e4,n[9]==="-"&&(a=-a)),d=new Date(Date.UTC(i,l,r,u,o,f,c)),a&&d.setTime(d.getTime()-a),d}function Hn(e){return e.toISOString()}var Un=new C("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:Pn,construct:jn,instanceOf:Date,represent:Hn});function Kn(e){return e==="<<"||e===null}var qn=new C("tag:yaml.org,2002:merge",{kind:"scalar",resolve:Kn}),X=`ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=
\r`;function Gn(e){if(e===null)return!1;var n,i,l=0,r=e.length,u=X;for(i=0;i<r;i++)if(n=u.indexOf(e.charAt(i)),!(n>64)){if(n<0)return!1;l+=6}return l%8===0}function Wn(e){var n,i,l=e.replace(/[\r\n=]/g,""),r=l.length,u=X,o=0,f=[];for(n=0;n<r;n++)n%4===0&&n&&(f.push(o>>16&255),f.push(o>>8&255),f.push(o&255)),o=o<<6|u.indexOf(l.charAt(n));return i=r%4*6,i===0?(f.push(o>>16&255),f.push(o>>8&255),f.push(o&255)):i===18?(f.push(o>>10&255),f.push(o>>2&255)):i===12&&f.push(o>>4&255),new Uint8Array(f)}function $n(e){var n="",i=0,l,r,u=e.length,o=X;for(l=0;l<u;l++)l%3===0&&l&&(n+=o[i>>18&63],n+=o[i>>12&63],n+=o[i>>6&63],n+=o[i&63]),i=(i<<8)+e[l];return r=u%3,r===0?(n+=o[i>>18&63],n+=o[i>>12&63],n+=o[i>>6&63],n+=o[i&63]):r===2?(n+=o[i>>10&63],n+=o[i>>4&63],n+=o[i<<2&63],n+=o[64]):r===1&&(n+=o[i>>2&63],n+=o[i<<4&63],n+=o[64],n+=o[64]),n}function Qn(e){return Object.prototype.toString.call(e)==="[object Uint8Array]"}var Vn=new C("tag:yaml.org,2002:binary",{kind:"scalar",resolve:Gn,construct:Wn,predicate:Qn,represent:$n}),Xn=Object.prototype.hasOwnProperty,Zn=Object.prototype.toString;function zn(e){if(e===null)return!0;var n=[],i,l,r,u,o,f=e;for(i=0,l=f.length;i<l;i+=1){if(r=f[i],o=!1,Zn.call(r)!=="[object Object]")return!1;for(u in r)if(Xn.call(r,u))if(!o)o=!0;else return!1;if(!o)return!1;if(n.indexOf(u)===-1)n.push(u);else return!1}return!0}function Jn(e){return e!==null?e:[]}var ei=new C("tag:yaml.org,2002:omap",{kind:"sequence",resolve:zn,construct:Jn}),ni=Object.prototype.toString;function ii(e){if(e===null)return!0;var n,i,l,r,u,o=e;for(u=new Array(o.length),n=0,i=o.length;n<i;n+=1){if(l=o[n],ni.call(l)!=="[object Object]"||(r=Object.keys(l),r.length!==1))return!1;u[n]=[r[0],l[r[0]]]}return!0}function ri(e){if(e===null)return[];var n,i,l,r,u,o=e;for(u=new Array(o.length),n=0,i=o.length;n<i;n+=1)l=o[n],r=Object.keys(l),u[n]=[r[0],l[r[0]]];return u}var li=new C("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:ii,construct:ri}),oi=Object.prototype.hasOwnProperty;function ui(e){if(e===null)return!0;var n,i=e;for(n in i)if(oi.call(i,n)&&i[n]!==null)return!1;return!0}function fi(e){return e!==null?e:{}}var ci=new C("tag:yaml.org,2002:set",{kind:"mapping",resolve:ui,construct:fi}),pe=Bn.extend({implicit:[Un,qn],explicit:[Vn,ei,li,ci]}),T=Object.prototype.hasOwnProperty,H=1,te=2,he=3,U=4,Z=1,ai=2,de=3,pi=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,ti=/[\x85\u2028\u2029]/,hi=/[,\[\]\{\}]/,se=/^(?:!|!!|![a-z\-]+!)$/i,xe=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;function me(e){return Object.prototype.toString.call(e)}function S(e){return e===10||e===13}function I(e){return e===9||e===32}function F(e){return e===9||e===32||e===10||e===13}function k(e){return e===44||e===91||e===93||e===123||e===125}function di(e){var n;return 48<=e&&e<=57?e-48:(n=e|32,97<=n&&n<=102?n-97+10:-1)}function si(e){return e===120?2:e===117?4:e===85?8:0}function xi(e){return 48<=e&&e<=57?e-48:-1}function ge(e){return e===48?"\0":e===97?"\x07":e===98?"\b":e===116||e===9?"	":e===110?`
`:e===118?"\v":e===102?"\f":e===114?"\r":e===101?"\x1B":e===32?" ":e===34?'"':e===47?"/":e===92?"\\":e===78?"\x85":e===95?"\xA0":e===76?"\u2028":e===80?"\u2029":""}function mi(e){return e<=65535?String.fromCharCode(e):String.fromCharCode((e-65536>>10)+55296,(e-65536&1023)+56320)}for(var Ae=new Array(256),ve=new Array(256),L=0;L<256;L++)Ae[L]=ge(L)?1:0,ve[L]=ge(L);function gi(e,n){this.input=e,this.filename=n.filename||null,this.schema=n.schema||pe,this.onWarning=n.onWarning||null,this.legacy=n.legacy||!1,this.json=n.json||!1,this.listener=n.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=e.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.firstTabInLine=-1,this.documents=[]}function ye(e,n){var i={name:e.filename,buffer:e.input.slice(0,-1),position:e.position,line:e.line,column:e.position-e.lineStart};return i.snippet=ln(i),new w(n,i)}function h(e,n){throw ye(e,n)}function K(e,n){e.onWarning&&e.onWarning.call(null,ye(e,n))}var Ce={YAML:function(n,i,l){var r,u,o;n.version!==null&&h(n,"duplication of %YAML directive"),l.length!==1&&h(n,"YAML directive accepts exactly one argument"),r=/^([0-9]+)\.([0-9]+)$/.exec(l[0]),r===null&&h(n,"ill-formed argument of the YAML directive"),u=parseInt(r[1],10),o=parseInt(r[2],10),u!==1&&h(n,"unacceptable YAML version of the document"),n.version=l[0],n.checkLineBreaks=o<2,o!==1&&o!==2&&K(n,"unsupported YAML version of the document")},TAG:function(n,i,l){var r,u;l.length!==2&&h(n,"TAG directive accepts exactly two arguments"),r=l[0],u=l[1],se.test(r)||h(n,"ill-formed tag handle (first argument) of the TAG directive"),T.call(n.tagMap,r)&&h(n,'there is a previously declared suffix for "'+r+'" tag handle'),xe.test(u)||h(n,"ill-formed tag prefix (second argument) of the TAG directive");try{u=decodeURIComponent(u)}catch{h(n,"tag prefix is malformed: "+u)}n.tagMap[r]=u}};function O(e,n,i,l){var r,u,o,f;if(n<i){if(f=e.input.slice(n,i),l)for(r=0,u=f.length;r<u;r+=1)o=f.charCodeAt(r),o===9||32<=o&&o<=1114111||h(e,"expected valid JSON character");else pi.test(f)&&h(e,"the stream contains non-printable characters");e.result+=f}}function _e(e,n,i,l){var r,u,o,f;for(y.isObject(i)||h(e,"cannot merge mappings; the provided source object is unacceptable"),r=Object.keys(i),o=0,f=r.length;o<f;o+=1)u=r[o],T.call(n,u)||(n[u]=i[u],l[u]=!0)}function N(e,n,i,l,r,u,o,f,c){var a,t;if(Array.isArray(r))for(r=Array.prototype.slice.call(r),a=0,t=r.length;a<t;a+=1)Array.isArray(r[a])&&h(e,"nested arrays are not supported inside keys"),typeof r=="object"&&me(r[a])==="[object Object]"&&(r[a]="[object Object]");if(typeof r=="object"&&me(r)==="[object Object]"&&(r="[object Object]"),r=String(r),n===null&&(n={}),l==="tag:yaml.org,2002:merge")if(Array.isArray(u))for(a=0,t=u.length;a<t;a+=1)_e(e,n,u[a],i);else _e(e,n,u,i);else!e.json&&!T.call(i,r)&&T.call(n,r)&&(e.line=o||e.line,e.lineStart=f||e.lineStart,e.position=c||e.position,h(e,"duplicated mapping key")),r==="__proto__"?Object.defineProperty(n,r,{configurable:!0,enumerable:!0,writable:!0,value:u}):n[r]=u,delete i[r];return n}function z(e){var n;n=e.input.charCodeAt(e.position),n===10?e.position++:n===13?(e.position++,e.input.charCodeAt(e.position)===10&&e.position++):h(e,"a line break is expected"),e.line+=1,e.lineStart=e.position,e.firstTabInLine=-1}function v(e,n,i){for(var l=0,r=e.input.charCodeAt(e.position);r!==0;){for(;I(r);)r===9&&e.firstTabInLine===-1&&(e.firstTabInLine=e.position),r=e.input.charCodeAt(++e.position);if(n&&r===35)do r=e.input.charCodeAt(++e.position);while(r!==10&&r!==13&&r!==0);if(S(r))for(z(e),r=e.input.charCodeAt(e.position),l++,e.lineIndent=0;r===32;)e.lineIndent++,r=e.input.charCodeAt(++e.position);else break}return i!==-1&&l!==0&&e.lineIndent<i&&K(e,"deficient indentation"),l}function q(e){var n=e.position,i;return i=e.input.charCodeAt(n),!!((i===45||i===46)&&i===e.input.charCodeAt(n+1)&&i===e.input.charCodeAt(n+2)&&(n+=3,i=e.input.charCodeAt(n),i===0||F(i)))}function J(e,n){n===1?e.result+=" ":n>1&&(e.result+=y.repeat(`
`,n-1))}function Ai(e,n,i){var l,r,u,o,f,c,a,t,p=e.kind,d=e.result,s;if(s=e.input.charCodeAt(e.position),F(s)||k(s)||s===35||s===38||s===42||s===33||s===124||s===62||s===39||s===34||s===37||s===64||s===96||(s===63||s===45)&&(r=e.input.charCodeAt(e.position+1),F(r)||i&&k(r)))return!1;for(e.kind="scalar",e.result="",u=o=e.position,f=!1;s!==0;){if(s===58){if(r=e.input.charCodeAt(e.position+1),F(r)||i&&k(r))break}else if(s===35){if(l=e.input.charCodeAt(e.position-1),F(l))break}else{if(e.position===e.lineStart&&q(e)||i&&k(s))break;if(S(s))if(c=e.line,a=e.lineStart,t=e.lineIndent,v(e,!1,-1),e.lineIndent>=n){f=!0,s=e.input.charCodeAt(e.position);continue}else{e.position=o,e.line=c,e.lineStart=a,e.lineIndent=t;break}}f&&(O(e,u,o,!1),J(e,e.line-c),u=o=e.position,f=!1),I(s)||(o=e.position+1),s=e.input.charCodeAt(++e.position)}return O(e,u,o,!1),e.result?!0:(e.kind=p,e.result=d,!1)}function vi(e,n){var i,l,r;if(i=e.input.charCodeAt(e.position),i!==39)return!1;for(e.kind="scalar",e.result="",e.position++,l=r=e.position;(i=e.input.charCodeAt(e.position))!==0;)if(i===39)if(O(e,l,e.position,!0),i=e.input.charCodeAt(++e.position),i===39)l=e.position,e.position++,r=e.position;else return!0;else S(i)?(O(e,l,r,!0),J(e,v(e,!1,n)),l=r=e.position):e.position===e.lineStart&&q(e)?h(e,"unexpected end of the document within a single quoted scalar"):(e.position++,r=e.position);h(e,"unexpected end of the stream within a single quoted scalar")}function yi(e,n){var i,l,r,u,o,f;if(f=e.input.charCodeAt(e.position),f!==34)return!1;for(e.kind="scalar",e.result="",e.position++,i=l=e.position;(f=e.input.charCodeAt(e.position))!==0;){if(f===34)return O(e,i,e.position,!0),e.position++,!0;if(f===92){if(O(e,i,e.position,!0),f=e.input.charCodeAt(++e.position),S(f))v(e,!1,n);else if(f<256&&Ae[f])e.result+=ve[f],e.position++;else if((o=si(f))>0){for(r=o,u=0;r>0;r--)f=e.input.charCodeAt(++e.position),(o=di(f))>=0?u=(u<<4)+o:h(e,"expected hexadecimal character");e.result+=mi(u),e.position++}else h(e,"unknown escape sequence");i=l=e.position}else S(f)?(O(e,i,l,!0),J(e,v(e,!1,n)),i=l=e.position):e.position===e.lineStart&&q(e)?h(e,"unexpected end of the document within a double quoted scalar"):(e.position++,l=e.position)}h(e,"unexpected end of the stream within a double quoted scalar")}function Ci(e,n){var i=!0,l,r,u,o=e.tag,f,c=e.anchor,a,t,p,d,s,x=Object.create(null),g,A,b,m;if(m=e.input.charCodeAt(e.position),m===91)t=93,s=!1,f=[];else if(m===123)t=125,s=!0,f={};else return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=f),m=e.input.charCodeAt(++e.position);m!==0;){if(v(e,!0,n),m=e.input.charCodeAt(e.position),m===t)return e.position++,e.tag=o,e.anchor=c,e.kind=s?"mapping":"sequence",e.result=f,!0;i?m===44&&h(e,"expected the node content, but found ','"):h(e,"missed comma between flow collection entries"),A=g=b=null,p=d=!1,m===63&&(a=e.input.charCodeAt(e.position+1),F(a)&&(p=d=!0,e.position++,v(e,!0,n))),l=e.line,r=e.lineStart,u=e.position,R(e,n,H,!1,!0),A=e.tag,g=e.result,v(e,!0,n),m=e.input.charCodeAt(e.position),(d||e.line===l)&&m===58&&(p=!0,m=e.input.charCodeAt(++e.position),v(e,!0,n),R(e,n,H,!1,!0),b=e.result),s?N(e,f,x,A,g,b,l,r,u):p?f.push(N(e,null,x,A,g,b,l,r,u)):f.push(g),v(e,!0,n),m=e.input.charCodeAt(e.position),m===44?(i=!0,m=e.input.charCodeAt(++e.position)):i=!1}h(e,"unexpected end of the stream within a flow collection")}function _i(e,n){var i,l,r=Z,u=!1,o=!1,f=n,c=0,a=!1,t,p;if(p=e.input.charCodeAt(e.position),p===124)l=!1;else if(p===62)l=!0;else return!1;for(e.kind="scalar",e.result="";p!==0;)if(p=e.input.charCodeAt(++e.position),p===43||p===45)Z===r?r=p===43?de:ai:h(e,"repeat of a chomping mode identifier");else if((t=xi(p))>=0)t===0?h(e,"bad explicit indentation width of a block scalar; it cannot be less than one"):o?h(e,"repeat of an indentation width identifier"):(f=n+t-1,o=!0);else break;if(I(p)){do p=e.input.charCodeAt(++e.position);while(I(p));if(p===35)do p=e.input.charCodeAt(++e.position);while(!S(p)&&p!==0)}for(;p!==0;){for(z(e),e.lineIndent=0,p=e.input.charCodeAt(e.position);(!o||e.lineIndent<f)&&p===32;)e.lineIndent++,p=e.input.charCodeAt(++e.position);if(!o&&e.lineIndent>f&&(f=e.lineIndent),S(p)){c++;continue}if(e.lineIndent<f){r===de?e.result+=y.repeat(`
`,u?1+c:c):r===Z&&u&&(e.result+=`
`);break}for(l?I(p)?(a=!0,e.result+=y.repeat(`
`,u?1+c:c)):a?(a=!1,e.result+=y.repeat(`
`,c+1)):c===0?u&&(e.result+=" "):e.result+=y.repeat(`
`,c):e.result+=y.repeat(`
`,u?1+c:c),u=!0,o=!0,c=0,i=e.position;!S(p)&&p!==0;)p=e.input.charCodeAt(++e.position);O(e,i,e.position,!1)}return!0}function we(e,n){var i,l=e.tag,r=e.anchor,u=[],o,f=!1,c;if(e.firstTabInLine!==-1)return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=u),c=e.input.charCodeAt(e.position);c!==0&&(e.firstTabInLine!==-1&&(e.position=e.firstTabInLine,h(e,"tab characters must not be used in indentation")),!(c!==45||(o=e.input.charCodeAt(e.position+1),!F(o))));){if(f=!0,e.position++,v(e,!0,-1)&&e.lineIndent<=n){u.push(null),c=e.input.charCodeAt(e.position);continue}if(i=e.line,R(e,n,he,!1,!0),u.push(e.result),v(e,!0,-1),c=e.input.charCodeAt(e.position),(e.line===i||e.lineIndent>n)&&c!==0)h(e,"bad indentation of a sequence entry");else if(e.lineIndent<n)break}return f?(e.tag=l,e.anchor=r,e.kind="sequence",e.result=u,!0):!1}function wi(e,n,i){var l,r,u,o,f,c,a=e.tag,t=e.anchor,p={},d=Object.create(null),s=null,x=null,g=null,A=!1,b=!1,m;if(e.firstTabInLine!==-1)return!1;for(e.anchor!==null&&(e.anchorMap[e.anchor]=p),m=e.input.charCodeAt(e.position);m!==0;){if(!A&&e.firstTabInLine!==-1&&(e.position=e.firstTabInLine,h(e,"tab characters must not be used in indentation")),l=e.input.charCodeAt(e.position+1),u=e.line,(m===63||m===58)&&F(l))m===63?(A&&(N(e,p,d,s,x,null,o,f,c),s=x=g=null),b=!0,A=!0,r=!0):A?(A=!1,r=!0):h(e,"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line"),e.position+=1,m=l;else{if(o=e.line,f=e.lineStart,c=e.position,!R(e,i,te,!1,!0))break;if(e.line===u){for(m=e.input.charCodeAt(e.position);I(m);)m=e.input.charCodeAt(++e.position);if(m===58)m=e.input.charCodeAt(++e.position),F(m)||h(e,"a whitespace character is expected after the key-value separator within a block mapping"),A&&(N(e,p,d,s,x,null,o,f,c),s=x=g=null),b=!0,A=!1,r=!1,s=e.tag,x=e.result;else if(b)h(e,"can not read an implicit mapping pair; a colon is missed");else return e.tag=a,e.anchor=t,!0}else if(b)h(e,"can not read a block mapping entry; a multiline key may not be an implicit key");else return e.tag=a,e.anchor=t,!0}if((e.line===u||e.lineIndent>n)&&(A&&(o=e.line,f=e.lineStart,c=e.position),R(e,n,U,!0,r)&&(A?x=e.result:g=e.result),A||(N(e,p,d,s,x,g,o,f,c),s=x=g=null),v(e,!0,-1),m=e.input.charCodeAt(e.position)),(e.line===u||e.lineIndent>n)&&m!==0)h(e,"bad indentation of a mapping entry");else if(e.lineIndent<n)break}return A&&N(e,p,d,s,x,null,o,f,c),b&&(e.tag=a,e.anchor=t,e.kind="mapping",e.result=p),b}function Fi(e){var n,i=!1,l=!1,r,u,o;if(o=e.input.charCodeAt(e.position),o!==33)return!1;if(e.tag!==null&&h(e,"duplication of a tag property"),o=e.input.charCodeAt(++e.position),o===60?(i=!0,o=e.input.charCodeAt(++e.position)):o===33?(l=!0,r="!!",o=e.input.charCodeAt(++e.position)):r="!",n=e.position,i){do o=e.input.charCodeAt(++e.position);while(o!==0&&o!==62);e.position<e.length?(u=e.input.slice(n,e.position),o=e.input.charCodeAt(++e.position)):h(e,"unexpected end of the stream within a verbatim tag")}else{for(;o!==0&&!F(o);)o===33&&(l?h(e,"tag suffix cannot contain exclamation marks"):(r=e.input.slice(n-1,e.position+1),se.test(r)||h(e,"named tag handle cannot contain such characters"),l=!0,n=e.position+1)),o=e.input.charCodeAt(++e.position);u=e.input.slice(n,e.position),hi.test(u)&&h(e,"tag suffix cannot contain flow indicator characters")}u&&!xe.test(u)&&h(e,"tag name cannot contain such characters: "+u);try{u=decodeURIComponent(u)}catch{h(e,"tag name is malformed: "+u)}return i?e.tag=u:T.call(e.tagMap,r)?e.tag=e.tagMap[r]+u:r==="!"?e.tag="!"+u:r==="!!"?e.tag="tag:yaml.org,2002:"+u:h(e,'undeclared tag handle "'+r+'"'),!0}function bi(e){var n,i;if(i=e.input.charCodeAt(e.position),i!==38)return!1;for(e.anchor!==null&&h(e,"duplication of an anchor property"),i=e.input.charCodeAt(++e.position),n=e.position;i!==0&&!F(i)&&!k(i);)i=e.input.charCodeAt(++e.position);return e.position===n&&h(e,"name of an anchor node must contain at least one character"),e.anchor=e.input.slice(n,e.position),!0}function Si(e){var n,i,l;if(l=e.input.charCodeAt(e.position),l!==42)return!1;for(l=e.input.charCodeAt(++e.position),n=e.position;l!==0&&!F(l)&&!k(l);)l=e.input.charCodeAt(++e.position);return e.position===n&&h(e,"name of an alias node must contain at least one character"),i=e.input.slice(n,e.position),T.call(e.anchorMap,i)||h(e,'unidentified alias "'+i+'"'),e.result=e.anchorMap[i],v(e,!0,-1),!0}function R(e,n,i,l,r){var u,o,f,c=1,a=!1,t=!1,p,d,s,x,g,A;if(e.listener!==null&&e.listener("open",e),e.tag=null,e.anchor=null,e.kind=null,e.result=null,u=o=f=U===i||he===i,l&&v(e,!0,-1)&&(a=!0,e.lineIndent>n?c=1:e.lineIndent===n?c=0:e.lineIndent<n&&(c=-1)),c===1)for(;Fi(e)||bi(e);)v(e,!0,-1)?(a=!0,f=u,e.lineIndent>n?c=1:e.lineIndent===n?c=0:e.lineIndent<n&&(c=-1)):f=!1;if(f&&(f=a||r),(c===1||U===i)&&(H===i||te===i?g=n:g=n+1,A=e.position-e.lineStart,c===1?f&&(we(e,A)||wi(e,A,g))||Ci(e,g)?t=!0:(o&&_i(e,g)||vi(e,g)||yi(e,g)?t=!0:Si(e)?(t=!0,(e.tag!==null||e.anchor!==null)&&h(e,"alias node should not have any properties")):Ai(e,g,H===i)&&(t=!0,e.tag===null&&(e.tag="?")),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):c===0&&(t=f&&we(e,A))),e.tag===null)e.anchor!==null&&(e.anchorMap[e.anchor]=e.result);else if(e.tag==="?"){for(e.result!==null&&e.kind!=="scalar"&&h(e,'unacceptable node kind for !<?> tag; it should be "scalar", not "'+e.kind+'"'),p=0,d=e.implicitTypes.length;p<d;p+=1)if(x=e.implicitTypes[p],x.resolve(e.result)){e.result=x.construct(e.result),e.tag=x.tag,e.anchor!==null&&(e.anchorMap[e.anchor]=e.result);break}}else if(e.tag!=="!"){if(T.call(e.typeMap[e.kind||"fallback"],e.tag))x=e.typeMap[e.kind||"fallback"][e.tag];else for(x=null,s=e.typeMap.multi[e.kind||"fallback"],p=0,d=s.length;p<d;p+=1)if(e.tag.slice(0,s[p].tag.length)===s[p].tag){x=s[p];break}x||h(e,"unknown tag !<"+e.tag+">"),e.result!==null&&x.kind!==e.kind&&h(e,"unacceptable node kind for !<"+e.tag+'> tag; it should be "'+x.kind+'", not "'+e.kind+'"'),x.resolve(e.result,e.tag)?(e.result=x.construct(e.result,e.tag),e.anchor!==null&&(e.anchorMap[e.anchor]=e.result)):h(e,"cannot resolve a node with !<"+e.tag+"> explicit tag")}return e.listener!==null&&e.listener("close",e),e.tag!==null||e.anchor!==null||t}function Ei(e){var n=e.position,i,l,r,u=!1,o;for(e.version=null,e.checkLineBreaks=e.legacy,e.tagMap=Object.create(null),e.anchorMap=Object.create(null);(o=e.input.charCodeAt(e.position))!==0&&(v(e,!0,-1),o=e.input.charCodeAt(e.position),!(e.lineIndent>0||o!==37));){for(u=!0,o=e.input.charCodeAt(++e.position),i=e.position;o!==0&&!F(o);)o=e.input.charCodeAt(++e.position);for(l=e.input.slice(i,e.position),r=[],l.length<1&&h(e,"directive name must not be less than one character in length");o!==0;){for(;I(o);)o=e.input.charCodeAt(++e.position);if(o===35){do o=e.input.charCodeAt(++e.position);while(o!==0&&!S(o));break}if(S(o))break;for(i=e.position;o!==0&&!F(o);)o=e.input.charCodeAt(++e.position);r.push(e.input.slice(i,e.position))}o!==0&&z(e),T.call(Ce,l)?Ce[l](e,l,r):K(e,'unknown document directive "'+l+'"')}if(v(e,!0,-1),e.lineIndent===0&&e.input.charCodeAt(e.position)===45&&e.input.charCodeAt(e.position+1)===45&&e.input.charCodeAt(e.position+2)===45?(e.position+=3,v(e,!0,-1)):u&&h(e,"directives end mark is expected"),R(e,e.lineIndent-1,U,!1,!0),v(e,!0,-1),e.checkLineBreaks&&ti.test(e.input.slice(n,e.position))&&K(e,"non-ASCII line breaks are interpreted as content"),e.documents.push(e.result),e.position===e.lineStart&&q(e)){e.input.charCodeAt(e.position)===46&&(e.position+=3,v(e,!0,-1));return}if(e.position<e.length-1)h(e,"end of the stream or a document separator is expected");else return}function Ti(e,n){e=String(e),n=n||{},e.length!==0&&(e.charCodeAt(e.length-1)!==10&&e.charCodeAt(e.length-1)!==13&&(e+=`
`),e.charCodeAt(0)===65279&&(e=e.slice(1)));var i=new gi(e,n),l=e.indexOf("\0");for(l!==-1&&(i.position=l,h(i,"null byte is not allowed in input")),i.input+="\0";i.input.charCodeAt(i.position)===32;)i.lineIndent+=1,i.position+=1;for(;i.position<i.length-1;)Ei(i);return i.documents}function Oi(e,n){var i=Ti(e,n);if(i.length!==0){if(i.length===1)return i[0];throw new w("expected a single document in the stream, but found more")}}var Ii=Oi,ki={load:Ii},Fe=Object.prototype.toString,be=Object.prototype.hasOwnProperty,ee=65279,Li=9,Y=10,Ni=13,Ri=32,Di=33,Mi=34,ne=35,Yi=37,Bi=38,Pi=39,ji=42,Se=44,Hi=45,G=58,Ui=61,Ki=62,qi=63,Gi=64,Ee=91,Te=93,Wi=96,Oe=123,$i=124,Ie=125,_={};_[0]="\\0",_[7]="\\a",_[8]="\\b",_[9]="\\t",_[10]="\\n",_[11]="\\v",_[12]="\\f",_[13]="\\r",_[27]="\\e",_[34]='\\"',_[92]="\\\\",_[133]="\\N",_[160]="\\_",_[8232]="\\L",_[8233]="\\P";var Qi=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"],Vi=/^[-+]?[0-9_]+(?::[0-9_]+)+(?:\.[0-9_]*)?$/;function Xi(e,n){var i,l,r,u,o,f,c;if(n===null)return{};for(i={},l=Object.keys(n),r=0,u=l.length;r<u;r+=1)o=l[r],f=String(n[o]),o.slice(0,2)==="!!"&&(o="tag:yaml.org,2002:"+o.slice(2)),c=e.compiledTypeMap.fallback[o],c&&be.call(c.styleAliases,f)&&(f=c.styleAliases[f]),i[o]=f;return i}function Zi(e){var n,i,l;if(n=e.toString(16).toUpperCase(),e<=255)i="x",l=2;else if(e<=65535)i="u",l=4;else if(e<=4294967295)i="U",l=8;else throw new w("code point within a string may not be greater than 0xFFFFFFFF");return"\\"+i+y.repeat("0",l-n.length)+n}var zi=1,B=2;function Ji(e){this.schema=e.schema||pe,this.indent=Math.max(1,e.indent||2),this.noArrayIndent=e.noArrayIndent||!1,this.skipInvalid=e.skipInvalid||!1,this.flowLevel=y.isNothing(e.flowLevel)?-1:e.flowLevel,this.styleMap=Xi(this.schema,e.styles||null),this.sortKeys=e.sortKeys||!1,this.lineWidth=e.lineWidth||80,this.noRefs=e.noRefs||!1,this.noCompatMode=e.noCompatMode||!1,this.condenseFlow=e.condenseFlow||!1,this.quotingType=e.quotingType==='"'?B:zi,this.forceQuotes=e.forceQuotes||!1,this.replacer=typeof e.replacer=="function"?e.replacer:null,this.implicitTypes=this.schema.compiledImplicit,this.explicitTypes=this.schema.compiledExplicit,this.tag=null,this.result="",this.duplicates=[],this.usedDuplicates=null}function ke(e,n){for(var i=y.repeat(" ",n),l=0,r=-1,u="",o,f=e.length;l<f;)r=e.indexOf(`
`,l),r===-1?(o=e.slice(l),l=f):(o=e.slice(l,r+1),l=r+1),o.length&&o!==`
`&&(u+=i),u+=o;return u}function ie(e,n){return`
`+y.repeat(" ",e.indent*n)}function er(e,n){var i,l,r;for(i=0,l=e.implicitTypes.length;i<l;i+=1)if(r=e.implicitTypes[i],r.resolve(n))return!0;return!1}function W(e){return e===Ri||e===Li}function P(e){return 32<=e&&e<=126||161<=e&&e<=55295&&e!==8232&&e!==8233||57344<=e&&e<=65533&&e!==ee||65536<=e&&e<=1114111}function Le(e){return P(e)&&e!==ee&&e!==Ni&&e!==Y}function Ne(e,n,i){var l=Le(e),r=l&&!W(e);return(i?l:l&&e!==Se&&e!==Ee&&e!==Te&&e!==Oe&&e!==Ie)&&e!==ne&&!(n===G&&!r)||Le(n)&&!W(n)&&e===ne||n===G&&r}function nr(e){return P(e)&&e!==ee&&!W(e)&&e!==Hi&&e!==qi&&e!==G&&e!==Se&&e!==Ee&&e!==Te&&e!==Oe&&e!==Ie&&e!==ne&&e!==Bi&&e!==ji&&e!==Di&&e!==$i&&e!==Ui&&e!==Ki&&e!==Pi&&e!==Mi&&e!==Yi&&e!==Gi&&e!==Wi}function ir(e){return!W(e)&&e!==G}function j(e,n){var i=e.charCodeAt(n),l;return i>=55296&&i<=56319&&n+1<e.length&&(l=e.charCodeAt(n+1),l>=56320&&l<=57343)?(i-55296)*1024+l-56320+65536:i}function Re(e){var n=/^\n* /;return n.test(e)}var De=1,re=2,Me=3,Ye=4,D=5;function rr(e,n,i,l,r,u,o,f){var c,a=0,t=null,p=!1,d=!1,s=l!==-1,x=-1,g=nr(j(e,0))&&ir(j(e,e.length-1));if(n||o)for(c=0;c<e.length;a>=65536?c+=2:c++){if(a=j(e,c),!P(a))return D;g=g&&Ne(a,t,f),t=a}else{for(c=0;c<e.length;a>=65536?c+=2:c++){if(a=j(e,c),a===Y)p=!0,s&&(d=d||c-x-1>l&&e[x+1]!==" ",x=c);else if(!P(a))return D;g=g&&Ne(a,t,f),t=a}d=d||s&&c-x-1>l&&e[x+1]!==" "}return!p&&!d?g&&!o&&!r(e)?De:u===B?D:re:i>9&&Re(e)?D:o?u===B?D:re:d?Ye:Me}function lr(e,n,i,l,r){e.dump=function(){if(n.length===0)return e.quotingType===B?'""':"''";if(!e.noCompatMode&&(Qi.indexOf(n)!==-1||Vi.test(n)))return e.quotingType===B?'"'+n+'"':"'"+n+"'";var u=e.indent*Math.max(1,i),o=e.lineWidth===-1?-1:Math.max(Math.min(e.lineWidth,40),e.lineWidth-u),f=l||e.flowLevel>-1&&i>=e.flowLevel;function c(a){return er(e,a)}switch(rr(n,f,e.indent,o,c,e.quotingType,e.forceQuotes&&!l,r)){case De:return n;case re:return"'"+n.replace(/'/g,"''")+"'";case Me:return"|"+Be(n,e.indent)+Pe(ke(n,u));case Ye:return">"+Be(n,e.indent)+Pe(ke(or(n,o),u));case D:return'"'+ur(n)+'"';default:throw new w("impossible error: invalid scalar style")}}()}function Be(e,n){var i=Re(e)?String(n):"",l=e[e.length-1]===`
`,r=l&&(e[e.length-2]===`
`||e===`
`),u=r?"+":l?"":"-";return i+u+`
`}function Pe(e){return e[e.length-1]===`
`?e.slice(0,-1):e}function or(e,n){for(var i=/(\n+)([^\n]*)/g,l=function(){var a=e.indexOf(`
`);return a=a!==-1?a:e.length,i.lastIndex=a,je(e.slice(0,a),n)}(),r=e[0]===`
`||e[0]===" ",u,o;o=i.exec(e);){var f=o[1],c=o[2];u=c[0]===" ",l+=f+(!r&&!u&&c!==""?`
`:"")+je(c,n),r=u}return l}function je(e,n){if(e===""||e[0]===" ")return e;for(var i=/ [^ ]/g,l,r=0,u,o=0,f=0,c="";l=i.exec(e);)f=l.index,f-r>n&&(u=o>r?o:f,c+=`
`+e.slice(r,u),r=u+1),o=f;return c+=`
`,e.length-r>n&&o>r?c+=e.slice(r,o)+`
`+e.slice(o+1):c+=e.slice(r),c.slice(1)}function ur(e){for(var n="",i=0,l,r=0;r<e.length;i>=65536?r+=2:r++)i=j(e,r),l=_[i],!l&&P(i)?(n+=e[r],i>=65536&&(n+=e[r+1])):n+=l||Zi(i);return n}function fr(e,n,i){var l="",r=e.tag,u,o,f;for(u=0,o=i.length;u<o;u+=1)f=i[u],e.replacer&&(f=e.replacer.call(i,String(u),f)),(E(e,n,f,!1,!1)||typeof f>"u"&&E(e,n,null,!1,!1))&&(l!==""&&(l+=","+(e.condenseFlow?"":" ")),l+=e.dump);e.tag=r,e.dump="["+l+"]"}function He(e,n,i,l){var r="",u=e.tag,o,f,c;for(o=0,f=i.length;o<f;o+=1)c=i[o],e.replacer&&(c=e.replacer.call(i,String(o),c)),(E(e,n+1,c,!0,!0,!1,!0)||typeof c>"u"&&E(e,n+1,null,!0,!0,!1,!0))&&((!l||r!=="")&&(r+=ie(e,n)),e.dump&&Y===e.dump.charCodeAt(0)?r+="-":r+="- ",r+=e.dump);e.tag=u,e.dump=r||"[]"}function cr(e,n,i){var l="",r=e.tag,u=Object.keys(i),o,f,c,a,t;for(o=0,f=u.length;o<f;o+=1)t="",l!==""&&(t+=", "),e.condenseFlow&&(t+='"'),c=u[o],a=i[c],e.replacer&&(a=e.replacer.call(i,c,a)),E(e,n,c,!1,!1)&&(e.dump.length>1024&&(t+="? "),t+=e.dump+(e.condenseFlow?'"':"")+":"+(e.condenseFlow?"":" "),E(e,n,a,!1,!1)&&(t+=e.dump,l+=t));e.tag=r,e.dump="{"+l+"}"}function ar(e,n,i,l){var r="",u=e.tag,o=Object.keys(i),f,c,a,t,p,d;if(e.sortKeys===!0)o.sort();else if(typeof e.sortKeys=="function")o.sort(e.sortKeys);else if(e.sortKeys)throw new w("sortKeys must be a boolean or a function");for(f=0,c=o.length;f<c;f+=1)d="",(!l||r!=="")&&(d+=ie(e,n)),a=o[f],t=i[a],e.replacer&&(t=e.replacer.call(i,a,t)),E(e,n+1,a,!0,!0,!0)&&(p=e.tag!==null&&e.tag!=="?"||e.dump&&e.dump.length>1024,p&&(e.dump&&Y===e.dump.charCodeAt(0)?d+="?":d+="? "),d+=e.dump,p&&(d+=ie(e,n)),E(e,n+1,t,!0,p)&&(e.dump&&Y===e.dump.charCodeAt(0)?d+=":":d+=": ",d+=e.dump,r+=d));e.tag=u,e.dump=r||"{}"}function Ue(e,n,i){var l,r,u,o,f,c;for(r=i?e.explicitTypes:e.implicitTypes,u=0,o=r.length;u<o;u+=1)if(f=r[u],(f.instanceOf||f.predicate)&&(!f.instanceOf||typeof n=="object"&&n instanceof f.instanceOf)&&(!f.predicate||f.predicate(n))){if(i?f.multi&&f.representName?e.tag=f.representName(n):e.tag=f.tag:e.tag="?",f.represent){if(c=e.styleMap[f.tag]||f.defaultStyle,Fe.call(f.represent)==="[object Function]")l=f.represent(n,c);else if(be.call(f.represent,c))l=f.represent[c](n,c);else throw new w("!<"+f.tag+'> tag resolver accepts not "'+c+'" style');e.dump=l}return!0}return!1}function E(e,n,i,l,r,u,o){e.tag=null,e.dump=i,Ue(e,i,!1)||Ue(e,i,!0);var f=Fe.call(e.dump),c=l,a;l&&(l=e.flowLevel<0||e.flowLevel>n);var t=f==="[object Object]"||f==="[object Array]",p,d;if(t&&(p=e.duplicates.indexOf(i),d=p!==-1),(e.tag!==null&&e.tag!=="?"||d||e.indent!==2&&n>0)&&(r=!1),d&&e.usedDuplicates[p])e.dump="*ref_"+p;else{if(t&&d&&!e.usedDuplicates[p]&&(e.usedDuplicates[p]=!0),f==="[object Object]")l&&Object.keys(e.dump).length!==0?(ar(e,n,e.dump,r),d&&(e.dump="&ref_"+p+e.dump)):(cr(e,n,e.dump),d&&(e.dump="&ref_"+p+" "+e.dump));else if(f==="[object Array]")l&&e.dump.length!==0?(e.noArrayIndent&&!o&&n>0?He(e,n-1,e.dump,r):He(e,n,e.dump,r),d&&(e.dump="&ref_"+p+e.dump)):(fr(e,n,e.dump),d&&(e.dump="&ref_"+p+" "+e.dump));else if(f==="[object String]")e.tag!=="?"&&lr(e,e.dump,n,u,c);else{if(f==="[object Undefined]")return!1;if(e.skipInvalid)return!1;throw new w("unacceptable kind of an object to dump "+f)}e.tag!==null&&e.tag!=="?"&&(a=encodeURI(e.tag[0]==="!"?e.tag.slice(1):e.tag).replace(/!/g,"%21"),e.tag[0]==="!"?a="!"+a:a.slice(0,18)==="tag:yaml.org,2002:"?a="!!"+a.slice(18):a="!<"+a+">",e.dump=a+" "+e.dump)}return!0}function pr(e,n){var i=[],l=[],r,u;for(le(e,i,l),r=0,u=l.length;r<u;r+=1)n.duplicates.push(i[l[r]]);n.usedDuplicates=new Array(u)}function le(e,n,i){var l,r,u;if(e!==null&&typeof e=="object")if(r=n.indexOf(e),r!==-1)i.indexOf(r)===-1&&i.push(r);else if(n.push(e),Array.isArray(e))for(r=0,u=e.length;r<u;r+=1)le(e[r],n,i);else for(l=Object.keys(e),r=0,u=l.length;r<u;r+=1)le(e[l[r]],n,i)}function tr(e,n){n=n||{};var i=new Ji(n);i.noRefs||pr(e,i);var l=e;return i.replacer&&(l=i.replacer.call({"":l},"",l)),E(i,0,l,!0,!0)?i.dump+`
`:""}var hr=tr,dr={dump:hr},sr=ki.load,xr=dr.dump;function mr(e,n){const i=sr(e,n);return Ke(e,i,n),i}function gr(e,n){const i=qe(e,{}),l=typeof i.indent=="string"?i.indent.length:i.indent,r=xr(e,{indent:l,...n});return i.whitespace.start+r.trim()+i.whitespace.end}export{mr as parseYAML,gr as stringifyYAML};
