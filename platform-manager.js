#!/usr/bin/env node

/**
 * 🎛️ MCP平台管理器
 * 
 * 统一管理所有MCP服务，与mcp-proxy集成
 */

import { spawn } from 'child_process';
import fs from 'fs/promises';
import path from 'path';
import http from 'http';

class MCPPlatformManager {
  constructor() {
    this.services = new Map();
    this.proxyProcess = null;
    this.webServer = null;
  }

  // 注册服务
  registerService(id, config) {
    this.services.set(id, {
      id,
      ...config,
      process: null,
      status: 'stopped',
      logs: []
    });
    console.log(`📦 已注册服务: ${id}`);
  }

  // 启动服务
  async startService(serviceId) {
    const service = this.services.get(serviceId);
    if (!service) throw new Error(`服务不存在: ${serviceId}`);
    
    if (service.status === 'running') {
      console.log(`⚠️ 服务 ${serviceId} 已在运行`);
      return;
    }

    try {
      const process = spawn(service.command, service.args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: process.cwd()
      });

      service.process = process;
      service.status = 'running';

      // 监听输出
      process.stdout.on('data', (data) => {
        const log = `[${serviceId}] ${data.toString().trim()}`;
        service.logs.push({ time: new Date(), message: log, type: 'stdout' });
        console.log(log);
      });

      process.stderr.on('data', (data) => {
        const log = `[${serviceId}] ERROR: ${data.toString().trim()}`;
        service.logs.push({ time: new Date(), message: log, type: 'stderr' });
        console.error(log);
      });

      process.on('exit', (code) => {
        service.status = 'stopped';
        service.process = null;
        console.log(`📡 服务 ${serviceId} 已退出 (代码: ${code})`);
      });

      console.log(`✅ 服务 ${serviceId} 启动成功`);
    } catch (error) {
      service.status = 'error';
      console.error(`❌ 服务 ${serviceId} 启动失败:`, error.message);
      throw error;
    }
  }

  // 停止服务
  async stopService(serviceId) {
    const service = this.services.get(serviceId);
    if (!service) throw new Error(`服务不存在: ${serviceId}`);
    
    if (service.status !== 'running' || !service.process) {
      console.log(`⚠️ 服务 ${serviceId} 未在运行`);
      return;
    }

    service.process.kill();
    service.status = 'stopped';
    service.process = null;
    console.log(`⏹️ 服务 ${serviceId} 已停止`);
  }

  // 重启服务
  async restartService(serviceId) {
    await this.stopService(serviceId);
    await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
    await this.startService(serviceId);
  }

  // 获取服务状态
  getServiceStatus(serviceId) {
    const service = this.services.get(serviceId);
    if (!service) return null;
    
    return {
      id: service.id,
      name: service.name,
      status: service.status,
      tools: service.tools,
      logs: service.logs.slice(-10) // 最近10条日志
    };
  }

  // 获取所有服务状态
  getAllServicesStatus() {
    const status = {};
    for (const [id, service] of this.services) {
      status[id] = this.getServiceStatus(id);
    }
    return status;
  }

  // 生成mcp-proxy配置
  generateProxyConfig() {
    const runningServices = Array.from(this.services.values())
      .filter(service => service.status === 'running');

    const config = {
      mcpProxy: {
        baseURL: "http://localhost:9090",
        addr: ":9090",
        name: "MCP Platform Proxy",
        version: "1.0.0",
        type: "streamable-http",
        options: {
          logEnabled: true
        }
      },
      mcpServers: {}
    };

    runningServices.forEach(service => {
      config.mcpServers[service.id] = {
        command: service.command,
        args: service.args
      };
    });

    return config;
  }

  // 启动mcp-proxy
  async startProxy() {
    if (this.proxyProcess) {
      console.log('⚠️ 代理服务已在运行');
      return;
    }

    // 生成配置文件
    const config = this.generateProxyConfig();
    await fs.writeFile('proxy-config.json', JSON.stringify(config, null, 2));

    try {
      this.proxyProcess = spawn('go', ['run', './mcp-proxy', '--config', 'proxy-config.json'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: process.cwd()
      });

      this.proxyProcess.stdout.on('data', (data) => {
        console.log(`[PROXY] ${data.toString().trim()}`);
      });

      this.proxyProcess.stderr.on('data', (data) => {
        console.error(`[PROXY] ERROR: ${data.toString().trim()}`);
      });

      this.proxyProcess.on('exit', (code) => {
        this.proxyProcess = null;
        console.log(`📡 代理服务已退出 (代码: ${code})`);
      });

      console.log('🌐 MCP代理服务启动成功');
    } catch (error) {
      console.error('❌ 代理服务启动失败:', error.message);
      throw error;
    }
  }

  // 停止mcp-proxy
  async stopProxy() {
    if (!this.proxyProcess) {
      console.log('⚠️ 代理服务未在运行');
      return;
    }

    this.proxyProcess.kill();
    this.proxyProcess = null;
    console.log('⏹️ 代理服务已停止');
  }

  // 启动Web管理界面
  async startWebInterface(port = 8080) {
    this.webServer = http.createServer(async (req, res) => {
      // 设置CORS
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

      if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
      }

      const url = new URL(req.url, `http://${req.headers.host}`);

      try {
        if (url.pathname === '/api/services') {
          // 获取服务状态
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify(this.getAllServicesStatus()));
          
        } else if (url.pathname === '/api/services/start' && req.method === 'POST') {
          // 启动服务
          const body = await this.readRequestBody(req);
          const { serviceId } = JSON.parse(body);
          await this.startService(serviceId);
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ success: true }));
          
        } else if (url.pathname === '/api/services/stop' && req.method === 'POST') {
          // 停止服务
          const body = await this.readRequestBody(req);
          const { serviceId } = JSON.parse(body);
          await this.stopService(serviceId);
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ success: true }));
          
        } else if (url.pathname === '/api/proxy/config') {
          // 获取代理配置
          const config = this.generateProxyConfig();
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify(config));
          
        } else if (url.pathname === '/api/proxy/start' && req.method === 'POST') {
          // 启动代理
          await this.startProxy();
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ success: true }));
          
        } else if (url.pathname === '/api/proxy/stop' && req.method === 'POST') {
          // 停止代理
          await this.stopProxy();
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ success: true }));
          
        } else {
          res.writeHead(404);
          res.end('Not Found');
        }
      } catch (error) {
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: error.message }));
      }
    });

    this.webServer.listen(port, () => {
      console.log(`🌐 Web管理界面启动: http://localhost:${port}`);
    });
  }

  async readRequestBody(req) {
    return new Promise((resolve, reject) => {
      let body = '';
      req.on('data', chunk => body += chunk.toString());
      req.on('end', () => resolve(body));
      req.on('error', reject);
    });
  }

  // 启动完整平台
  async startPlatform() {
    console.log('🚀 启动MCP平台...');
    
    // 注册所有服务
    this.registerService('universal', {
      name: '通用MCP服务',
      command: 'node',
      args: ['universal-mcp-server.js'],
      tools: ['file-ops__read_file', 'web-tools__http_request']
    });

    this.registerService('database', {
      name: '数据库服务',
      command: 'node',
      args: ['mcp-services-collection.js', 'database'],
      tools: ['create_table', 'insert_data', 'query_data']
    });

    this.registerService('email', {
      name: '邮件服务',
      command: 'node',
      args: ['mcp-services-collection.js', 'email'],
      tools: ['send_email', 'check_inbox']
    });

    // 启动Web界面
    await this.startWebInterface();
    
    console.log('✅ MCP平台启动完成');
    console.log('📋 管理界面: http://localhost:8080');
    console.log('🌐 打开 mcp-platform.html 进行可视化管理');
  }
}

// 启动平台
if (import.meta.url === `file://${process.argv[1]}`) {
  const platform = new MCPPlatformManager();
  
  platform.startPlatform().catch(error => {
    console.error('❌ 平台启动失败:', error);
    process.exit(1);
  });

  // 优雅关闭
  process.on('SIGINT', async () => {
    console.log('\n🛑 正在关闭平台...');
    
    // 停止所有服务
    for (const serviceId of platform.services.keys()) {
      await platform.stopService(serviceId).catch(console.error);
    }
    
    // 停止代理
    await platform.stopProxy().catch(console.error);
    
    // 关闭Web服务器
    if (platform.webServer) {
      platform.webServer.close();
    }
    
    console.log('👋 平台已关闭');
    process.exit(0);
  });
}

export default MCPPlatformManager;
