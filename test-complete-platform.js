#!/usr/bin/env node

/**
 * 🧪 测试完整MCP平台功能
 */

async function testCompletePlatform() {
  console.log('🧪 开始测试完整MCP平台...\n');

  // 1. 测试服务API
  console.log('1️⃣ 测试服务API...');
  try {
    const response = await fetch('http://localhost:8081/api/services', {
      headers: {
        'Authorization': 'Bearer admin_token_placeholder'
      }
    });
    
    if (response.ok) {
      const services = await response.json();
      console.log(`✅ 服务API正常 - 发现 ${services.length} 个服务`);
      
      services.forEach(service => {
        console.log(`   📦 ${service.name}: ${service.status} (${service.tools} 工具)`);
      });
      
      // 验证所有6个服务都存在
      const expectedServices = ['universal', 'database', 'email', 'password', 'analytics', 'weather'];
      const foundServices = services.map(s => s.id);
      const missingServices = expectedServices.filter(id => !foundServices.includes(id));
      
      if (missingServices.length === 0) {
        console.log('✅ 所有6个预期服务都已注册');
      } else {
        console.log(`❌ 缺少服务: ${missingServices.join(', ')}`);
      }
    } else {
      console.log(`❌ 服务API失败: HTTP ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ 服务API错误: ${error.message}`);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // 2. 测试统计API
  console.log('2️⃣ 测试统计API...');
  try {
    const response = await fetch('http://localhost:8081/api/stats', {
      headers: {
        'Authorization': 'Bearer admin_token_placeholder'
      }
    });
    
    if (response.ok) {
      const stats = await response.json();
      console.log('✅ 统计API正常');
      console.log(`   📊 运行中服务: ${stats.services.running}/${stats.services.total}`);
      console.log(`   👥 用户数量: ${stats.users}`);
      console.log(`   📞 今日调用: ${stats.calls.today}`);
      console.log(`   ⏱️ 运行时间: ${Math.floor(stats.uptime / 60)} 分钟`);
    } else {
      console.log(`❌ 统计API失败: HTTP ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ 统计API错误: ${error.message}`);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // 3. 测试服务控制
  console.log('3️⃣ 测试服务控制...');
  try {
    // 测试停止服务
    console.log('   🛑 测试停止服务...');
    const stopResponse = await fetch('http://localhost:8081/api/services/universal/stop', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer admin_token_placeholder',
        'Content-Type': 'application/json'
      }
    });
    
    if (stopResponse.ok) {
      const stopResult = await stopResponse.json();
      console.log(`   ✅ 停止服务成功: ${stopResult.message}`);
    } else {
      console.log(`   ❌ 停止服务失败: HTTP ${stopResponse.status}`);
    }

    // 等待1秒
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 测试启动服务
    console.log('   🚀 测试启动服务...');
    const startResponse = await fetch('http://localhost:8081/api/services/universal/start', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer admin_token_placeholder',
        'Content-Type': 'application/json'
      }
    });
    
    if (startResponse.ok) {
      const startResult = await startResponse.json();
      console.log(`   ✅ 启动服务成功: ${startResult.message}`);
    } else {
      console.log(`   ❌ 启动服务失败: HTTP ${startResponse.status}`);
    }
  } catch (error) {
    console.log(`❌ 服务控制错误: ${error.message}`);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // 4. 测试Web界面
  console.log('4️⃣ 测试Web界面...');
  try {
    const response = await fetch('http://localhost:3001/complete-mcp-platform.html');
    
    if (response.ok) {
      const html = await response.text();
      
      // 检查关键元素
      const checks = [
        { name: '页面标题', pattern: /完整MCP管理平台/ },
        { name: '仪表板菜单', pattern: /data-page="dashboard"/ },
        { name: '服务管理菜单', pattern: /data-page="services"/ },
        { name: '权限管理菜单', pattern: /data-page="permissions"/ },
        { name: '日志菜单', pattern: /data-page="logs"/ },
        { name: 'API转换菜单', pattern: /data-page="api-converter"/ },
        { name: '网络配置菜单', pattern: /data-page="network"/ },
        { name: '系统设置菜单', pattern: /data-page="settings"/ },
        { name: 'JavaScript代码', pattern: /function showPage/ },
        { name: '真实数据加载', pattern: /loadRealServices/ },
        { name: '服务控制功能', pattern: /startService/ }
      ];
      
      console.log('✅ Web界面可访问');
      
      checks.forEach(check => {
        if (check.pattern.test(html)) {
          console.log(`   ✅ ${check.name}: 存在`);
        } else {
          console.log(`   ❌ ${check.name}: 缺失`);
        }
      });
    } else {
      console.log(`❌ Web界面失败: HTTP ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ Web界面错误: ${error.message}`);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // 5. 最终验证
  console.log('5️⃣ 最终验证...');
  try {
    const response = await fetch('http://localhost:8081/api/services', {
      headers: {
        'Authorization': 'Bearer admin_token_placeholder'
      }
    });
    
    if (response.ok) {
      const services = await response.json();
      const runningServices = services.filter(s => s.status === 'running').length;
      const totalTools = services.reduce((sum, s) => sum + s.tools, 0);
      
      console.log('✅ 平台状态验证完成');
      console.log(`   📊 运行中服务: ${runningServices}/${services.length}`);
      console.log(`   🛠️ 总工具数: ${totalTools}`);
      console.log(`   🌐 Web界面: http://localhost:3001/complete-mcp-platform.html`);
      console.log(`   📡 管理API: http://localhost:8081/api`);
      
      if (runningServices > 0 && totalTools > 0) {
        console.log('\n🎉 完整MCP平台测试通过！所有功能正常！');
      } else {
        console.log('\n⚠️ 平台功能可能存在问题，请检查服务状态');
      }
    }
  } catch (error) {
    console.log(`❌ 最终验证错误: ${error.message}`);
  }

  console.log('\n📋 测试总结:');
  console.log('   🌐 访问地址: http://localhost:3001/complete-mcp-platform.html');
  console.log('   📊 所有菜单应该可以点击');
  console.log('   🛠️ 所有服务应该可以启动/停止');
  console.log('   📡 所有数据都是真实的，没有模拟数据');
  console.log('   🎛️ 完整的企业级管理功能');
}

// 运行测试
testCompletePlatform().catch(console.error);
