<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 MCP平台状态检查</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .status-item { margin: 10px 0; padding: 15px; border-radius: 8px; border-left: 4px solid #ccc; }
        .status-success { border-left-color: #28a745; background: #d4edda; }
        .status-error { border-left-color: #dc3545; background: #f8d7da; }
        .status-warning { border-left-color: #ffc107; background: #fff3cd; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; background: #007bff; color: white; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 MCP平台状态检查</h1>
        
        <button class="btn" onclick="checkAllStatus()">🔄 检查所有状态</button>
        
        <div id="statusResults">
            <div class="status-item">
                <h3>📡 后端服务状态</h3>
                <div id="backendStatus">检查中...</div>
            </div>
            
            <div class="status-item">
                <h3>🛠️ 服务管理API</h3>
                <div id="servicesApiStatus">检查中...</div>
            </div>
            
            <div class="status-item">
                <h3>📊 统计API</h3>
                <div id="statsApiStatus">检查中...</div>
            </div>
            
            <div class="status-item">
                <h3>🌐 MCP HTTP端点</h3>
                <div id="mcpHttpStatus">检查中...</div>
            </div>
            
            <div class="status-item">
                <h3>📡 MCP SSE端点</h3>
                <div id="mcpSseStatus">检查中...</div>
            </div>
        </div>
        
        <div class="result" id="detailResults"></div>
    </div>

    <script>
        async function checkAllStatus() {
            const results = [];
            
            // 检查后端服务
            await checkBackendService(results);
            
            // 检查服务管理API
            await checkServicesApi(results);
            
            // 检查统计API
            await checkStatsApi(results);
            
            // 检查MCP HTTP端点
            await checkMcpHttp(results);
            
            // 检查MCP SSE端点
            await checkMcpSse(results);
            
            // 显示详细结果
            document.getElementById('detailResults').innerHTML = results.join('\n');
        }

        async function checkBackendService(results) {
            const statusEl = document.getElementById('backendStatus');
            const parentEl = statusEl.parentElement;
            
            try {
                const response = await fetch('/api/stats', {
                    headers: {
                        'Authorization': 'Bearer admin_token_placeholder'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    statusEl.innerHTML = '✅ 连接成功';
                    parentEl.className = 'status-item status-success';
                    results.push(`[后端服务] ✅ 连接成功 - 状态: ${JSON.stringify(data)}`);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusEl.innerHTML = `❌ 连接失败: ${error.message}`;
                parentEl.className = 'status-item status-error';
                results.push(`[后端服务] ❌ 连接失败: ${error.message}`);
            }
        }

        async function checkServicesApi(results) {
            const statusEl = document.getElementById('servicesApiStatus');
            const parentEl = statusEl.parentElement;
            
            try {
                const response = await fetch('/api/services', {
                    headers: {
                        'Authorization': 'Bearer admin_token_placeholder'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    statusEl.innerHTML = `✅ API可用 (${data.length} 个服务)`;
                    parentEl.className = 'status-item status-success';
                    results.push(`[服务API] ✅ 可用 - 服务数: ${data.length}`);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusEl.innerHTML = `❌ API不可用: ${error.message}`;
                parentEl.className = 'status-item status-error';
                results.push(`[服务API] ❌ 不可用: ${error.message}`);
            }
        }

        async function checkStatsApi(results) {
            const statusEl = document.getElementById('statsApiStatus');
            const parentEl = statusEl.parentElement;
            
            try {
                const response = await fetch('/api/stats', {
                    headers: {
                        'Authorization': 'Bearer admin_token_placeholder'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    statusEl.innerHTML = '✅ 统计API可用';
                    parentEl.className = 'status-item status-success';
                    results.push(`[统计API] ✅ 可用 - 数据: ${JSON.stringify(data)}`);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusEl.innerHTML = `❌ 统计API不可用: ${error.message}`;
                parentEl.className = 'status-item status-error';
                results.push(`[统计API] ❌ 不可用: ${error.message}`);
            }
        }

        async function checkMcpHttp(results) {
            const statusEl = document.getElementById('mcpHttpStatus');
            const parentEl = statusEl.parentElement;
            
            try {
                const response = await fetch('http://localhost:9090/api/status');
                
                if (response.ok) {
                    const data = await response.json();
                    statusEl.innerHTML = '✅ MCP HTTP端点可用';
                    parentEl.className = 'status-item status-success';
                    results.push(`[MCP HTTP] ✅ 可用 - 状态: ${JSON.stringify(data)}`);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusEl.innerHTML = `❌ MCP HTTP端点不可用: ${error.message}`;
                parentEl.className = 'status-item status-error';
                results.push(`[MCP HTTP] ❌ 不可用: ${error.message}`);
            }
        }

        async function checkMcpSse(results) {
            const statusEl = document.getElementById('mcpSseStatus');
            const parentEl = statusEl.parentElement;
            
            try {
                // 简单的连接测试
                const response = await fetch('http://localhost:9090/sse', {
                    method: 'HEAD',
                    headers: {
                        'Authorization': 'Bearer admin_token_placeholder'
                    }
                });
                
                if (response.status === 200 || response.status === 401) {
                    statusEl.innerHTML = '✅ MCP SSE端点可用';
                    parentEl.className = 'status-item status-success';
                    results.push(`[MCP SSE] ✅ 端点可用`);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusEl.innerHTML = `❌ MCP SSE端点不可用: ${error.message}`;
                parentEl.className = 'status-item status-error';
                results.push(`[MCP SSE] ❌ 不可用: ${error.message}`);
            }
        }

        // 页面加载时自动检查
        document.addEventListener('DOMContentLoaded', () => {
            checkAllStatus();
        });
    </script>
</body>
</html>
