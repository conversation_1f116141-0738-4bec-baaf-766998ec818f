import { defineConfig } from 'vite';
import dts from 'vite-plugin-dts';
import { resolve } from 'path';

export default defineConfig({
  plugins: [
    dts({
      insertTypesEntry: true,
      rollupTypes: true,
    }),
  ],
  build: {
    lib: {
      entry: {
        index: resolve(__dirname, 'src/index.ts'),
        transport: resolve(__dirname, 'src/transport/index.ts'),
        protocol: resolve(__dirname, 'src/protocol/index.ts'),
      },
      formats: ['es'],
    },
    rollupOptions: {
      external: ['zod', 'ws'],
    },
  },
  test: {
    environment: 'node',
  },
});
