# @mcp-framework/core

> 🚀 Core framework for building MCP (Model Context Protocol) services with ease

## Features

- **🎯 极简API** - 5分钟上手，直观易用
- **🔧 多传输支持** - stdio、HTTP、SSE统一接口
- **📝 装饰器支持** - 声明式工具、资源、提示定义
- **🛠️ 类型安全** - 完整的TypeScript支持
- **🔍 调试友好** - 详细的错误信息和日志
- **⚡ 高性能** - 优化的消息处理和传输

## Quick Start

### 基础用法

```typescript
import { createMCPService } from '@mcp-framework/core';

// 创建服务
const service = createMCPService('my-service', '1.0.0')
  .description('My awesome MCP service')
  .tool(
    'greet',
    'Greet someone',
    { type: 'object', properties: { name: { type: 'string' } }, required: ['name'] },
    ({ name }) => `Hello, ${name}!`
  )
  .build();

// 启动服务
await service.start(['stdio']);
```

### 装饰器用法

```typescript
import { MCPService, tool, resource, prompt, mcpService } from '@mcp-framework/core';

@mcpService({
  name: 'advanced-service',
  version: '1.0.0',
  description: 'Advanced MCP service with decorators'
})
class AdvancedService {
  @tool({
    description: 'Calculate sum of two numbers',
    inputSchema: {
      type: 'object',
      properties: {
        a: { type: 'number' },
        b: { type: 'number' }
      },
      required: ['a', 'b']
    }
  })
  async add({ a, b }: { a: number; b: number }) {
    return a + b;
  }

  @resource({
    pattern: 'file://**',
    name: 'File System',
    description: 'Read files from filesystem'
  })
  async readFile(uri: string) {
    const fs = await import('fs/promises');
    const path = uri.replace('file://', '');
    const content = await fs.readFile(path, 'utf-8');
    
    return [{
      uri,
      mimeType: 'text/plain',
      text: content
    }];
  }

  @prompt({
    description: 'Generate a coding prompt',
    arguments: [
      { name: 'language', description: 'Programming language', required: true },
      { name: 'difficulty', description: 'Difficulty level', required: false }
    ]
  })
  async codingPrompt({ language, difficulty = 'medium' }: { language: string; difficulty?: string }) {
    return [{
      role: 'user' as const,
      content: {
        type: 'text' as const,
        text: `Write a ${difficulty} level ${language} program that demonstrates best practices.`
      }
    }];
  }
}

// 使用装饰器服务
const service = new MCPService({
  name: 'advanced-service',
  version: '1.0.0'
});

service.registerService(new AdvancedService());
await service.start(['stdio', 'http']);
```

### 多传输支持

```typescript
// 同时启动多种传输方式
await service.start([
  'stdio',                                    // CLI工具
  { type: 'http', config: { port: 3000 } },  // REST API
  { type: 'sse', config: { port: 3001 } }    // 实时Web
]);
```

## API Reference

### MCPService

主服务类，管理工具、资源、提示和传输。

```typescript
const service = new MCPService({
  name: 'my-service',
  version: '1.0.0',
  description: 'Service description',
  debug: true
});
```

### 工厂函数

#### createMCPService()

创建服务构建器：

```typescript
const service = createMCPService('name', 'version')
  .description('Description')
  .debug(true)
  .tool(name, description, schema, handler)
  .resource(pattern, name, description, handler)
  .prompt(name, description, handler)
  .build();
```

#### 预定义示例

```typescript
import { Examples } from '@mcp-framework/core';

// Echo服务
const echoService = Examples.echo();

// 文件操作服务
const fileService = Examples.fileOps('/path/to/files');

// Web工具服务
const webService = Examples.webUtils();
```

### 装饰器

#### @tool

定义工具方法：

```typescript
@tool({
  description: 'Tool description',
  inputSchema: { /* JSON Schema */ }
})
async myTool(input: any) {
  return result;
}
```

#### @resource

定义资源处理器：

```typescript
@resource({
  pattern: 'scheme://**',
  name: 'Resource name',
  description: 'Resource description'
})
async myResource(uri: string) {
  return [{ uri, text: 'content' }];
}
```

#### @prompt

定义提示生成器：

```typescript
@prompt({
  description: 'Prompt description',
  arguments: [{ name: 'arg', description: 'Argument' }]
})
async myPrompt(args: any) {
  return [{ role: 'user', content: { type: 'text', text: 'prompt' } }];
}
```

### 传输配置

#### Stdio传输

```typescript
await service.start([{
  type: 'stdio',
  config: {
    input: process.stdin,
    output: process.stdout
  }
}]);
```

#### HTTP传输

```typescript
await service.start([{
  type: 'http',
  config: {
    port: 3000,
    host: 'localhost',
    path: '/mcp',
    cors: {
      origin: '*',
      credentials: true
    }
  }
}]);
```

#### SSE传输

```typescript
await service.start([{
  type: 'sse',
  config: {
    port: 3001,
    heartbeatInterval: 30000,
    maxConnections: 100
  }
}]);
```

## 错误处理

框架提供完善的错误处理机制：

```typescript
import { MCPError, MCPErrorCode } from '@mcp-framework/core';

// 抛出MCP错误
throw new MCPError(MCPErrorCode.INVALID_TOOL, 'Tool not found');

// 捕获和处理错误
service.on('error', (error) => {
  console.error('Service error:', error);
});
```

## 调试和监控

```typescript
// 启用调试模式
const service = new MCPService({
  name: 'debug-service',
  version: '1.0.0',
  debug: true
});

// 获取服务统计
const stats = service.getStats();
console.log('Service stats:', stats);

// 监听事件
service.on('toolCall', (toolName, input, result) => {
  console.log(`Tool called: ${toolName}`, { input, result });
});
```

## 最佳实践

1. **类型安全**: 使用TypeScript类型定义输入输出
2. **错误处理**: 适当处理和抛出错误
3. **日志记录**: 使用提供的logger进行调试
4. **资源管理**: 正确清理资源和连接
5. **测试**: 编写单元测试和集成测试

## License

MIT
