/**
 * MCP Service Implementation
 * 
 * Main service class that integrates protocol, transport, and application layers
 */

import { EventEmitter } from 'events';
import {
  ServiceConfig,
  ServiceStats,
  ServiceEvents,
  ToolDefinition,
  ResourceDefinition,
  PromptDefinition,
  ToolContext,
  ResourceContext,
  PromptContext,
  Logger,
} from './handlers.js';
import {
  TransportManager,
  TransportManagerConfig,
  TransportType,
} from './transport/index.js';
import {
  MessageHandler,
  MessageParser,
  ResponseBuilder,
  MCPValidator,
  MCPError,
  createMCPError,
} from './protocol/index.js';
import {
  MCPMethod,
  InitializeRequest,
  InitializeResponse,
  ToolsListResponse,
  ToolsCallRequest,
  ToolsCallResponse,
  ResourcesListResponse,
  ResourcesReadRequest,
  ResourcesReadResponse,
  PromptsListResponse,
  PromptsGetRequest,
  PromptsGetResponse,
} from './types/index.js';
import {
  getToolDefinitions,
  getResourceDefinitions,
  getPromptDefinitions,
  getServiceConfig,
  hasMCPDecorators,
} from './decorators.js';

/**
 * Default logger implementation
 */
class DefaultLogger implements Logger {
  constructor(private debugMode: boolean = false) {}

  debug(message: string, ...args: any[]): void {
    if (this.debugMode) {
      console.debug(`[DEBUG] ${message}`, ...args);
    }
  }

  info(message: string, ...args: any[]): void {
    console.info(`[INFO] ${message}`, ...args);
  }

  warn(message: string, ...args: any[]): void {
    console.warn(`[WARN] ${message}`, ...args);
  }

  error(message: string, ...args: any[]): void {
    console.error(`[ERROR] ${message}`, ...args);
  }
}

/**
 * Main MCP Service class
 */
export class MCPService extends EventEmitter {
  private _config: ServiceConfig;
  private _logger: Logger;
  private _transportManager?: TransportManager;
  private _messageHandler: MessageHandler;
  private _tools = new Map<string, ToolDefinition>();
  private _resources = new Map<string | RegExp, ResourceDefinition>();
  private _prompts = new Map<string, PromptDefinition>();
  private _stats: ServiceStats;
  private _startTime?: Date;
  private _initialized = false;

  constructor(config: ServiceConfig) {
    super();
    
    this._config = {
      protocolVersion: '2024-11-05',
      capabilities: {
        tools: { listChanged: false },
        resources: { subscribe: false, listChanged: false },
        prompts: { listChanged: false },
      },
      debug: false,
      ...config,
    };

    this._logger = config.logger || new DefaultLogger(this._config.debug);
    this._messageHandler = new MessageHandler();
    
    this._stats = {
      uptime: 0,
      totalRequests: 0,
      totalErrors: 0,
      toolCalls: {},
      resourceAccess: {},
      promptGeneration: {},
    };

    this._setupMessageHandlers();
  }

  /**
   * Add a tool to the service
   */
  addTool(tool: ToolDefinition): void {
    this._tools.set(tool.name, tool);
    this._logger.debug(`Added tool: ${tool.name}`);
  }

  /**
   * Add a resource handler to the service
   */
  addResource(resource: ResourceDefinition): void {
    this._resources.set(resource.pattern, resource);
    this._logger.debug(`Added resource: ${resource.pattern}`);
  }

  /**
   * Add a prompt to the service
   */
  addPrompt(prompt: PromptDefinition): void {
    this._prompts.set(prompt.name, prompt);
    this._logger.debug(`Added prompt: ${prompt.name}`);
  }

  /**
   * Register a service class with decorators
   */
  registerService(serviceInstance: any): void {
    const constructor = serviceInstance.constructor;
    
    if (!hasMCPDecorators(constructor)) {
      throw new Error('Service class must have MCP decorators');
    }

    // Extract service configuration if available
    const serviceConfig = getServiceConfig(constructor);
    if (serviceConfig) {
      this._config = { ...this._config, ...serviceConfig };
    }

    // Register tools
    const tools = getToolDefinitions(constructor);
    for (const tool of tools) {
      this.addTool({
        ...tool,
        handler: tool.handler.bind(serviceInstance),
      } as ToolDefinition);
    }

    // Register resources
    const resources = getResourceDefinitions(constructor);
    for (const resource of resources) {
      this.addResource({
        ...resource,
        handler: resource.handler.bind(serviceInstance),
      } as ResourceDefinition);
    }

    // Register prompts
    const prompts = getPromptDefinitions(constructor);
    for (const prompt of prompts) {
      this.addPrompt({
        ...prompt,
        handler: prompt.handler.bind(serviceInstance),
      } as PromptDefinition);
    }

    this._logger.info(`Registered service class: ${constructor.name}`);
  }

  /**
   * Start the service with specified transports
   */
  async start(transports: Array<TransportType | { type: TransportType; config?: any }>): Promise<void> {
    if (this._transportManager) {
      throw new Error('Service is already started');
    }

    // Prepare transport configuration
    const transportConfig: TransportManagerConfig = {
      transports: transports.map(t => 
        typeof t === 'string' 
          ? { type: t, enabled: true }
          : { type: t.type, config: t.config, enabled: true }
      ),
      defaultTransport: typeof transports[0] === 'string' ? transports[0] : transports[0].type,
      debug: this._config.debug,
    };

    // Create and start transport manager
    this._transportManager = new TransportManager(transportConfig);
    
    // Set up transport event handlers
    this._transportManager.on('message', async (message: string, transportName: string) => {
      await this._handleMessage(message, transportName);
    });

    this._transportManager.on('error', (error: Error, transportName: string) => {
      this._logger.error(`Transport error (${transportName}):`, error);
      this.emit('error', error);
    });

    await this._transportManager.startAll();
    
    this._startTime = new Date();
    this._logger.info(`Service started: ${this._config.name} v${this._config.version}`);
    this.emit('started');
  }

  /**
   * Stop the service
   */
  async stop(): Promise<void> {
    if (this._transportManager) {
      await this._transportManager.stopAll();
      this._transportManager = undefined;
    }

    this._logger.info('Service stopped');
    this.emit('stopped');
  }

  /**
   * Get service statistics
   */
  getStats(): ServiceStats {
    return {
      ...this._stats,
      uptime: this._startTime ? Date.now() - this._startTime.getTime() : 0,
    };
  }

  /**
   * Setup message handlers for MCP methods
   */
  private _setupMessageHandlers(): void {
    // Initialize method
    this._messageHandler.registerMethod(MCPMethod.INITIALIZE, async (params) => {
      const request = MCPValidator.validateParams(MCPMethod.INITIALIZE, params) as InitializeRequest;
      
      this._initialized = true;
      this._logger.info(`Initialized by client: ${request.clientInfo.name} v${request.clientInfo.version}`);

      const response: InitializeResponse = {
        protocolVersion: this._config.protocolVersion!,
        capabilities: this._config.capabilities!,
        serverInfo: {
          name: this._config.name,
          version: this._config.version,
        },
      };

      return response;
    });

    // Tools methods
    this._messageHandler.registerMethod(MCPMethod.TOOLS_LIST, async () => {
      const tools = Array.from(this._tools.values()).map(tool => ({
        name: tool.name,
        description: tool.description,
        inputSchema: tool.inputSchema,
      }));

      const response: ToolsListResponse = { tools };
      return response;
    });

    this._messageHandler.registerMethod(MCPMethod.TOOLS_CALL, async (params) => {
      const request = MCPValidator.validateParams(MCPMethod.TOOLS_CALL, params) as ToolsCallRequest;
      return await this._handleToolCall(request);
    });

    // Resources methods
    this._messageHandler.registerMethod(MCPMethod.RESOURCES_LIST, async () => {
      const resources = Array.from(this._resources.values()).map(resource => ({
        uri: typeof resource.pattern === 'string' ? resource.pattern : resource.pattern.source,
        name: resource.name,
        description: resource.description,
        mimeType: resource.mimeType,
      }));

      const response: ResourcesListResponse = { resources };
      return response;
    });

    this._messageHandler.registerMethod(MCPMethod.RESOURCES_READ, async (params) => {
      const request = MCPValidator.validateParams(MCPMethod.RESOURCES_READ, params) as ResourcesReadRequest;
      return await this._handleResourceRead(request);
    });

    // Prompts methods
    this._messageHandler.registerMethod(MCPMethod.PROMPTS_LIST, async () => {
      const prompts = Array.from(this._prompts.values()).map(prompt => ({
        name: prompt.name,
        description: prompt.description,
        arguments: prompt.arguments,
      }));

      const response: PromptsListResponse = { prompts };
      return response;
    });

    this._messageHandler.registerMethod(MCPMethod.PROMPTS_GET, async (params) => {
      const request = MCPValidator.validateParams(MCPMethod.PROMPTS_GET, params) as PromptsGetRequest;
      return await this._handlePromptGet(request);
    });
  }

  /**
   * Handle incoming message
   */
  private async _handleMessage(rawMessage: string, transportName: string): Promise<void> {
    try {
      this._stats.totalRequests++;
      
      const message = MessageParser.parse(rawMessage);
      const response = await this._messageHandler.handleMessage(message);
      
      if (response && this._transportManager) {
        const responseMessage = MessageParser.serialize(response);
        await this._transportManager.send(responseMessage, transportName);
      }
    } catch (error) {
      this._stats.totalErrors++;
      this._logger.error('Message handling error:', error);
      
      if (this._transportManager) {
        const errorResponse = ResponseBuilder.errorFromUnknown(null, error);
        const responseMessage = MessageParser.serialize(errorResponse);
        await this._transportManager.send(responseMessage, transportName);
      }
    }
  }

  /**
   * Handle tool call
   */
  private async _handleToolCall(request: ToolsCallRequest): Promise<ToolsCallResponse> {
    const tool = this._tools.get(request.name);
    if (!tool) {
      throw new MCPError(-32000, `Tool not found: ${request.name}`);
    }

    const startTime = Date.now();
    
    try {
      // Validate input
      const validatedInput = MCPValidator.validateToolInput(request.arguments, tool.inputSchema);
      
      // Create context
      const context: ToolContext = {
        logger: this._logger,
        metadata: {},
      };

      // Execute tool
      const result = await tool.handler(validatedInput, context);
      
      // Update statistics
      const executionTime = Date.now() - startTime;
      this._updateToolStats(request.name, executionTime, false);
      
      this.emit('toolCall', request.name, validatedInput, result);

      return {
        content: [{
          type: 'text',
          text: typeof result === 'string' ? result : JSON.stringify(result),
        }],
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      this._updateToolStats(request.name, executionTime, true);
      
      throw createMCPError(error);
    }
  }

  /**
   * Handle resource read
   */
  private async _handleResourceRead(request: ResourcesReadRequest): Promise<ResourcesReadResponse> {
    // Find matching resource handler
    let matchedResource: ResourceDefinition | undefined;
    
    for (const [pattern, resource] of this._resources) {
      if (typeof pattern === 'string') {
        // Simple string matching (could be enhanced with glob patterns)
        if (request.uri.startsWith(pattern.replace('**', ''))) {
          matchedResource = resource;
          break;
        }
      } else {
        // RegExp matching
        if (pattern.test(request.uri)) {
          matchedResource = resource;
          break;
        }
      }
    }

    if (!matchedResource) {
      throw new MCPError(-32002, `Resource not found: ${request.uri}`);
    }

    try {
      const context: ResourceContext = {
        logger: this._logger,
        metadata: {},
      };

      const contents = await matchedResource.handler(request.uri, context);
      
      this.emit('resourceAccess', request.uri, contents);

      return { contents };
    } catch (error) {
      throw createMCPError(error);
    }
  }

  /**
   * Handle prompt get
   */
  private async _handlePromptGet(request: PromptsGetRequest): Promise<PromptsGetResponse> {
    const prompt = this._prompts.get(request.name);
    if (!prompt) {
      throw new MCPError(-32004, `Prompt not found: ${request.name}`);
    }

    try {
      const context: PromptContext = {
        logger: this._logger,
        metadata: {},
      };

      const messages = await prompt.handler(request.arguments || {}, context);
      
      this.emit('promptGeneration', request.name, request.arguments, messages);

      return {
        description: prompt.description,
        messages,
      };
    } catch (error) {
      throw createMCPError(error);
    }
  }

  /**
   * Update tool statistics
   */
  private _updateToolStats(toolName: string, executionTime: number, isError: boolean): void {
    if (!this._stats.toolCalls[toolName]) {
      this._stats.toolCalls[toolName] = {
        count: 0,
        errors: 0,
        averageTime: 0,
      };
    }

    const stats = this._stats.toolCalls[toolName];
    stats.count++;
    
    if (isError) {
      stats.errors++;
    }

    // Update average execution time
    stats.averageTime = (stats.averageTime * (stats.count - 1) + executionTime) / stats.count;
  }
}
