/**
 * Factory Functions
 * 
 * Convenient factory functions for creating MCP services and components
 */

import { MCPService } from './service.js';
import { ServiceConfig, ToolDefinition, ResourceDefinition, PromptDefinition } from './handlers.js';
import { TransportType } from './transport/index.js';
import { JsonSchema } from './types/index.js';

/**
 * Service builder for fluent API
 */
export class ServiceBuilder {
  private _config: Partial<ServiceConfig> = {};
  private _tools: ToolDefinition[] = [];
  private _resources: ResourceDefinition[] = [];
  private _prompts: PromptDefinition[] = [];

  /**
   * Set service name
   */
  name(name: string): ServiceBuilder {
    this._config.name = name;
    return this;
  }

  /**
   * Set service version
   */
  version(version: string): ServiceBuilder {
    this._config.version = version;
    return this;
  }

  /**
   * Set service description
   */
  description(description: string): ServiceBuilder {
    this._config.description = description;
    return this;
  }

  /**
   * Enable debug mode
   */
  debug(enabled = true): ServiceBuilder {
    this._config.debug = enabled;
    return this;
  }

  /**
   * Add a tool
   */
  tool<TInput = any, TOutput = any>(
    name: string,
    description: string,
    inputSchema: JsonSchema,
    handler: (input: TInput) => Promise<TOutput> | TOutput
  ): ServiceBuilder {
    this._tools.push({
      name,
      description,
      inputSchema,
      handler: async (input, context) => handler(input),
    });
    return this;
  }

  /**
   * Add a resource
   */
  resource(
    pattern: string | RegExp,
    name: string,
    description: string,
    handler: (uri: string) => Promise<any> | any,
    mimeType?: string
  ): ServiceBuilder {
    this._resources.push({
      pattern,
      name,
      description,
      mimeType,
      handler: async (uri, context) => {
        const result = await handler(uri);
        return Array.isArray(result) ? result : [result];
      },
    });
    return this;
  }

  /**
   * Add a prompt
   */
  prompt(
    name: string,
    description: string,
    handler: (args: Record<string, any>) => Promise<any> | any,
    args?: Array<{ name: string; description: string; required?: boolean }>
  ): ServiceBuilder {
    this._prompts.push({
      name,
      description,
      arguments: args,
      handler: async (args, context) => {
        const result = await handler(args);
        return Array.isArray(result) ? result : [result];
      },
    });
    return this;
  }

  /**
   * Build the service
   */
  build(): MCPService {
    if (!this._config.name) {
      throw new Error('Service name is required');
    }
    if (!this._config.version) {
      throw new Error('Service version is required');
    }

    const service = new MCPService(this._config as ServiceConfig);

    // Add all tools, resources, and prompts
    for (const tool of this._tools) {
      service.addTool(tool);
    }
    for (const resource of this._resources) {
      service.addResource(resource);
    }
    for (const prompt of this._prompts) {
      service.addPrompt(prompt);
    }

    return service;
  }
}

/**
 * Create a new service builder
 */
export function createMCPService(name?: string, version?: string): ServiceBuilder {
  const builder = new ServiceBuilder();
  if (name) builder.name(name);
  if (version) builder.version(version);
  return builder;
}

/**
 * Quick service creation with minimal configuration
 */
export function quickService(config: {
  name: string;
  version: string;
  description?: string;
  debug?: boolean;
}): MCPService {
  return new MCPService(config);
}

/**
 * Create a simple tool definition
 */
export function createTool<TInput = any, TOutput = any>(
  name: string,
  description: string,
  inputSchema: JsonSchema,
  handler: (input: TInput) => Promise<TOutput> | TOutput
): ToolDefinition<TInput, TOutput> {
  return {
    name,
    description,
    inputSchema,
    handler: async (input, context) => handler(input),
  };
}

/**
 * Create a simple resource definition
 */
export function createResource(
  pattern: string | RegExp,
  name: string,
  description: string,
  handler: (uri: string) => Promise<any> | any,
  mimeType?: string
): ResourceDefinition {
  return {
    pattern,
    name,
    description,
    mimeType,
    handler: async (uri, context) => {
      const result = await handler(uri);
      return Array.isArray(result) ? result : [result];
    },
  };
}

/**
 * Create a simple prompt definition
 */
export function createPrompt(
  name: string,
  description: string,
  handler: (args: Record<string, any>) => Promise<any> | any,
  args?: Array<{ name: string; description: string; required?: boolean }>
): PromptDefinition {
  return {
    name,
    description,
    arguments: args,
    handler: async (args, context) => {
      const result = await handler(args);
      return Array.isArray(result) ? result : [result];
    },
  };
}

/**
 * Predefined common JSON schemas
 */
export const CommonSchemas = {
  string: {
    type: 'string' as const,
  },
  number: {
    type: 'number' as const,
  },
  boolean: {
    type: 'boolean' as const,
  },
  stringArray: {
    type: 'array' as const,
    items: { type: 'string' as const },
  },
  object: (properties: Record<string, JsonSchema>, required?: string[]) => ({
    type: 'object' as const,
    properties,
    ...(required && { required }),
  }),
  optional: (schema: JsonSchema) => ({
    ...schema,
    optional: true,
  }),
} as const;

/**
 * Helper for creating file system resources
 */
export function createFileResource(
  basePath: string,
  name = 'File System',
  description = 'Access to file system'
): ResourceDefinition {
  return createResource(
    new RegExp(`^file://${basePath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`),
    name,
    description,
    async (uri: string) => {
      const fs = await import('fs/promises');
      const path = await import('path');
      
      const filePath = uri.replace('file://', '');
      const resolvedPath = path.resolve(basePath, path.relative(basePath, filePath));
      
      // Security check: ensure path is within basePath
      if (!resolvedPath.startsWith(path.resolve(basePath))) {
        throw new Error('Access denied: path outside allowed directory');
      }

      const content = await fs.readFile(resolvedPath, 'utf-8');
      const stats = await fs.stat(resolvedPath);
      
      return {
        uri,
        mimeType: 'text/plain',
        text: content,
      };
    },
    'text/plain'
  );
}

/**
 * Helper for creating HTTP client tools
 */
export function createHttpTool(
  name = 'http_request',
  description = 'Make HTTP requests'
): ToolDefinition {
  return createTool(
    name,
    description,
    CommonSchemas.object({
      url: CommonSchemas.string,
      method: { type: 'string', enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'] },
      headers: { type: 'object', additionalProperties: { type: 'string' } },
      body: CommonSchemas.string,
    }, ['url']),
    async ({ url, method = 'GET', headers = {}, body }) => {
      const response = await fetch(url, {
        method,
        headers,
        ...(body && { body }),
      });

      return {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        body: await response.text(),
      };
    }
  );
}

/**
 * Example service configurations
 */
export const Examples = {
  /**
   * Simple echo service
   */
  echo: () => createMCPService('echo-service', '1.0.0')
    .description('Simple echo service for testing')
    .tool(
      'echo',
      'Echo back the input message',
      CommonSchemas.object({ message: CommonSchemas.string }, ['message']),
      ({ message }) => `Echo: ${message}`
    )
    .build(),

  /**
   * File operations service
   */
  fileOps: (basePath: string) => {
    const service = createMCPService('file-ops', '1.0.0')
      .description('File operations service')
      .tool(
        'read_file',
        'Read file contents',
        CommonSchemas.object({ path: CommonSchemas.string }, ['path']),
        async ({ path }) => {
          const fs = await import('fs/promises');
          return await fs.readFile(path, 'utf-8');
        }
      );

    service.build().addResource(createFileResource(basePath));
    return service.build();
  },

  /**
   * Web utilities service
   */
  webUtils: () => createMCPService('web-utils', '1.0.0')
    .description('Web utilities and HTTP client')
    .build()
    .addTool(createHttpTool()),
} as const;
