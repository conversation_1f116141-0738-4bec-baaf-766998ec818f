/**
 * JSON-RPC 2.0 Protocol Types
 * 
 * Based on JSON-RPC 2.0 Specification
 * https://www.jsonrpc.org/specification
 */

import { z } from 'zod';

// JSON-RPC 2.0 version constant
export const JSONRPC_VERSION = '2.0' as const;

// Request ID can be string, number, or null
export const RequestIdSchema = z.union([z.string(), z.number(), z.null()]);
export type RequestId = z.infer<typeof RequestIdSchema>;

// JSON-RPC 2.0 Request
export const JsonRpcRequestSchema = z.object({
  jsonrpc: z.literal(JSONRPC_VERSION),
  method: z.string(),
  params: z.unknown().optional(),
  id: RequestIdSchema,
});

export type JsonRpcRequest = z.infer<typeof JsonRpcRequestSchema>;

// JSON-RPC 2.0 Notification (request without id)
export const JsonRpcNotificationSchema = z.object({
  jsonrpc: z.literal(JSONRPC_VERSION),
  method: z.string(),
  params: z.unknown().optional(),
});

export type JsonRpcNotification = z.infer<typeof JsonRpcNotificationSchema>;

// JSON-RPC 2.0 Error
export const JsonRpcErrorSchema = z.object({
  code: z.number(),
  message: z.string(),
  data: z.unknown().optional(),
});

export type JsonRpcError = z.infer<typeof JsonRpcErrorSchema>;

// JSON-RPC 2.0 Success Response
export const JsonRpcSuccessResponseSchema = z.object({
  jsonrpc: z.literal(JSONRPC_VERSION),
  result: z.unknown(),
  id: RequestIdSchema,
});

export type JsonRpcSuccessResponse = z.infer<typeof JsonRpcSuccessResponseSchema>;

// JSON-RPC 2.0 Error Response
export const JsonRpcErrorResponseSchema = z.object({
  jsonrpc: z.literal(JSONRPC_VERSION),
  error: JsonRpcErrorSchema,
  id: RequestIdSchema,
});

export type JsonRpcErrorResponse = z.infer<typeof JsonRpcErrorResponseSchema>;

// JSON-RPC 2.0 Response (success or error)
export const JsonRpcResponseSchema = z.union([
  JsonRpcSuccessResponseSchema,
  JsonRpcErrorResponseSchema,
]);

export type JsonRpcResponse = z.infer<typeof JsonRpcResponseSchema>;

// JSON-RPC 2.0 Message (request, notification, or response)
export const JsonRpcMessageSchema = z.union([
  JsonRpcRequestSchema,
  JsonRpcNotificationSchema,
  JsonRpcResponseSchema,
]);

export type JsonRpcMessage = z.infer<typeof JsonRpcMessageSchema>;

// Standard JSON-RPC 2.0 Error Codes
export const JsonRpcErrorCode = {
  PARSE_ERROR: -32700,
  INVALID_REQUEST: -32600,
  METHOD_NOT_FOUND: -32601,
  INVALID_PARAMS: -32602,
  INTERNAL_ERROR: -32603,
  // Server error range: -32099 to -32000
} as const;

// Type guards
export function isJsonRpcRequest(message: JsonRpcMessage): message is JsonRpcRequest {
  return 'id' in message && 'method' in message;
}

export function isJsonRpcNotification(message: JsonRpcMessage): message is JsonRpcNotification {
  return !('id' in message) && 'method' in message;
}

export function isJsonRpcResponse(message: JsonRpcMessage): message is JsonRpcResponse {
  return 'result' in message || 'error' in message;
}

export function isJsonRpcSuccessResponse(response: JsonRpcResponse): response is JsonRpcSuccessResponse {
  return 'result' in response;
}

export function isJsonRpcErrorResponse(response: JsonRpcResponse): response is JsonRpcErrorResponse {
  return 'error' in response;
}
