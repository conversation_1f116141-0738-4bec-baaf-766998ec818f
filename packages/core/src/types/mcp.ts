/**
 * MCP (Model Context Protocol) Types
 * 
 * Protocol-specific types for MCP implementation
 */

import { z } from 'zod';

// JSON Schema for input/output validation
export const JsonSchemaSchema = z.object({
  type: z.string(),
  properties: z.record(z.unknown()).optional(),
  required: z.array(z.string()).optional(),
  description: z.string().optional(),
  default: z.unknown().optional(),
  enum: z.array(z.unknown()).optional(),
  items: z.unknown().optional(),
  additionalProperties: z.boolean().optional(),
});

export type JsonSchema = z.infer<typeof JsonSchemaSchema>;

// Tool Definition
export const ToolSchema = z.object({
  name: z.string(),
  description: z.string(),
  inputSchema: JsonSchemaSchema,
});

export type Tool = z.infer<typeof ToolSchema>;

// Resource Definition
export const ResourceSchema = z.object({
  uri: z.string(),
  name: z.string(),
  description: z.string().optional(),
  mimeType: z.string().optional(),
});

export type Resource = z.infer<typeof ResourceSchema>;

// Resource Contents
export const ResourceContentsSchema = z.object({
  uri: z.string(),
  mimeType: z.string().optional(),
  text: z.string().optional(),
  blob: z.string().optional(), // base64 encoded
});

export type ResourceContents = z.infer<typeof ResourceContentsSchema>;

// Prompt Definition
export const PromptSchema = z.object({
  name: z.string(),
  description: z.string(),
  arguments: z.array(z.object({
    name: z.string(),
    description: z.string(),
    required: z.boolean().optional(),
  })).optional(),
});

export type Prompt = z.infer<typeof PromptSchema>;

// Prompt Message
export const PromptMessageSchema = z.object({
  role: z.enum(['user', 'assistant', 'system']),
  content: z.object({
    type: z.enum(['text', 'image']),
    text: z.string().optional(),
    data: z.string().optional(), // base64 for images
    mimeType: z.string().optional(),
  }),
});

export type PromptMessage = z.infer<typeof PromptMessageSchema>;

// MCP Method Names
export const MCPMethod = {
  // Lifecycle
  INITIALIZE: 'initialize',
  INITIALIZED: 'initialized',
  
  // Tools
  TOOLS_LIST: 'tools/list',
  TOOLS_CALL: 'tools/call',
  
  // Resources
  RESOURCES_LIST: 'resources/list',
  RESOURCES_READ: 'resources/read',
  RESOURCES_SUBSCRIBE: 'resources/subscribe',
  RESOURCES_UNSUBSCRIBE: 'resources/unsubscribe',
  
  // Prompts
  PROMPTS_LIST: 'prompts/list',
  PROMPTS_GET: 'prompts/get',
  
  // Notifications
  RESOURCES_UPDATED: 'notifications/resources/updated',
  TOOLS_UPDATED: 'notifications/tools/updated',
  PROMPTS_UPDATED: 'notifications/prompts/updated',
} as const;

// Initialize Request
export const InitializeRequestSchema = z.object({
  protocolVersion: z.string(),
  capabilities: z.object({
    tools: z.object({
      listChanged: z.boolean().optional(),
    }).optional(),
    resources: z.object({
      subscribe: z.boolean().optional(),
      listChanged: z.boolean().optional(),
    }).optional(),
    prompts: z.object({
      listChanged: z.boolean().optional(),
    }).optional(),
  }),
  clientInfo: z.object({
    name: z.string(),
    version: z.string(),
  }),
});

export type InitializeRequest = z.infer<typeof InitializeRequestSchema>;

// Initialize Response
export const InitializeResponseSchema = z.object({
  protocolVersion: z.string(),
  capabilities: z.object({
    tools: z.object({
      listChanged: z.boolean().optional(),
    }).optional(),
    resources: z.object({
      subscribe: z.boolean().optional(),
      listChanged: z.boolean().optional(),
    }).optional(),
    prompts: z.object({
      listChanged: z.boolean().optional(),
    }).optional(),
  }),
  serverInfo: z.object({
    name: z.string(),
    version: z.string(),
  }),
});

export type InitializeResponse = z.infer<typeof InitializeResponseSchema>;

// Tools List Response
export const ToolsListResponseSchema = z.object({
  tools: z.array(ToolSchema),
});

export type ToolsListResponse = z.infer<typeof ToolsListResponseSchema>;

// Tools Call Request
export const ToolsCallRequestSchema = z.object({
  name: z.string(),
  arguments: z.record(z.unknown()).optional(),
});

export type ToolsCallRequest = z.infer<typeof ToolsCallRequestSchema>;

// Tools Call Response
export const ToolsCallResponseSchema = z.object({
  content: z.array(z.object({
    type: z.enum(['text', 'image', 'resource']),
    text: z.string().optional(),
    data: z.string().optional(),
    mimeType: z.string().optional(),
    uri: z.string().optional(),
  })),
  isError: z.boolean().optional(),
});

export type ToolsCallResponse = z.infer<typeof ToolsCallResponseSchema>;

// Resources List Response
export const ResourcesListResponseSchema = z.object({
  resources: z.array(ResourceSchema),
});

export type ResourcesListResponse = z.infer<typeof ResourcesListResponseSchema>;

// Resources Read Request
export const ResourcesReadRequestSchema = z.object({
  uri: z.string(),
});

export type ResourcesReadRequest = z.infer<typeof ResourcesReadRequestSchema>;

// Resources Read Response
export const ResourcesReadResponseSchema = z.object({
  contents: z.array(ResourceContentsSchema),
});

export type ResourcesReadResponse = z.infer<typeof ResourcesReadResponseSchema>;

// Prompts List Response
export const PromptsListResponseSchema = z.object({
  prompts: z.array(PromptSchema),
});

export type PromptsListResponse = z.infer<typeof PromptsListResponseSchema>;

// Prompts Get Request
export const PromptsGetRequestSchema = z.object({
  name: z.string(),
  arguments: z.record(z.unknown()).optional(),
});

export type PromptsGetRequest = z.infer<typeof PromptsGetRequestSchema>;

// Prompts Get Response
export const PromptsGetResponseSchema = z.object({
  description: z.string().optional(),
  messages: z.array(PromptMessageSchema),
});

export type PromptsGetResponse = z.infer<typeof PromptsGetResponseSchema>;
