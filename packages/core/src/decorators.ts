/**
 * Decorator System
 * 
 * Decorators for defining tools, resources, and prompts
 */

import { JsonSchema } from './types/index.js';
import { ToolDefinition, ResourceDefinition, PromptDefinition } from './handlers.js';

// Metadata storage for decorated methods
const TOOL_METADATA_KEY = Symbol('mcp:tool');
const RESOURCE_METADATA_KEY = Symbol('mcp:resource');
const PROMPT_METADATA_KEY = Symbol('mcp:prompt');

/**
 * Tool decorator options
 */
export interface ToolDecoratorOptions {
  name?: string;
  description: string;
  inputSchema: JsonSchema;
}

/**
 * Resource decorator options
 */
export interface ResourceDecoratorOptions {
  pattern: string | RegExp;
  name: string;
  description?: string;
  mimeType?: string;
}

/**
 * Prompt decorator options
 */
export interface PromptDecoratorOptions {
  name?: string;
  description: string;
  arguments?: Array<{
    name: string;
    description: string;
    required?: boolean;
  }>;
}

/**
 * Tool decorator
 * 
 * @example
 * ```typescript
 * class MyService {
 *   @tool({
 *     description: 'Say hello to someone',
 *     inputSchema: {
 *       type: 'object',
 *       properties: {
 *         name: { type: 'string' }
 *       },
 *       required: ['name']
 *     }
 *   })
 *   async sayHello({ name }: { name: string }) {
 *     return `Hello, ${name}!`;
 *   }
 * }
 * ```
 */
export function tool(options: ToolDecoratorOptions) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const toolDef: Partial<ToolDefinition> = {
      name: options.name || propertyKey,
      description: options.description,
      inputSchema: options.inputSchema,
      handler: descriptor.value,
    };

    // Store metadata
    const existingTools = Reflect.getMetadata(TOOL_METADATA_KEY, target.constructor) || [];
    existingTools.push(toolDef);
    Reflect.defineMetadata(TOOL_METADATA_KEY, existingTools, target.constructor);

    return descriptor;
  };
}

/**
 * Resource decorator
 * 
 * @example
 * ```typescript
 * class MyService {
 *   @resource({
 *     pattern: 'file://**',
 *     name: 'File System',
 *     description: 'Access to file system',
 *     mimeType: 'text/plain'
 *   })
 *   async readFile(uri: string) {
 *     const path = uri.replace('file://', '');
 *     const content = await fs.readFile(path, 'utf-8');
 *     return [{
 *       uri,
 *       mimeType: 'text/plain',
 *       text: content
 *     }];
 *   }
 * }
 * ```
 */
export function resource(options: ResourceDecoratorOptions) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const resourceDef: Partial<ResourceDefinition> = {
      pattern: options.pattern,
      name: options.name,
      description: options.description,
      mimeType: options.mimeType,
      handler: descriptor.value,
    };

    // Store metadata
    const existingResources = Reflect.getMetadata(RESOURCE_METADATA_KEY, target.constructor) || [];
    existingResources.push(resourceDef);
    Reflect.defineMetadata(RESOURCE_METADATA_KEY, existingResources, target.constructor);

    return descriptor;
  };
}

/**
 * Prompt decorator
 * 
 * @example
 * ```typescript
 * class MyService {
 *   @prompt({
 *     description: 'Generate a greeting prompt',
 *     arguments: [
 *       { name: 'name', description: 'Name to greet', required: true },
 *       { name: 'formal', description: 'Use formal greeting', required: false }
 *     ]
 *   })
 *   async greetingPrompt({ name, formal }: { name: string; formal?: boolean }) {
 *     const greeting = formal ? 'Good day' : 'Hello';
 *     return [{
 *       role: 'user' as const,
 *       content: {
 *         type: 'text' as const,
 *         text: `${greeting}, ${name}! How can I help you today?`
 *       }
 *     }];
 *   }
 * }
 * ```
 */
export function prompt(options: PromptDecoratorOptions) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const promptDef: Partial<PromptDefinition> = {
      name: options.name || propertyKey,
      description: options.description,
      arguments: options.arguments,
      handler: descriptor.value,
    };

    // Store metadata
    const existingPrompts = Reflect.getMetadata(PROMPT_METADATA_KEY, target.constructor) || [];
    existingPrompts.push(promptDef);
    Reflect.defineMetadata(PROMPT_METADATA_KEY, existingPrompts, target.constructor);

    return descriptor;
  };
}

/**
 * Extract tool definitions from a class
 */
export function getToolDefinitions(target: any): ToolDefinition[] {
  return Reflect.getMetadata(TOOL_METADATA_KEY, target) || [];
}

/**
 * Extract resource definitions from a class
 */
export function getResourceDefinitions(target: any): ResourceDefinition[] {
  return Reflect.getMetadata(RESOURCE_METADATA_KEY, target) || [];
}

/**
 * Extract prompt definitions from a class
 */
export function getPromptDefinitions(target: any): PromptDefinition[] {
  return Reflect.getMetadata(PROMPT_METADATA_KEY, target) || [];
}

/**
 * Check if a class has MCP decorators
 */
export function hasMCPDecorators(target: any): boolean {
  return (
    Reflect.hasMetadata(TOOL_METADATA_KEY, target) ||
    Reflect.hasMetadata(RESOURCE_METADATA_KEY, target) ||
    Reflect.hasMetadata(PROMPT_METADATA_KEY, target)
  );
}

/**
 * Service class decorator for automatic registration
 * 
 * @example
 * ```typescript
 * @mcpService({
 *   name: 'my-service',
 *   version: '1.0.0',
 *   description: 'My awesome MCP service'
 * })
 * class MyService {
 *   // ... tool, resource, and prompt methods
 * }
 * ```
 */
export function mcpService(config: {
  name: string;
  version: string;
  description?: string;
}) {
  return function <T extends { new (...args: any[]): {} }>(constructor: T) {
    // Store service configuration
    Reflect.defineMetadata('mcp:service:config', config, constructor);
    
    return constructor;
  };
}

/**
 * Get service configuration from decorated class
 */
export function getServiceConfig(target: any): { name: string; version: string; description?: string } | null {
  return Reflect.getMetadata('mcp:service:config', target) || null;
}

// Note: This requires the 'reflect-metadata' package to be installed
// We'll need to add it to the dependencies
if (typeof Reflect === 'undefined' || !Reflect.getMetadata) {
  throw new Error(
    'Decorators require the "reflect-metadata" package. Please install it: npm install reflect-metadata'
  );
}
