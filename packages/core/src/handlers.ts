/**
 * Handler Interfaces
 * 
 * Type definitions for tool, resource, and prompt handlers
 */

import {
  JsonSchema,
  Tool,
  Resource,
  ResourceContents,
  Prompt,
  PromptMessage,
  ToolsCallResponse,
} from './types/index.js';

/**
 * Tool handler function type
 */
export type ToolHandler<TInput = any, TOutput = any> = (
  input: TInput,
  context: ToolContext
) => Promise<TOutput> | TOutput;

/**
 * Resource handler function type
 */
export type ResourceHandler = (
  uri: string,
  context: ResourceContext
) => Promise<ResourceContents[]> | ResourceContents[];

/**
 * Prompt handler function type
 */
export type PromptHandler = (
  args: Record<string, any>,
  context: PromptContext
) => Promise<PromptMessage[]> | PromptMessage[];

/**
 * Context provided to tool handlers
 */
export interface ToolContext {
  /** Request ID for correlation */
  requestId?: string | number | null;
  /** Client information */
  client?: {
    name: string;
    version: string;
  };
  /** Additional metadata */
  metadata?: Record<string, any>;
  /** Logger instance */
  logger?: Logger;
}

/**
 * Context provided to resource handlers
 */
export interface ResourceContext {
  /** Request ID for correlation */
  requestId?: string | number | null;
  /** Client information */
  client?: {
    name: string;
    version: string;
  };
  /** Additional metadata */
  metadata?: Record<string, any>;
  /** Logger instance */
  logger?: Logger;
}

/**
 * Context provided to prompt handlers
 */
export interface PromptContext {
  /** Request ID for correlation */
  requestId?: string | number | null;
  /** Client information */
  client?: {
    name: string;
    version: string;
  };
  /** Additional metadata */
  metadata?: Record<string, any>;
  /** Logger instance */
  logger?: Logger;
}

/**
 * Logger interface
 */
export interface Logger {
  debug(message: string, ...args: any[]): void;
  info(message: string, ...args: any[]): void;
  warn(message: string, ...args: any[]): void;
  error(message: string, ...args: any[]): void;
}

/**
 * Tool definition with handler
 */
export interface ToolDefinition<TInput = any, TOutput = any> extends Omit<Tool, 'name'> {
  name: string;
  handler: ToolHandler<TInput, TOutput>;
  middleware?: ToolMiddleware[];
}

/**
 * Resource definition with handler
 */
export interface ResourceDefinition extends Omit<Resource, 'uri'> {
  pattern: string | RegExp;
  handler: ResourceHandler;
  middleware?: ResourceMiddleware[];
}

/**
 * Prompt definition with handler
 */
export interface PromptDefinition extends Omit<Prompt, 'name'> {
  name: string;
  handler: PromptHandler;
  middleware?: PromptMiddleware[];
}

/**
 * Middleware function types
 */
export type ToolMiddleware = (
  input: any,
  context: ToolContext,
  next: () => Promise<any>
) => Promise<any>;

export type ResourceMiddleware = (
  uri: string,
  context: ResourceContext,
  next: () => Promise<ResourceContents[]>
) => Promise<ResourceContents[]>;

export type PromptMiddleware = (
  args: Record<string, any>,
  context: PromptContext,
  next: () => Promise<PromptMessage[]>
) => Promise<PromptMessage[]>;

/**
 * Service configuration
 */
export interface ServiceConfig {
  /** Service name */
  name: string;
  /** Service version */
  version: string;
  /** Service description */
  description?: string;
  /** Protocol version */
  protocolVersion?: string;
  /** Service capabilities */
  capabilities?: {
    tools?: {
      listChanged?: boolean;
    };
    resources?: {
      subscribe?: boolean;
      listChanged?: boolean;
    };
    prompts?: {
      listChanged?: boolean;
    };
  };
  /** Logger instance */
  logger?: Logger;
  /** Debug mode */
  debug?: boolean;
}

/**
 * Service statistics
 */
export interface ServiceStats {
  /** Service uptime in milliseconds */
  uptime: number;
  /** Total requests processed */
  totalRequests: number;
  /** Total errors encountered */
  totalErrors: number;
  /** Tool call statistics */
  toolCalls: Record<string, {
    count: number;
    errors: number;
    averageTime: number;
  }>;
  /** Resource access statistics */
  resourceAccess: Record<string, {
    count: number;
    errors: number;
  }>;
  /** Prompt generation statistics */
  promptGeneration: Record<string, {
    count: number;
    errors: number;
  }>;
}

/**
 * Service events
 */
export interface ServiceEvents {
  started: () => void;
  stopped: () => void;
  error: (error: Error) => void;
  toolCall: (toolName: string, input: any, result: any) => void;
  resourceAccess: (uri: string, result: ResourceContents[]) => void;
  promptGeneration: (promptName: string, args: any, result: PromptMessage[]) => void;
}
