/**
 * MCP Protocol Message Handler
 * 
 * Handles JSON-RPC message parsing, validation, and serialization
 */

import { z } from 'zod';
import {
  JsonRpcMessage,
  JsonRpcMessageSchema,
  JsonRpcRequest,
  JsonRpcResponse,
  JsonRpcNotification,
  JsonRpcSuccessResponse,
  JsonRpcErrorResponse,
  RequestId,
  JSONRPC_VERSION,
  isJsonRpcRequest,
  isJsonRpcNotification,
  isJsonRpcResponse,
} from '../types/jsonrpc.js';
import {
  MCPError,
  ParseError,
  InvalidRequestError,
  createMCPError,
} from './errors.js';

/**
 * Message parser for JSON-RPC messages
 */
export class MessageParser {
  /**
   * Parse raw message string to JSON-RPC message
   */
  static parse(rawMessage: string): JsonRpcMessage {
    try {
      const parsed = JSON.parse(rawMessage);
      return MessageParser.validate(parsed);
    } catch (error) {
      if (error instanceof SyntaxError) {
        throw new ParseError('Invalid JSON format', { originalError: error.message });
      }
      throw error;
    }
  }

  /**
   * Validate parsed object as JSON-RPC message
   */
  static validate(parsed: unknown): JsonRpcMessage {
    try {
      return JsonRpcMessageSchema.parse(parsed);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new InvalidRequestError('Invalid JSON-RPC message format', {
          validationErrors: error.errors,
        });
      }
      throw error;
    }
  }

  /**
   * Serialize JSON-RPC message to string
   */
  static serialize(message: JsonRpcMessage): string {
    try {
      return JSON.stringify(message);
    } catch (error) {
      throw new MCPError(-32603, 'Failed to serialize message', {
        originalError: error instanceof Error ? error.message : String(error),
      });
    }
  }
}

/**
 * Response builder for creating JSON-RPC responses
 */
export class ResponseBuilder {
  /**
   * Create success response
   */
  static success(id: RequestId, result: unknown): JsonRpcSuccessResponse {
    return {
      jsonrpc: JSONRPC_VERSION,
      result,
      id,
    };
  }

  /**
   * Create error response
   */
  static error(id: RequestId, error: MCPError): JsonRpcErrorResponse {
    return {
      jsonrpc: JSONRPC_VERSION,
      error: error.toJsonRpcError(),
      id,
    };
  }

  /**
   * Create error response from unknown error
   */
  static errorFromUnknown(id: RequestId, error: unknown): JsonRpcErrorResponse {
    const mcpError = createMCPError(error);
    return ResponseBuilder.error(id, mcpError);
  }
}

/**
 * Request builder for creating JSON-RPC requests
 */
export class RequestBuilder {
  private static requestId = 0;

  /**
   * Generate next request ID
   */
  static nextId(): number {
    return ++RequestBuilder.requestId;
  }

  /**
   * Create JSON-RPC request
   */
  static request(method: string, params?: unknown, id?: RequestId): JsonRpcRequest {
    return {
      jsonrpc: JSONRPC_VERSION,
      method,
      ...(params !== undefined && { params }),
      id: id ?? RequestBuilder.nextId(),
    };
  }

  /**
   * Create JSON-RPC notification
   */
  static notification(method: string, params?: unknown): JsonRpcNotification {
    return {
      jsonrpc: JSONRPC_VERSION,
      method,
      ...(params !== undefined && { params }),
    };
  }
}

/**
 * Message handler for processing JSON-RPC messages
 */
export class MessageHandler {
  private readonly methodHandlers = new Map<string, (params: unknown) => Promise<unknown>>();
  private readonly notificationHandlers = new Map<string, (params: unknown) => Promise<void>>();

  /**
   * Register method handler
   */
  registerMethod(method: string, handler: (params: unknown) => Promise<unknown>): void {
    this.methodHandlers.set(method, handler);
  }

  /**
   * Register notification handler
   */
  registerNotification(method: string, handler: (params: unknown) => Promise<void>): void {
    this.notificationHandlers.set(method, handler);
  }

  /**
   * Handle incoming JSON-RPC message
   */
  async handleMessage(message: JsonRpcMessage): Promise<JsonRpcResponse | null> {
    if (isJsonRpcRequest(message)) {
      return this.handleRequest(message);
    }
    
    if (isJsonRpcNotification(message)) {
      await this.handleNotification(message);
      return null; // Notifications don't return responses
    }
    
    if (isJsonRpcResponse(message)) {
      // This is a response to a request we sent
      // In a full implementation, this would be handled by a request manager
      return null;
    }

    throw new InvalidRequestError('Unknown message type');
  }

  /**
   * Handle JSON-RPC request
   */
  private async handleRequest(request: JsonRpcRequest): Promise<JsonRpcResponse> {
    try {
      const handler = this.methodHandlers.get(request.method);
      if (!handler) {
        throw new MCPError(-32601, `Method '${request.method}' not found`);
      }

      const result = await handler(request.params);
      return ResponseBuilder.success(request.id, result);
    } catch (error) {
      return ResponseBuilder.errorFromUnknown(request.id, error);
    }
  }

  /**
   * Handle JSON-RPC notification
   */
  private async handleNotification(notification: JsonRpcNotification): Promise<void> {
    try {
      const handler = this.notificationHandlers.get(notification.method);
      if (!handler) {
        // Notifications for unknown methods are silently ignored
        return;
      }

      await handler(notification.params);
    } catch (error) {
      // Notifications don't return errors, but we should log them
      console.error(`Error handling notification '${notification.method}':`, error);
    }
  }

  /**
   * Get list of registered methods
   */
  getRegisteredMethods(): string[] {
    return Array.from(this.methodHandlers.keys());
  }

  /**
   * Get list of registered notifications
   */
  getRegisteredNotifications(): string[] {
    return Array.from(this.notificationHandlers.keys());
  }
}
