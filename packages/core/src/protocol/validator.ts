/**
 * MCP Protocol Validator
 * 
 * Validates MCP-specific message formats and parameters
 */

import { z } from 'zod';
import {
  <PERSON><PERSON>eth<PERSON>,
  InitializeRequestSchema,
  ToolsCallRequestSchema,
  ResourcesReadRequestSchema,
  PromptsGetRequestSchema,
  JsonSchema,
  JsonSchemaSchema,
} from '../types/mcp.js';
import { InvalidParamsError } from './errors.js';

/**
 * Parameter validator for MCP methods
 */
export class MCPValidator {
  /**
   * Validate parameters for a specific MCP method
   */
  static validateParams(method: string, params: unknown): unknown {
    switch (method) {
      case MCPMethod.INITIALIZE:
        return MCPValidator.validateSchema(InitializeRequestSchema, params, 'initialize parameters');
      
      case MCPMethod.TOOLS_CALL:
        return MCPValidator.validateSchema(ToolsCallRequestSchema, params, 'tools/call parameters');
      
      case MCPMethod.RESOURCES_READ:
        return MCPValidator.validateSchema(ResourcesReadRequestSchema, params, 'resources/read parameters');
      
      case MCPMethod.PROMPTS_GET:
        return MCPValidator.validateSchema(PromptsGetRequestSchema, params, 'prompts/get parameters');
      
      // Methods without parameters
      case MCPMethod.TOOLS_LIST:
      case MCPMethod.RESOURCES_LIST:
      case MCPMethod.PROMPTS_LIST:
        if (params !== undefined && params !== null) {
          throw new InvalidParamsError(`Method '${method}' does not accept parameters`);
        }
        return undefined;
      
      default:
        // For unknown methods, we don't validate parameters
        return params;
    }
  }

  /**
   * Validate against a Zod schema
   */
  private static validateSchema<T>(schema: z.ZodSchema<T>, data: unknown, context: string): T {
    try {
      return schema.parse(data);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new InvalidParamsError(`Invalid ${context}`, {
          validationErrors: error.errors,
        });
      }
      throw error;
    }
  }

  /**
   * Validate JSON Schema definition
   */
  static validateJsonSchema(schema: unknown): JsonSchema {
    return MCPValidator.validateSchema(JsonSchemaSchema, schema, 'JSON Schema');
  }

  /**
   * Validate tool input against its schema
   */
  static validateToolInput(input: unknown, schema: JsonSchema): unknown {
    try {
      // Convert JSON Schema to Zod schema for validation
      const zodSchema = MCPValidator.jsonSchemaToZod(schema);
      return zodSchema.parse(input);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new InvalidParamsError('Invalid tool input', {
          validationErrors: error.errors,
        });
      }
      throw error;
    }
  }

  /**
   * Convert JSON Schema to Zod schema (basic implementation)
   * This is a simplified converter - a full implementation would handle more cases
   */
  private static jsonSchemaToZod(schema: JsonSchema): z.ZodSchema {
    switch (schema.type) {
      case 'string':
        let stringSchema = z.string();
        if (schema.enum) {
          stringSchema = z.enum(schema.enum as [string, ...string[]]);
        }
        return stringSchema;
      
      case 'number':
        return z.number();
      
      case 'integer':
        return z.number().int();
      
      case 'boolean':
        return z.boolean();
      
      case 'array':
        if (schema.items) {
          const itemSchema = MCPValidator.jsonSchemaToZod(schema.items as JsonSchema);
          return z.array(itemSchema);
        }
        return z.array(z.unknown());
      
      case 'object':
        if (schema.properties) {
          const shape: Record<string, z.ZodSchema> = {};
          const required = schema.required || [];
          
          for (const [key, propSchema] of Object.entries(schema.properties)) {
            let propZodSchema = MCPValidator.jsonSchemaToZod(propSchema as JsonSchema);
            
            if (!required.includes(key)) {
              propZodSchema = propZodSchema.optional();
            }
            
            shape[key] = propZodSchema;
          }
          
          return z.object(shape);
        }
        return z.record(z.unknown());
      
      default:
        return z.unknown();
    }
  }

  /**
   * Validate protocol version compatibility
   */
  static validateProtocolVersion(clientVersion: string, serverVersion: string): boolean {
    // Simple version comparison - in a real implementation, this would be more sophisticated
    const parseVersion = (version: string) => {
      const parts = version.split('.').map(Number);
      return { major: parts[0] || 0, minor: parts[1] || 0, patch: parts[2] || 0 };
    };

    const client = parseVersion(clientVersion);
    const server = parseVersion(serverVersion);

    // Major versions must match
    if (client.major !== server.major) {
      return false;
    }

    // Server minor version should be >= client minor version
    return server.minor >= client.minor;
  }

  /**
   * Validate capability requirements
   */
  static validateCapabilities(
    clientCapabilities: Record<string, unknown>,
    serverCapabilities: Record<string, unknown>
  ): { compatible: boolean; missingCapabilities: string[] } {
    const missingCapabilities: string[] = [];

    // Check if server supports required client capabilities
    for (const [capability, required] of Object.entries(clientCapabilities)) {
      if (required && !serverCapabilities[capability]) {
        missingCapabilities.push(capability);
      }
    }

    return {
      compatible: missingCapabilities.length === 0,
      missingCapabilities,
    };
  }
}
