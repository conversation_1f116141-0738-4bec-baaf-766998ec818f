/**
 * MCP Protocol Error Handling
 * 
 * Comprehensive error handling for MCP protocol operations
 */

import { JsonRpcError, JsonRpcErrorCode } from '../types/jsonrpc.js';

// MCP-specific error codes (extending JSON-RPC error codes)
export const MCPErrorCode = {
  // JSON-RPC standard errors
  ...JsonRpcErrorCode,
  
  // MCP-specific errors (-32000 to -32099)
  INVALID_TOOL: -32000,
  TOOL_EXECUTION_ERROR: -32001,
  RESOURCE_NOT_FOUND: -32002,
  RESOURCE_ACCESS_DENIED: -32003,
  PROMPT_NOT_FOUND: -32004,
  INVALID_PROMPT_ARGUMENTS: -32005,
  PROTOCOL_VERSION_MISMATCH: -32006,
  CAPABILITY_NOT_SUPPORTED: -32007,
  INITIALIZATION_FAILED: -32008,
  TRANSPORT_ERROR: -32009,
} as const;

export type MCPErrorCode = typeof MCPErrorCode[keyof typeof MCPErrorCode];

// Error messages for each error code
export const ErrorMessages: Record<MCPErrorCode, string> = {
  [MCPErrorCode.PARSE_ERROR]: 'Parse error',
  [MCPErrorCode.INVALID_REQUEST]: 'Invalid Request',
  [MCPErrorCode.METHOD_NOT_FOUND]: 'Method not found',
  [MCPErrorCode.INVALID_PARAMS]: 'Invalid params',
  [MCPErrorCode.INTERNAL_ERROR]: 'Internal error',
  [MCPErrorCode.INVALID_TOOL]: 'Invalid tool',
  [MCPErrorCode.TOOL_EXECUTION_ERROR]: 'Tool execution error',
  [MCPErrorCode.RESOURCE_NOT_FOUND]: 'Resource not found',
  [MCPErrorCode.RESOURCE_ACCESS_DENIED]: 'Resource access denied',
  [MCPErrorCode.PROMPT_NOT_FOUND]: 'Prompt not found',
  [MCPErrorCode.INVALID_PROMPT_ARGUMENTS]: 'Invalid prompt arguments',
  [MCPErrorCode.PROTOCOL_VERSION_MISMATCH]: 'Protocol version mismatch',
  [MCPErrorCode.CAPABILITY_NOT_SUPPORTED]: 'Capability not supported',
  [MCPErrorCode.INITIALIZATION_FAILED]: 'Initialization failed',
  [MCPErrorCode.TRANSPORT_ERROR]: 'Transport error',
};

/**
 * Base MCP Error class
 */
export class MCPError extends Error {
  public readonly code: MCPErrorCode;
  public readonly data?: unknown;

  constructor(code: MCPErrorCode, message?: string, data?: unknown) {
    const errorMessage = message || ErrorMessages[code] || 'Unknown error';
    super(errorMessage);
    
    this.name = 'MCPError';
    this.code = code;
    this.data = data;
    
    // Maintain proper stack trace for where our error was thrown
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, MCPError);
    }
  }

  /**
   * Convert to JSON-RPC error format
   */
  toJsonRpcError(): JsonRpcError {
    return {
      code: this.code,
      message: this.message,
      ...(this.data && { data: this.data }),
    };
  }

  /**
   * Create MCPError from JSON-RPC error
   */
  static fromJsonRpcError(error: JsonRpcError): MCPError {
    return new MCPError(error.code as MCPErrorCode, error.message, error.data);
  }
}

/**
 * Specific error classes for different error types
 */

export class ParseError extends MCPError {
  constructor(message?: string, data?: unknown) {
    super(MCPErrorCode.PARSE_ERROR, message, data);
    this.name = 'ParseError';
  }
}

export class InvalidRequestError extends MCPError {
  constructor(message?: string, data?: unknown) {
    super(MCPErrorCode.INVALID_REQUEST, message, data);
    this.name = 'InvalidRequestError';
  }
}

export class MethodNotFoundError extends MCPError {
  constructor(method: string, data?: unknown) {
    super(MCPErrorCode.METHOD_NOT_FOUND, `Method '${method}' not found`, data);
    this.name = 'MethodNotFoundError';
  }
}

export class InvalidParamsError extends MCPError {
  constructor(message?: string, data?: unknown) {
    super(MCPErrorCode.INVALID_PARAMS, message, data);
    this.name = 'InvalidParamsError';
  }
}

export class InternalError extends MCPError {
  constructor(message?: string, data?: unknown) {
    super(MCPErrorCode.INTERNAL_ERROR, message, data);
    this.name = 'InternalError';
  }
}

export class InvalidToolError extends MCPError {
  constructor(toolName: string, data?: unknown) {
    super(MCPErrorCode.INVALID_TOOL, `Invalid tool: ${toolName}`, data);
    this.name = 'InvalidToolError';
  }
}

export class ToolExecutionError extends MCPError {
  constructor(toolName: string, originalError?: Error) {
    const message = `Tool execution failed: ${toolName}`;
    const data = originalError ? {
      originalError: originalError.message,
      stack: originalError.stack,
    } : undefined;
    
    super(MCPErrorCode.TOOL_EXECUTION_ERROR, message, data);
    this.name = 'ToolExecutionError';
  }
}

export class ResourceNotFoundError extends MCPError {
  constructor(uri: string, data?: unknown) {
    super(MCPErrorCode.RESOURCE_NOT_FOUND, `Resource not found: ${uri}`, data);
    this.name = 'ResourceNotFoundError';
  }
}

export class ResourceAccessDeniedError extends MCPError {
  constructor(uri: string, data?: unknown) {
    super(MCPErrorCode.RESOURCE_ACCESS_DENIED, `Resource access denied: ${uri}`, data);
    this.name = 'ResourceAccessDeniedError';
  }
}

export class PromptNotFoundError extends MCPError {
  constructor(promptName: string, data?: unknown) {
    super(MCPErrorCode.PROMPT_NOT_FOUND, `Prompt not found: ${promptName}`, data);
    this.name = 'PromptNotFoundError';
  }
}

export class ProtocolVersionMismatchError extends MCPError {
  constructor(clientVersion: string, serverVersion: string) {
    const message = `Protocol version mismatch: client=${clientVersion}, server=${serverVersion}`;
    super(MCPErrorCode.PROTOCOL_VERSION_MISMATCH, message, {
      clientVersion,
      serverVersion,
    });
    this.name = 'ProtocolVersionMismatchError';
  }
}

/**
 * Utility function to create appropriate error from unknown error
 */
export function createMCPError(error: unknown): MCPError {
  if (error instanceof MCPError) {
    return error;
  }
  
  if (error instanceof Error) {
    return new InternalError(error.message, {
      originalError: error.message,
      stack: error.stack,
    });
  }
  
  return new InternalError('Unknown error occurred', { error });
}

/**
 * Type guard to check if an error is an MCPError
 */
export function isMCPError(error: unknown): error is MCPError {
  return error instanceof MCPError;
}
