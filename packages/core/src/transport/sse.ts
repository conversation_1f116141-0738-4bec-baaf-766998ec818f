/**
 * SSE Transport Implementation
 * 
 * Transport adapter for Server-Sent Events communication
 */

import { createServer, Server, IncomingMessage, ServerResponse } from 'http';
import { BaseTransport } from './base.js';
import { SSETransportConfig } from './types.js';
import { ConnectionError, SendError } from './errors.js';

interface SSEConnection {
  id: string;
  response: ServerResponse;
  lastActivity: Date;
}

/**
 * SSE transport for real-time web applications
 */
export class SSETransport extends BaseTransport {
  private _server?: Server;
  private _port: number;
  private _host: string;
  private _path: string;
  private _connections = new Map<string, SSEConnection>();
  private _heartbeatInterval?: NodeJS.Timeout;
  private _maxConnections: number;

  constructor(config: SSETransportConfig = {}) {
    super(config);
    
    this._port = config.port || 3000;
    this._host = config.host || 'localhost';
    this._path = config.path || '/mcp';
    this._maxConnections = config.maxConnections || 100;
  }

  /**
   * Start SSE server
   */
  protected async _start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this._server = createServer((req, res) => {
          this._handleRequest(req, res);
        });

        this._server.on('error', (error) => {
          this.emit('error', new ConnectionError(`SSE server error: ${error.message}`, this.name));
        });

        this._server.listen(this._port, this._host, () => {
          this._startHeartbeat();
          
          if (this._config.debug) {
            console.log(`[${this.name}] SSE server listening on ${this._host}:${this._port}${this._path}`);
          }
          resolve();
        });

      } catch (error) {
        reject(new ConnectionError(`Failed to start SSE server: ${error}`, this.name));
      }
    });
  }

  /**
   * Stop SSE server
   */
  protected async _stop(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Stop heartbeat
      if (this._heartbeatInterval) {
        clearInterval(this._heartbeatInterval);
        this._heartbeatInterval = undefined;
      }

      // Close all connections
      for (const [id, connection] of this._connections) {
        connection.response.end();
      }
      this._connections.clear();

      if (!this._server) {
        resolve();
        return;
      }

      this._server.close((error) => {
        if (error) {
          reject(new ConnectionError(`Failed to stop SSE server: ${error.message}`, this.name));
        } else {
          this._server = undefined;
          resolve();
        }
      });
    });
  }

  /**
   * Send message to all SSE connections
   */
  protected async _send(message: string): Promise<void> {
    if (this._connections.size === 0) {
      throw new SendError('No active SSE connections', this.name);
    }

    const sseMessage = this._formatSSEMessage(message);
    const failedConnections: string[] = [];

    for (const [id, connection] of this._connections) {
      try {
        connection.response.write(sseMessage);
        connection.lastActivity = new Date();
      } catch (error) {
        failedConnections.push(id);
        if (this._config.debug) {
          console.error(`[${this.name}] Failed to send to connection ${id}:`, error);
        }
      }
    }

    // Remove failed connections
    for (const id of failedConnections) {
      this._removeConnection(id);
    }

    if (failedConnections.length === this._connections.size) {
      throw new SendError('Failed to send to all connections', this.name);
    }
  }

  /**
   * Handle incoming HTTP request
   */
  private async _handleRequest(req: IncomingMessage, res: ServerResponse): Promise<void> {
    // Handle CORS preflight
    if (req.method === 'OPTIONS') {
      res.writeHead(200, {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Cache-Control',
      });
      res.end();
      return;
    }

    const url = new URL(req.url || '', `http://${req.headers.host}`);

    // Handle SSE connection
    if (req.method === 'GET' && url.pathname === this._path) {
      this._handleSSEConnection(req, res);
      return;
    }

    // Handle POST requests for client-to-server messages
    if (req.method === 'POST' && url.pathname === this._path) {
      await this._handlePostMessage(req, res);
      return;
    }

    // 404 for other requests
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ error: 'Not found' }));
  }

  /**
   * Handle SSE connection establishment
   */
  private _handleSSEConnection(req: IncomingMessage, res: ServerResponse): void {
    // Check connection limit
    if (this._connections.size >= this._maxConnections) {
      res.writeHead(503, { 'Content-Type': 'text/plain' });
      res.end('Too many connections');
      return;
    }

    const connectionId = this._generateConnectionId();

    // Set SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
    });

    // Send initial connection message
    res.write(`data: ${JSON.stringify({ type: 'connected', id: connectionId })}\n\n`);

    // Store connection
    const connection: SSEConnection = {
      id: connectionId,
      response: res,
      lastActivity: new Date(),
    };
    this._connections.set(connectionId, connection);

    // Handle connection close
    req.on('close', () => {
      this._removeConnection(connectionId);
    });

    req.on('error', () => {
      this._removeConnection(connectionId);
    });

    if (this._config.debug) {
      console.log(`[${this.name}] New SSE connection: ${connectionId}`);
    }
  }

  /**
   * Handle POST message from client
   */
  private async _handlePostMessage(req: IncomingMessage, res: ServerResponse): Promise<void> {
    try {
      const body = await this._readRequestBody(req);
      
      // Set CORS headers
      res.writeHead(200, {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      });
      
      res.end(JSON.stringify({ status: 'received' }));

      // Process the message
      this._handleMessage(body);

    } catch (error) {
      res.writeHead(400, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: 'Invalid request' }));
    }
  }

  /**
   * Read request body
   */
  private _readRequestBody(req: IncomingMessage): Promise<string> {
    return new Promise((resolve, reject) => {
      let body = '';
      
      req.on('data', (chunk) => {
        body += chunk.toString();
      });
      
      req.on('end', () => {
        resolve(body);
      });
      
      req.on('error', reject);
    });
  }

  /**
   * Format message for SSE
   */
  private _formatSSEMessage(message: string): string {
    return `data: ${message}\n\n`;
  }

  /**
   * Generate unique connection ID
   */
  private _generateConnectionId(): string {
    return `sse_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Remove connection
   */
  private _removeConnection(id: string): void {
    const connection = this._connections.get(id);
    if (connection) {
      try {
        connection.response.end();
      } catch (error) {
        // Ignore errors when closing connection
      }
      this._connections.delete(id);
      
      if (this._config.debug) {
        console.log(`[${this.name}] Removed SSE connection: ${id}`);
      }
    }
  }

  /**
   * Start heartbeat to keep connections alive
   */
  private _startHeartbeat(): void {
    const interval = (this._config as SSETransportConfig).heartbeatInterval || 30000;
    
    this._heartbeatInterval = setInterval(() => {
      const heartbeat = this._formatSSEMessage(JSON.stringify({ type: 'heartbeat', timestamp: Date.now() }));
      
      for (const [id, connection] of this._connections) {
        try {
          connection.response.write(heartbeat);
        } catch (error) {
          this._removeConnection(id);
        }
      }
    }, interval);
  }

  /**
   * Get active connections count
   */
  getConnectionsCount(): number {
    return this._connections.size;
  }
}

/**
 * Factory function for creating SSE transport
 */
export function createSSETransport(config: SSETransportConfig = {}): SSETransport {
  return new SSETransport(config);
}
