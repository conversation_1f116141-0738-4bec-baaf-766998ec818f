/**
 * Base Transport Implementation
 * 
 * Abstract base class providing common transport functionality
 */

import { EventEmitter } from 'events';
import {
  Transport,
  TransportConfig,
  TransportState,
  TransportStats,
  TransportMiddleware,
} from './types.js';
import { TransportError } from './errors.js';

/**
 * Abstract base transport class
 */
export abstract class BaseTransport extends EventEmitter implements Transport {
  protected _state: TransportState = TransportState.DISCONNECTED;
  protected _stats: TransportStats;
  protected _config: TransportConfig;
  protected _middleware: TransportMiddleware[] = [];
  protected _startTime: Date | null = null;

  constructor(config: TransportConfig) {
    super();
    this._config = {
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
      debug: false,
      ...config,
    };

    this._stats = {
      messagesReceived: 0,
      messagesSent: 0,
      errors: 0,
      connections: 0,
      uptime: 0,
      lastActivity: null,
    };

    // Set up error handling
    this.on('error', (error) => {
      this._stats.errors++;
      this._setState(TransportState.ERROR);
      if (this._config.debug) {
        console.error(`[${this.name}] Transport error:`, error);
      }
    });
  }

  get name(): string {
    return this._config.name || this.constructor.name;
  }

  get state(): TransportState {
    return this._state;
  }

  get stats(): TransportStats {
    return {
      ...this._stats,
      uptime: this._startTime ? Date.now() - this._startTime.getTime() : 0,
    };
  }

  /**
   * Start the transport
   */
  async start(): Promise<void> {
    if (this._state === TransportState.CONNECTED || this._state === TransportState.CONNECTING) {
      return;
    }

    this._setState(TransportState.CONNECTING);
    this._startTime = new Date();

    try {
      await this._start();
      this._setState(TransportState.CONNECTED);
      this._stats.connections++;
      this.emit('connect');
      
      if (this._config.debug) {
        console.log(`[${this.name}] Transport started`);
      }
    } catch (error) {
      this._setState(TransportState.ERROR);
      throw new TransportError(`Failed to start transport: ${error}`);
    }
  }

  /**
   * Stop the transport
   */
  async stop(): Promise<void> {
    if (this._state === TransportState.DISCONNECTED || this._state === TransportState.DISCONNECTING) {
      return;
    }

    this._setState(TransportState.DISCONNECTING);

    try {
      await this._stop();
      this._setState(TransportState.DISCONNECTED);
      this.emit('disconnect');
      
      if (this._config.debug) {
        console.log(`[${this.name}] Transport stopped`);
      }
    } catch (error) {
      this._setState(TransportState.ERROR);
      throw new TransportError(`Failed to stop transport: ${error}`);
    }
  }

  /**
   * Send a message through the transport
   */
  async send(message: string): Promise<void> {
    if (!this.isConnected()) {
      throw new TransportError('Transport is not connected');
    }

    try {
      // Apply middleware
      const processedMessage = await this._applyMiddleware(message);
      await this._send(processedMessage);
      
      this._stats.messagesSent++;
      this._stats.lastActivity = new Date();
      
      if (this._config.debug) {
        console.log(`[${this.name}] Sent message:`, processedMessage);
      }
    } catch (error) {
      this.emit('error', new TransportError(`Failed to send message: ${error}`));
      throw error;
    }
  }

  /**
   * Check if transport is connected
   */
  isConnected(): boolean {
    return this._state === TransportState.CONNECTED;
  }

  /**
   * Get transport configuration
   */
  getConfig(): TransportConfig {
    return { ...this._config };
  }

  /**
   * Add middleware
   */
  use(middleware: TransportMiddleware): void {
    this._middleware.push(middleware);
  }

  /**
   * Handle incoming message
   */
  protected _handleMessage(message: string): void {
    this._stats.messagesReceived++;
    this._stats.lastActivity = new Date();
    
    if (this._config.debug) {
      console.log(`[${this.name}] Received message:`, message);
    }
    
    this.emit('message', message);
  }

  /**
   * Set transport state
   */
  protected _setState(state: TransportState): void {
    if (this._state !== state) {
      this._state = state;
      this.emit('stateChange', state);
    }
  }

  /**
   * Apply middleware to outgoing message
   */
  private async _applyMiddleware(message: string): Promise<string> {
    let processedMessage = message;

    for (const middleware of this._middleware) {
      await new Promise<void>((resolve) => {
        middleware(processedMessage, (modifiedMessage) => {
          if (modifiedMessage !== undefined) {
            processedMessage = modifiedMessage;
          }
          resolve();
        });
      });
    }

    return processedMessage;
  }

  // Abstract methods to be implemented by subclasses
  protected abstract _start(): Promise<void>;
  protected abstract _stop(): Promise<void>;
  protected abstract _send(message: string): Promise<void>;
}
