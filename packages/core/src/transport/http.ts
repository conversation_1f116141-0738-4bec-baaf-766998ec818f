/**
 * HTTP Transport Implementation
 * 
 * Transport adapter for HTTP request/response communication
 */

import { createServer, Server, IncomingMessage, ServerResponse } from 'http';
import { BaseTransport } from './base.js';
import { HTTPTransportConfig } from './types.js';
import { ConnectionError, SendError, ConfigurationError } from './errors.js';

/**
 * HTTP transport for web applications and APIs
 */
export class HTTPTransport extends BaseTransport {
  private _server?: Server;
  private _port: number;
  private _host: string;
  private _path: string;
  private _pendingResponses = new Map<string, ServerResponse>();

  constructor(config: HTTPTransportConfig = {}) {
    super(config);
    
    this._port = config.port || 3000;
    this._host = config.host || 'localhost';
    this._path = config.path || '/mcp';
  }

  /**
   * Start HTTP server
   */
  protected async _start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this._server = createServer((req, res) => {
          this._handleRequest(req, res);
        });

        this._server.on('error', (error) => {
          this.emit('error', new ConnectionError(`HTTP server error: ${error.message}`, this.name));
        });

        this._server.listen(this._port, this._host, () => {
          if (this._config.debug) {
            console.log(`[${this.name}] HTTP server listening on ${this._host}:${this._port}${this._path}`);
          }
          resolve();
        });

      } catch (error) {
        reject(new ConnectionError(`Failed to start HTTP server: ${error}`, this.name));
      }
    });
  }

  /**
   * Stop HTTP server
   */
  protected async _stop(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this._server) {
        resolve();
        return;
      }

      // Close all pending responses
      for (const [id, res] of this._pendingResponses) {
        if (!res.headersSent) {
          res.writeHead(503, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ error: 'Server shutting down' }));
        }
      }
      this._pendingResponses.clear();

      this._server.close((error) => {
        if (error) {
          reject(new ConnectionError(`Failed to stop HTTP server: ${error.message}`, this.name));
        } else {
          this._server = undefined;
          resolve();
        }
      });
    });
  }

  /**
   * Send response to HTTP request
   */
  protected async _send(message: string): Promise<void> {
    // For HTTP transport, we need to associate responses with requests
    // This is a simplified implementation - in practice, you'd need request correlation
    const messageObj = JSON.parse(message);
    const requestId = messageObj.id;
    
    if (!requestId) {
      throw new SendError('Cannot send response without request ID', this.name);
    }

    const response = this._pendingResponses.get(String(requestId));
    if (!response) {
      throw new SendError(`No pending response found for request ID: ${requestId}`, this.name);
    }

    try {
      response.writeHead(200, {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      });
      
      response.end(message);
      this._pendingResponses.delete(String(requestId));
    } catch (error) {
      throw new SendError(`Failed to send HTTP response: ${error}`, this.name);
    }
  }

  /**
   * Handle incoming HTTP request
   */
  private async _handleRequest(req: IncomingMessage, res: ServerResponse): Promise<void> {
    // Handle CORS preflight
    if (req.method === 'OPTIONS') {
      res.writeHead(200, {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      });
      res.end();
      return;
    }

    // Only accept POST requests to the MCP path
    if (req.method !== 'POST' || req.url !== this._path) {
      res.writeHead(404, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: 'Not found' }));
      return;
    }

    try {
      const body = await this._readRequestBody(req);
      
      // Parse and validate JSON-RPC message
      const message = JSON.parse(body);
      
      // Store response object for later use
      if (message.id !== undefined) {
        this._pendingResponses.set(String(message.id), res);
        
        // Set timeout for pending responses
        setTimeout(() => {
          if (this._pendingResponses.has(String(message.id))) {
            this._pendingResponses.delete(String(message.id));
            if (!res.headersSent) {
              res.writeHead(408, { 'Content-Type': 'application/json' });
              res.end(JSON.stringify({ 
                jsonrpc: '2.0',
                error: { code: -32603, message: 'Request timeout' },
                id: message.id 
              }));
            }
          }
        }, this._config.timeout || 30000);
      }

      // Emit message for processing
      this._handleMessage(body);

      // For notifications (no id), send immediate empty response
      if (message.id === undefined) {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end();
      }

    } catch (error) {
      res.writeHead(400, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        jsonrpc: '2.0',
        error: { code: -32700, message: 'Parse error' },
        id: null
      }));
    }
  }

  /**
   * Read request body
   */
  private _readRequestBody(req: IncomingMessage): Promise<string> {
    return new Promise((resolve, reject) => {
      let body = '';
      
      req.on('data', (chunk) => {
        body += chunk.toString();
      });
      
      req.on('end', () => {
        resolve(body);
      });
      
      req.on('error', (error) => {
        reject(error);
      });
    });
  }

  /**
   * Get server address info
   */
  getAddress(): { host: string; port: number; path: string } | null {
    if (!this._server || !this.isConnected()) {
      return null;
    }

    return {
      host: this._host,
      port: this._port,
      path: this._path,
    };
  }
}

/**
 * Factory function for creating HTTP transport
 */
export function createHTTPTransport(config: HTTPTransportConfig = {}): HTTPTransport {
  return new HTTPTransport(config);
}
