/**
 * Transport Layer Errors
 * 
 * Error classes specific to transport layer operations
 */

/**
 * Base transport error
 */
export class TransportError extends Error {
  public readonly code: string;
  public readonly transportName?: string;

  constructor(message: string, code = 'TRANSPORT_ERROR', transportName?: string) {
    super(message);
    this.name = 'TransportError';
    this.code = code;
    this.transportName = transportName;

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, TransportError);
    }
  }
}

/**
 * Connection-related errors
 */
export class ConnectionError extends TransportError {
  constructor(message: string, transportName?: string) {
    super(message, 'CONNECTION_ERROR', transportName);
    this.name = 'ConnectionError';
  }
}

/**
 * Message sending errors
 */
export class SendError extends TransportError {
  constructor(message: string, transportName?: string) {
    super(message, 'SEND_ERROR', transportName);
    this.name = 'SendError';
  }
}

/**
 * Configuration errors
 */
export class ConfigurationError extends TransportError {
  constructor(message: string, transportName?: string) {
    super(message, 'CONFIGURATION_ERROR', transportName);
    this.name = 'ConfigurationError';
  }
}

/**
 * Timeout errors
 */
export class TimeoutError extends TransportError {
  constructor(message: string, transportName?: string) {
    super(message, 'TIMEOUT_ERROR', transportName);
    this.name = 'TimeoutError';
  }
}
