/**
 * Stdio Transport Implementation
 * 
 * Transport adapter for standard input/output communication
 */

import { createInterface } from 'readline';
import { BaseTransport } from './base.js';
import { StdioTransportConfig } from './types.js';
import { ConnectionError, SendError } from './errors.js';

/**
 * Stdio transport for CLI applications
 */
export class StdioTransport extends BaseTransport {
  private _input: NodeJS.ReadableStream;
  private _output: NodeJS.WritableStream;
  private _readline?: ReturnType<typeof createReadLine>;

  constructor(config: StdioTransportConfig = {}) {
    super(config);
    
    this._input = config.input || process.stdin;
    this._output = config.output || process.stdout;
  }

  /**
   * Start stdio transport
   */
  protected async _start(): Promise<void> {
    try {
      // Set up readline interface for line-by-line input
      this._readline = createInterface({
        input: this._input,
        output: undefined, // Don't echo input
        terminal: false,
      });

      // Handle incoming messages
      this._readline.on('line', (line: string) => {
        const trimmed = line.trim();
        if (trimmed) {
          this._handleMessage(trimmed);
        }
      });

      // Handle input stream errors
      this._input.on('error', (error) => {
        this.emit('error', new ConnectionError(`Input stream error: ${error.message}`, this.name));
      });

      // Handle output stream errors
      this._output.on('error', (error) => {
        this.emit('error', new ConnectionError(`Output stream error: ${error.message}`, this.name));
      });

      // Handle input stream end
      this._input.on('end', () => {
        this.emit('disconnect');
      });

      // For stdio, we're immediately "connected" once streams are set up
    } catch (error) {
      throw new ConnectionError(`Failed to initialize stdio transport: ${error}`, this.name);
    }
  }

  /**
   * Stop stdio transport
   */
  protected async _stop(): Promise<void> {
    try {
      if (this._readline) {
        this._readline.close();
        this._readline = undefined;
      }

      // Remove event listeners
      this._input.removeAllListeners();
      this._output.removeAllListeners();
    } catch (error) {
      throw new ConnectionError(`Failed to stop stdio transport: ${error}`, this.name);
    }
  }

  /**
   * Send message via stdout
   */
  protected async _send(message: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // Ensure message ends with newline for proper line-based communication
      const messageWithNewline = message.endsWith('\n') ? message : message + '\n';
      
      this._output.write(messageWithNewline, (error) => {
        if (error) {
          reject(new SendError(`Failed to write to output stream: ${error.message}`, this.name));
        } else {
          resolve();
        }
      });
    });
  }

  /**
   * Check if stdio streams are available
   */
  isConnected(): boolean {
    return super.isConnected() && 
           !this._input.destroyed && 
           !this._output.destroyed;
  }
}

/**
 * Factory function for creating stdio transport
 */
export function createStdioTransport(config: StdioTransportConfig = {}): StdioTransport {
  return new StdioTransport(config);
}
