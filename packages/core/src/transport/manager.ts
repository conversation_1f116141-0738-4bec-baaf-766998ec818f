/**
 * Transport Manager
 * 
 * Manages multiple transport instances and provides unified interface
 */

import { EventEmitter } from 'events';
import {
  Transport,
  TransportConfig,
  TransportFactory,
  TransportState,
} from './types.js';
import { StdioTransport, createStdioTransport } from './stdio.js';
import { HTTPTransport, createHTTPTransport } from './http.js';
import { SSETransport, createSSETransport } from './sse.js';
import { TransportError } from './errors.js';

/**
 * Transport type registry
 */
export const TransportRegistry = {
  stdio: createStdioTransport,
  http: createHTTPTransport,
  sse: createSSETransport,
} as const;

export type TransportType = keyof typeof TransportRegistry;

/**
 * Transport manager configuration
 */
export interface TransportManagerConfig {
  transports: Array<{
    type: TransportType;
    config?: TransportConfig;
    enabled?: boolean;
  }>;
  defaultTransport?: TransportType;
  debug?: boolean;
}

/**
 * Transport manager for handling multiple transport instances
 */
export class TransportManager extends EventEmitter {
  private _transports = new Map<string, Transport>();
  private _config: TransportManagerConfig;
  private _defaultTransport?: Transport;

  constructor(config: TransportManagerConfig) {
    super();
    this._config = config;
    this._initializeTransports();
  }

  /**
   * Initialize configured transports
   */
  private _initializeTransports(): void {
    for (const transportConfig of this._config.transports) {
      if (transportConfig.enabled === false) {
        continue;
      }

      const factory = TransportRegistry[transportConfig.type];
      if (!factory) {
        throw new TransportError(`Unknown transport type: ${transportConfig.type}`);
      }

      const transport = factory(transportConfig.config || {});
      const name = transport.name || transportConfig.type;
      
      this._transports.set(name, transport);

      // Set up event forwarding
      transport.on('message', (message) => {
        this.emit('message', message, name);
      });

      transport.on('error', (error) => {
        this.emit('error', error, name);
      });

      transport.on('connect', () => {
        this.emit('connect', name);
      });

      transport.on('disconnect', () => {
        this.emit('disconnect', name);
      });

      transport.on('stateChange', (state) => {
        this.emit('stateChange', state, name);
      });

      // Set default transport
      if (transportConfig.type === this._config.defaultTransport || !this._defaultTransport) {
        this._defaultTransport = transport;
      }

      if (this._config.debug) {
        console.log(`[TransportManager] Initialized transport: ${name}`);
      }
    }
  }

  /**
   * Start all transports
   */
  async startAll(): Promise<void> {
    const startPromises = Array.from(this._transports.values()).map(transport => 
      transport.start().catch(error => {
        this.emit('error', new TransportError(`Failed to start ${transport.name}: ${error.message}`));
      })
    );

    await Promise.allSettled(startPromises);
  }

  /**
   * Stop all transports
   */
  async stopAll(): Promise<void> {
    const stopPromises = Array.from(this._transports.values()).map(transport => 
      transport.stop().catch(error => {
        this.emit('error', new TransportError(`Failed to stop ${transport.name}: ${error.message}`));
      })
    );

    await Promise.allSettled(stopPromises);
  }

  /**
   * Start specific transport
   */
  async start(name: string): Promise<void> {
    const transport = this._transports.get(name);
    if (!transport) {
      throw new TransportError(`Transport not found: ${name}`);
    }

    await transport.start();
  }

  /**
   * Stop specific transport
   */
  async stop(name: string): Promise<void> {
    const transport = this._transports.get(name);
    if (!transport) {
      throw new TransportError(`Transport not found: ${name}`);
    }

    await transport.stop();
  }

  /**
   * Send message via specific transport
   */
  async send(message: string, transportName?: string): Promise<void> {
    const transport = transportName 
      ? this._transports.get(transportName)
      : this._defaultTransport;

    if (!transport) {
      throw new TransportError(`Transport not found: ${transportName || 'default'}`);
    }

    await transport.send(message);
  }

  /**
   * Broadcast message to all connected transports
   */
  async broadcast(message: string): Promise<void> {
    const sendPromises = Array.from(this._transports.values())
      .filter(transport => transport.isConnected())
      .map(transport => 
        transport.send(message).catch(error => {
          this.emit('error', new TransportError(`Broadcast failed for ${transport.name}: ${error.message}`));
        })
      );

    await Promise.allSettled(sendPromises);
  }

  /**
   * Get transport by name
   */
  getTransport(name: string): Transport | undefined {
    return this._transports.get(name);
  }

  /**
   * Get all transport names
   */
  getTransportNames(): string[] {
    return Array.from(this._transports.keys());
  }

  /**
   * Get connected transports
   */
  getConnectedTransports(): Array<{ name: string; transport: Transport }> {
    return Array.from(this._transports.entries())
      .filter(([, transport]) => transport.isConnected())
      .map(([name, transport]) => ({ name, transport }));
  }

  /**
   * Get transport statistics
   */
  getStats(): Record<string, any> {
    const stats: Record<string, any> = {};
    
    for (const [name, transport] of this._transports) {
      stats[name] = {
        state: transport.state,
        connected: transport.isConnected(),
        stats: transport.stats,
      };
    }

    return stats;
  }

  /**
   * Add custom transport
   */
  addTransport(name: string, transport: Transport): void {
    if (this._transports.has(name)) {
      throw new TransportError(`Transport already exists: ${name}`);
    }

    this._transports.set(name, transport);

    // Set up event forwarding
    transport.on('message', (message) => {
      this.emit('message', message, name);
    });

    transport.on('error', (error) => {
      this.emit('error', error, name);
    });

    transport.on('connect', () => {
      this.emit('connect', name);
    });

    transport.on('disconnect', () => {
      this.emit('disconnect', name);
    });

    if (this._config.debug) {
      console.log(`[TransportManager] Added transport: ${name}`);
    }
  }

  /**
   * Remove transport
   */
  async removeTransport(name: string): Promise<void> {
    const transport = this._transports.get(name);
    if (!transport) {
      return;
    }

    if (transport.isConnected()) {
      await transport.stop();
    }

    transport.removeAllListeners();
    this._transports.delete(name);

    if (this._config.debug) {
      console.log(`[TransportManager] Removed transport: ${name}`);
    }
  }
}
