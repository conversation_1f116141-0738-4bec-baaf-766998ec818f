/**
 * Transport Layer Types
 * 
 * Type definitions for transport layer abstractions
 */

import { EventEmitter } from 'events';

/**
 * Transport connection states
 */
export enum TransportState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTING = 'disconnecting',
  ERROR = 'error',
}

/**
 * Transport events
 */
export interface TransportEvents {
  message: (message: string) => void;
  error: (error: Error) => void;
  connect: () => void;
  disconnect: () => void;
  stateChange: (state: TransportState) => void;
}

/**
 * Base transport configuration
 */
export interface TransportConfig {
  name?: string;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
  debug?: boolean;
}

/**
 * Stdio transport configuration
 */
export interface StdioTransportConfig extends TransportConfig {
  input?: NodeJS.ReadableStream;
  output?: NodeJS.WritableStream;
}

/**
 * HTTP transport configuration
 */
export interface HTTPTransportConfig extends TransportConfig {
  port?: number;
  host?: string;
  path?: string;
  cors?: {
    origin?: string | string[];
    credentials?: boolean;
  };
  middleware?: Array<(req: any, res: any, next: any) => void>;
}

/**
 * SSE transport configuration
 */
export interface SSETransportConfig extends HTTPTransportConfig {
  heartbeatInterval?: number;
  maxConnections?: number;
}

/**
 * Transport statistics
 */
export interface TransportStats {
  messagesReceived: number;
  messagesSent: number;
  errors: number;
  connections: number;
  uptime: number;
  lastActivity: Date | null;
}

/**
 * Transport interface
 */
export interface Transport extends EventEmitter {
  readonly name: string;
  readonly state: TransportState;
  readonly stats: TransportStats;

  /**
   * Start the transport
   */
  start(): Promise<void>;

  /**
   * Stop the transport
   */
  stop(): Promise<void>;

  /**
   * Send a message
   */
  send(message: string): Promise<void>;

  /**
   * Check if transport is connected
   */
  isConnected(): boolean;

  /**
   * Get transport configuration
   */
  getConfig(): TransportConfig;
}

/**
 * Transport factory function type
 */
export type TransportFactory<T extends TransportConfig = TransportConfig> = (
  config: T
) => Transport;

/**
 * Transport middleware function type
 */
export type TransportMiddleware = (
  message: string,
  next: (message?: string) => void
) => void;
