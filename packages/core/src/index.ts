/**
 * @mcp-framework/core
 *
 * Core framework for building MCP (Model Context Protocol) services
 */

// Core exports
export * from './types/index.js';
export * from './protocol/index.js';
export * from './transport/index.js';
export * from './utils/index.js';

// Handler interfaces
export * from './handlers.js';

// Main service class
export { MCPService } from './service.js';

// Decorators and utilities
export {
  tool,
  resource,
  prompt,
  mcpService,
  getToolDefinitions,
  getResourceDefinitions,
  getPromptDefinitions,
  getServiceConfig,
  hasMCPDecorators,
} from './decorators.js';

// Factory functions
export {
  createMCPService,
  quickService,
  createTool,
  createResource,
  createPrompt,
  createFileResource,
  createHttpTool,
  ServiceBuilder,
  CommonSchemas,
  Examples,
} from './factory.js';
