/**
 * @mcp-framework/core
 * 
 * Core framework for building MCP (Model Context Protocol) services
 */

// Core exports
export * from './types/index.js';
export * from './protocol/index.js';
export * from './transport/index.js';
export * from './utils/index.js';

// Main service class
export { MCPService } from './service.js';

// Decorators and utilities
export { tool, resource, prompt } from './decorators.js';
export { createMCPService } from './factory.js';
