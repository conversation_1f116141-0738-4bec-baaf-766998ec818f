{"name": "@mcp-framework/core", "version": "0.1.0", "description": "Core framework for building MCP (Model Context Protocol) services", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./transport": {"import": "./dist/transport/index.js", "types": "./dist/transport/index.d.ts"}, "./protocol": {"import": "./dist/protocol/index.js", "types": "./dist/protocol/index.d.ts"}}, "files": ["dist", "README.md"], "scripts": {"build": "vite build", "dev": "vite build --watch", "test": "vitest", "test:run": "vitest run", "typecheck": "tsc --noEmit", "clean": "rm -rf dist"}, "keywords": ["mcp", "model-context-protocol", "framework", "typescript"], "author": "MCP Framework Team", "license": "MIT", "dependencies": {"zod": "^3.22.4", "ws": "^8.16.0", "reflect-metadata": "^0.1.13"}, "devDependencies": {"@types/ws": "^8.5.10", "vite": "^5.0.12", "vite-plugin-dts": "^3.7.2"}, "peerDependencies": {"typescript": ">=5.0.0"}}