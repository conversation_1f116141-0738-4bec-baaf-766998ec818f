# @mcp-framework/cli

> 🛠️ Command line interface for MCP Framework

The official CLI tool for creating, developing, and building MCP (Model Context Protocol) services with ease.

## Installation

```bash
# Install globally
npm install -g @mcp-framework/cli

# Or use with npx
npx @mcp-framework/cli --help
```

## Quick Start

```bash
# Create a new MCP project
mcp init my-service

# Navigate to project
cd my-service

# Start development server
mcp dev

# Build for production
mcp build

# Run tests
mcp test
```

## Commands

### `mcp init [project-name]`

Initialize a new MCP project with interactive setup.

**Options:**
- `-t, --template <template>` - Template to use (default: basic)
- `-n, --name <name>` - Project name
- `-y, --yes` - Skip prompts and use defaults
- `--skip-install` - Skip npm install
- `--skip-git` - Skip git initialization

**Examples:**
```bash
# Interactive setup
mcp init

# Quick setup with defaults
mcp init my-service --yes

# Use specific template
mcp init my-service --template advanced

# Skip dependency installation
mcp init my-service --skip-install
```

### `mcp dev`

Start development server with hot reload and real-time debugging.

**Options:**
- `-p, --port <port>` - Port number (default: 3000)
- `-h, --host <host>` - Host address (default: localhost)
- `-w, --watch <patterns...>` - Additional watch patterns
- `-d, --debug` - Enable debug mode
- `--no-restart` - Disable automatic restart on file changes

**Examples:**
```bash
# Start with default settings
mcp dev

# Custom port and host
mcp dev --port 8080 --host 0.0.0.0

# Enable debug mode
mcp dev --debug

# Watch additional files
mcp dev --watch "config/**/*" "templates/**/*"
```

**Features:**
- 🔥 Hot reload on file changes
- 🐛 Real-time error display
- 📊 Live service statistics
- 🔍 Protocol message debugging
- 🚀 Multi-transport testing

### `mcp build`

Build project for production with optimizations.

**Options:**
- `-o, --out-dir <dir>` - Output directory (default: dist)
- `-t, --target <target>` - Build target (default: node18)
- `-f, --format <format>` - Output format: esm, cjs (default: esm)
- `-m, --minify` - Minify output
- `-s, --sourcemap` - Generate source maps
- `-w, --watch` - Watch for changes
- `--clean` - Clean output directory before build

**Examples:**
```bash
# Basic build
mcp build

# Build with minification
mcp build --minify

# Build for specific Node.js version
mcp build --target node20

# Build in CommonJS format
mcp build --format cjs

# Watch mode for development
mcp build --watch
```

**Features:**
- ⚡ Fast builds with esbuild
- 📦 Bundle optimization
- 🗺️ Source map generation
- 📊 Build statistics
- 🎯 Multiple output formats

### `mcp test`

Run tests with coverage reporting and watch mode.

**Options:**
- `-w, --watch` - Watch for changes and re-run tests
- `-c, --coverage` - Generate coverage report
- `-v, --verbose` - Verbose output
- `-p, --pattern <pattern>` - Test file pattern
- `-t, --timeout <ms>` - Test timeout (default: 5000ms)
- `--bail` - Stop on first test failure

**Examples:**
```bash
# Run all tests
mcp test

# Run with coverage
mcp test --coverage

# Watch mode
mcp test --watch

# Run specific test pattern
mcp test --pattern "**/*.unit.test.ts"

# Verbose output with custom timeout
mcp test --verbose --timeout 10000
```

**Supported Test Runners:**
- ✅ Vitest (recommended)
- ✅ Jest
- ✅ Mocha

## Project Structure

When you create a new project with `mcp init`, you get:

```
my-service/
├── src/
│   ├── index.ts          # Main entry point
│   └── types.ts          # Type definitions
├── mcp.config.yaml       # MCP configuration
├── package.json          # Node.js package config
├── tsconfig.json         # TypeScript config
├── README.md             # Project documentation
└── .gitignore           # Git ignore rules
```

## Configuration

### mcp.config.yaml

The main configuration file for your MCP service:

```yaml
name: my-service
version: 1.0.0
description: My awesome MCP service
transport: [stdio, http]

tools:
  - name: hello
    description: Say hello
    inputSchema:
      type: object
      properties:
        name: { type: string }
      required: [name]
    handler: ./src/handlers/hello.js

resources:
  - pattern: file://**
    name: File System
    description: Access to files
    handler: ./src/handlers/files.js

build:
  outDir: dist
  target: node18
  format: esm
  minify: false
  sourcemap: true

dev:
  port: 3000
  debug: true
  watch: [src/**/*]
```

## Templates

### Built-in Templates

- **basic** - Simple MCP service with one tool
- **advanced** - Full-featured service with tools, resources, and prompts
- **file-ops** - File operations service
- **web-utils** - Web utilities and HTTP client

### Custom Templates

You can create custom templates by placing them in the `templates/` directory:

```
templates/
└── my-template/
    ├── template.json     # Template configuration
    └── files/           # Template files
        ├── src/
        └── package.json
```

## Development Workflow

1. **Initialize** - `mcp init my-service`
2. **Develop** - `mcp dev` (with hot reload)
3. **Test** - `mcp test --watch`
4. **Build** - `mcp build`
5. **Deploy** - Use built files in `dist/`

## Debugging

### Debug Mode

Enable debug mode for detailed logging:

```bash
mcp dev --debug
```

### Environment Variables

- `MCP_DEBUG=1` - Enable debug logging
- `NODE_ENV=development` - Development mode
- `MCP_LOG_LEVEL=debug` - Set log level

### Protocol Debugging

The dev server provides real-time protocol message debugging:

- 📨 Request/response logging
- ⚡ Performance metrics
- 🔍 Error tracking
- 📊 Usage statistics

## Troubleshooting

### Common Issues

**Build Failures:**
```bash
# Clean and rebuild
mcp build --clean

# Check for TypeScript errors
npx tsc --noEmit
```

**Development Server Issues:**
```bash
# Clear cache and restart
rm -rf node_modules/.cache
mcp dev
```

**Test Failures:**
```bash
# Run tests with verbose output
mcp test --verbose

# Check test configuration
cat package.json | grep -A 10 '"scripts"'
```

### Getting Help

- 📖 [Documentation](https://mcp-framework.dev)
- 🐛 [Issues](https://github.com/mcp-framework/mcp-framework/issues)
- 💬 [Discussions](https://github.com/mcp-framework/mcp-framework/discussions)

## License

MIT
