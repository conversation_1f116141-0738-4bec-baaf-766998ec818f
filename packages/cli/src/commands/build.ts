/**
 * Build Command
 * 
 * Build project for production
 */

import { Command } from 'commander';
import chalk from 'chalk';
import ora from 'ora';
import { build } from 'esbuild';
import { existsSync, rmSync, statSync } from 'fs';
import { join, resolve } from 'path';
import { loadConfig, isMCPProject } from '../utils/config.js';
import { BuildResult } from '../types.js';

/**
 * Build command options
 */
interface BuildOptions {
  outDir?: string;
  target?: string;
  format?: 'esm' | 'cjs';
  minify?: boolean;
  sourcemap?: boolean;
  watch?: boolean;
  clean?: boolean;
}

/**
 * Create build command
 */
export const buildCommand = new Command('build')
  .description('Build project for production')
  .option('-o, --out-dir <dir>', 'Output directory')
  .option('-t, --target <target>', 'Build target (e.g., node18, node20)')
  .option('-f, --format <format>', 'Output format (esm, cjs)', 'esm')
  .option('-m, --minify', 'Minify output')
  .option('-s, --sourcemap', 'Generate source maps')
  .option('-w, --watch', 'Watch for changes')
  .option('--clean', 'Clean output directory before build')
  .action(async (options: BuildOptions) => {
    try {
      const result = await buildProject(options);
      
      if (result.success) {
        console.log(chalk.green('\n✅ Build completed successfully'));
        console.log(chalk.gray(`   Output: ${result.outputFiles.join(', ')}`));
        console.log(chalk.gray(`   Duration: ${result.duration}ms`));
        
        if (result.warnings.length > 0) {
          console.log(chalk.yellow('\n⚠️  Warnings:'));
          result.warnings.forEach(warning => console.log(chalk.yellow(`   ${warning}`)));
        }
      } else {
        console.log(chalk.red('\n❌ Build failed'));
        result.errors.forEach(error => console.log(chalk.red(`   ${error}`)));
        process.exit(1);
      }
    } catch (error) {
      console.error(chalk.red('Error:'), error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  });

/**
 * Build project
 */
async function buildProject(options: BuildOptions): Promise<BuildResult> {
  const startTime = Date.now();
  const cwd = process.cwd();

  // Check if this is an MCP project
  if (!isMCPProject(cwd)) {
    throw new Error('Not an MCP project. Run "mcp init" to create a new project.');
  }

  // Load project configuration
  const config = loadConfig(cwd);
  if (!config) {
    throw new Error('No MCP configuration found. Make sure mcp.config.yaml exists.');
  }

  // Merge options with config
  const buildConfig = {
    ...config.build,
    outDir: options.outDir || config.build?.outDir || 'dist',
    target: options.target || config.build?.target || 'node18',
    format: options.format || config.build?.format || 'esm',
    minify: options.minify !== undefined ? options.minify : (config.build?.minify || false),
    sourcemap: options.sourcemap !== undefined ? options.sourcemap : (config.build?.sourcemap !== false),
    external: config.build?.external || ['@mcp-framework/core'],
  };

  const result: BuildResult = {
    success: false,
    outputFiles: [],
    errors: [],
    warnings: [],
    duration: 0,
  };

  const spinner = ora('Building project...').start();

  try {
    // Find entry point
    const entryPoint = findEntryPoint(cwd);
    if (!entryPoint) {
      throw new Error('Entry point not found. Expected src/index.ts or src/index.js');
    }

    // Clean output directory if requested
    if (options.clean && existsSync(buildConfig.outDir)) {
      rmSync(buildConfig.outDir, { recursive: true, force: true });
      spinner.text = 'Cleaned output directory';
    }

    // Build with esbuild
    const buildResult = await build({
      entryPoints: [entryPoint],
      bundle: true,
      platform: 'node',
      target: buildConfig.target,
      format: buildConfig.format === 'cjs' ? 'cjs' : 'esm',
      outdir: buildConfig.outDir,
      sourcemap: buildConfig.sourcemap,
      minify: buildConfig.minify,
      external: buildConfig.external,
      metafile: true,
      write: true,
      logLevel: 'silent',
      ...(options.watch && { watch: true }),
    });

    // Process build results
    if (buildResult.errors.length > 0) {
      result.errors = buildResult.errors.map(error => error.text);
    }

    if (buildResult.warnings.length > 0) {
      result.warnings = buildResult.warnings.map(warning => warning.text);
    }

    // Get output files
    if (buildResult.metafile) {
      result.outputFiles = Object.keys(buildResult.metafile.outputs);
    }

    result.success = buildResult.errors.length === 0;
    result.duration = Date.now() - startTime;

    if (result.success) {
      spinner.succeed('Build completed');
      
      // Show build stats
      if (buildResult.metafile) {
        showBuildStats(buildResult.metafile, buildConfig.outDir);
      }
    } else {
      spinner.fail('Build failed');
    }

    return result;
  } catch (error) {
    spinner.fail('Build failed');
    result.errors.push(error instanceof Error ? error.message : String(error));
    result.duration = Date.now() - startTime;
    return result;
  }
}

/**
 * Find entry point file
 */
function findEntryPoint(cwd: string): string | null {
  const candidates = [
    'src/index.ts',
    'src/index.js',
    'src/main.ts',
    'src/main.js',
    'index.ts',
    'index.js',
  ];

  for (const candidate of candidates) {
    const fullPath = join(cwd, candidate);
    if (existsSync(fullPath)) {
      return fullPath;
    }
  }

  return null;
}

/**
 * Show build statistics
 */
function showBuildStats(metafile: any, outDir: string): void {
  console.log(chalk.blue('\n📊 Build Statistics:'));

  // Output files
  const outputs = Object.entries(metafile.outputs);
  let totalSize = 0;

  for (const [file, info] of outputs) {
    const stats = existsSync(file) ? statSync(file) : null;
    const size = stats ? stats.size : (info as any).bytes || 0;
    totalSize += size;

    console.log(chalk.gray(`   ${file.replace(outDir + '/', '')}: ${formatBytes(size)}`));
  }

  console.log(chalk.gray(`   Total: ${formatBytes(totalSize)}`));

  // Bundle analysis
  if (metafile.inputs) {
    const inputCount = Object.keys(metafile.inputs).length;
    console.log(chalk.gray(`   Bundled ${inputCount} files`));
  }
}

/**
 * Format bytes to human readable string
 */
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

/**
 * Watch mode handler
 */
function setupWatchMode(): void {
  console.log(chalk.blue('\n👀 Watching for changes...'));
  console.log(chalk.gray('Press Ctrl+C to stop'));

  process.on('SIGINT', () => {
    console.log(chalk.yellow('\n📦 Stopping watch mode...'));
    process.exit(0);
  });
}
