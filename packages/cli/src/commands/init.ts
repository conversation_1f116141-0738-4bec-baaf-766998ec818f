/**
 * Init Command
 * 
 * Initialize a new MCP project
 */

import { Command } from 'commander';
import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';
import { existsSync, mkdirSync } from 'fs';
import { join, resolve, basename } from 'path';
import { execa } from 'execa';
import boxen from 'boxen';
import gradient from 'gradient-string';
import { generateFromTemplate, BUILTIN_TEMPLATES, getAvailableTemplates } from '../utils/template.js';
import { saveConfig } from '../utils/config.js';

/**
 * Init command options
 */
interface InitOptions {
  template?: string;
  name?: string;
  yes?: boolean;
  skipInstall?: boolean;
  skipGit?: boolean;
}

/**
 * Create init command
 */
export const initCommand = new Command('init')
  .description('Initialize a new MCP project')
  .argument('[project-name]', 'Project name')
  .option('-t, --template <template>', 'Template to use', 'basic')
  .option('-n, --name <name>', 'Project name')
  .option('-y, --yes', 'Skip prompts and use defaults')
  .option('--skip-install', 'Skip npm install')
  .option('--skip-git', 'Skip git initialization')
  .action(async (projectName: string | undefined, options: InitOptions) => {
    try {
      await initProject(projectName, options);
    } catch (error) {
      console.error(chalk.red('Error:'), error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  });

/**
 * Initialize project
 */
async function initProject(projectName: string | undefined, options: InitOptions): Promise<void> {
  console.log(gradient.rainbow.multiline([
    '╔══════════════════════════════════════╗',
    '║        MCP Framework CLI             ║',
    '║   Initialize a new MCP project       ║',
    '╚══════════════════════════════════════╝',
  ].join('\n')));

  // Determine project name and directory
  const name = projectName || options.name;
  let projectDir: string;

  if (name) {
    projectDir = resolve(process.cwd(), name);
  } else if (!options.yes) {
    const { projectName: inputName } = await inquirer.prompt([
      {
        type: 'input',
        name: 'projectName',
        message: 'Project name:',
        validate: (input: string) => input.length > 0 || 'Project name is required',
      },
    ]);
    projectDir = resolve(process.cwd(), inputName);
  } else {
    throw new Error('Project name is required');
  }

  const projectBaseName = basename(projectDir);

  // Check if directory already exists
  if (existsSync(projectDir)) {
    const { overwrite } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'overwrite',
        message: `Directory ${projectBaseName} already exists. Overwrite?`,
        default: false,
      },
    ]);

    if (!overwrite) {
      console.log(chalk.yellow('Aborted.'));
      return;
    }
  }

  // Get available templates
  const builtinTemplates = Object.keys(BUILTIN_TEMPLATES);
  const customTemplates = getAvailableTemplates(join(__dirname, '../templates'));
  const allTemplates = [
    ...builtinTemplates.map(name => ({ name, builtin: true })),
    ...customTemplates.map(({ name }) => ({ name, builtin: false })),
  ];

  // Select template
  let selectedTemplate = options.template || 'basic';
  
  if (!options.yes && allTemplates.length > 1) {
    const { template } = await inquirer.prompt([
      {
        type: 'list',
        name: 'template',
        message: 'Select a template:',
        choices: allTemplates.map(t => ({
          name: t.builtin ? `${t.name} (built-in)` : t.name,
          value: t.name,
        })),
        default: selectedTemplate,
      },
    ]);
    selectedTemplate = template;
  }

  // Get template configuration
  const templateConfig = BUILTIN_TEMPLATES[selectedTemplate as keyof typeof BUILTIN_TEMPLATES];
  if (!templateConfig) {
    throw new Error(`Template not found: ${selectedTemplate}`);
  }

  // Collect template variables
  let variables: Record<string, any> = {
    name: projectBaseName,
    version: '1.0.0',
    description: `An MCP service built with MCP Framework`,
    author: '',
    license: 'MIT',
    transport: ['stdio'],
  };

  if (!options.yes && templateConfig.prompts) {
    const answers = await inquirer.prompt(
      templateConfig.prompts.map(prompt => ({
        ...prompt,
        default: prompt.name === 'name' ? projectBaseName : prompt.default,
      }))
    );
    variables = { ...variables, ...answers };
  }

  // Create project directory
  const spinner = ora('Creating project...').start();
  
  try {
    if (!existsSync(projectDir)) {
      mkdirSync(projectDir, { recursive: true });
    }

    // Generate project from template
    await generateFromTemplate('', projectDir, variables);

    // Create project files manually for built-in templates
    if (selectedTemplate === 'basic') {
      await generateBasicTemplate(projectDir, variables);
    }

    spinner.succeed('Project created successfully');
  } catch (error) {
    spinner.fail('Failed to create project');
    throw error;
  }

  // Install dependencies
  if (!options.skipInstall) {
    const installSpinner = ora('Installing dependencies...').start();
    
    try {
      await execa('npm', ['install'], { cwd: projectDir });
      installSpinner.succeed('Dependencies installed');
    } catch (error) {
      installSpinner.fail('Failed to install dependencies');
      console.log(chalk.yellow('You can install dependencies manually by running:'));
      console.log(chalk.cyan(`  cd ${projectBaseName} && npm install`));
    }
  }

  // Initialize git repository
  if (!options.skipGit) {
    const gitSpinner = ora('Initializing git repository...').start();
    
    try {
      await execa('git', ['init'], { cwd: projectDir });
      await execa('git', ['add', '.'], { cwd: projectDir });
      await execa('git', ['commit', '-m', 'Initial commit'], { cwd: projectDir });
      gitSpinner.succeed('Git repository initialized');
    } catch (error) {
      gitSpinner.fail('Failed to initialize git repository');
      console.log(chalk.yellow('You can initialize git manually by running:'));
      console.log(chalk.cyan(`  cd ${projectBaseName} && git init`));
    }
  }

  // Show success message
  console.log('\n' + boxen(
    chalk.green.bold('🎉 Project created successfully!') + '\n\n' +
    chalk.white('Next steps:') + '\n' +
    chalk.cyan(`  cd ${projectBaseName}`) + '\n' +
    chalk.cyan('  npm run dev') + '\n\n' +
    chalk.white('Available commands:') + '\n' +
    chalk.gray('  npm run dev    - Start development server') + '\n' +
    chalk.gray('  npm run build  - Build for production') + '\n' +
    chalk.gray('  npm run test   - Run tests') + '\n' +
    chalk.gray('  npm start      - Start production server'),
    {
      padding: 1,
      margin: 1,
      borderStyle: 'round',
      borderColor: 'green',
    }
  ));
}

/**
 * Generate basic template files
 */
async function generateBasicTemplate(projectDir: string, variables: Record<string, any>): Promise<void> {
  const template = BUILTIN_TEMPLATES.basic;
  
  for (const file of template.files) {
    const filePath = join(projectDir, file.path);
    const fileDir = require('path').dirname(filePath);
    
    // Ensure directory exists
    if (!existsSync(fileDir)) {
      mkdirSync(fileDir, { recursive: true });
    }
    
    // Process template content
    let content = file.content;
    if (file.template) {
      content = replaceTemplateVariables(content, variables);
    }
    
    require('fs').writeFileSync(filePath, content, 'utf-8');
  }
}

/**
 * Simple template variable replacement
 */
function replaceTemplateVariables(content: string, variables: Record<string, any>): string {
  let result = content;
  
  for (const [key, value] of Object.entries(variables)) {
    const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
    result = result.replace(regex, String(value));
  }
  
  // Handle arrays in transport
  if (variables.transport && Array.isArray(variables.transport)) {
    result = result.replace(
      /{{#each transport}}'{{this}}'{{#unless @last}}, {{\/unless}}{{\/each}}/g,
      variables.transport.map((t: string) => `'${t}'`).join(', ')
    );
  }
  
  return result;
}
