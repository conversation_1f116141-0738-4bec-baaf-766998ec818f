/**
 * Dev Command
 * 
 * Start development server with hot reload
 */

import { Command } from 'commander';
import chalk from 'chalk';
import ora from 'ora';
import chokidar from 'chokidar';
import { spawn, ChildProcess } from 'child_process';
import { join, resolve } from 'path';
import { existsSync } from 'fs';
import { build } from 'esbuild';
import { loadConfig, isMCPProject } from '../utils/config.js';
import { ProjectConfig, DevServerStatus } from '../types.js';

/**
 * Dev command options
 */
interface DevOptions {
  port?: number;
  host?: string;
  watch?: string[];
  debug?: boolean;
  noRestart?: boolean;
}

/**
 * Development server state
 */
let devServer: {
  process?: ChildProcess;
  status: DevServerStatus;
  config: ProjectConfig;
  buildPromise?: Promise<void>;
} | null = null;

/**
 * Create dev command
 */
export const devCommand = new Command('dev')
  .description('Start development server with hot reload')
  .option('-p, --port <port>', 'Port number', '3000')
  .option('-h, --host <host>', 'Host address', 'localhost')
  .option('-w, --watch <patterns...>', 'Additional watch patterns')
  .option('-d, --debug', 'Enable debug mode')
  .option('--no-restart', 'Disable automatic restart on file changes')
  .action(async (options: DevOptions) => {
    try {
      await startDevServer(options);
    } catch (error) {
      console.error(chalk.red('Error:'), error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  });

/**
 * Start development server
 */
async function startDevServer(options: DevOptions): Promise<void> {
  const cwd = process.cwd();

  // Check if this is an MCP project
  if (!isMCPProject(cwd)) {
    throw new Error('Not an MCP project. Run "mcp init" to create a new project.');
  }

  // Load project configuration
  const config = loadConfig(cwd);
  if (!config) {
    throw new Error('No MCP configuration found. Make sure mcp.config.yaml exists.');
  }

  // Merge options with config
  const devConfig = {
    ...config.dev,
    port: options.port ? parseInt(options.port) : config.dev?.port || 3000,
    host: options.host || config.dev?.host || 'localhost',
    debug: options.debug || config.dev?.debug || false,
    watch: options.watch || config.dev?.watch || ['src/**/*'],
    restart: options.noRestart ? false : (config.dev?.restart !== false),
  };

  // Initialize dev server state
  devServer = {
    status: {
      running: false,
      port: devConfig.port,
      host: devConfig.host,
      transports: config.transport,
      errors: [],
    },
    config,
  };

  console.log(chalk.blue.bold('🚀 Starting MCP development server...\n'));

  // Initial build
  await buildProject();

  // Start the service
  await startService();

  // Set up file watcher
  if (devConfig.restart) {
    setupFileWatcher(devConfig.watch, devConfig.ignore || ['node_modules/**', 'dist/**']);
  }

  // Handle process termination
  process.on('SIGINT', async () => {
    console.log(chalk.yellow('\n📦 Shutting down development server...'));
    await stopService();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    await stopService();
    process.exit(0);
  });

  // Keep the process running
  console.log(chalk.green('✅ Development server started'));
  console.log(chalk.gray(`   Service: ${config.name} v${config.version}`));
  console.log(chalk.gray(`   Transports: ${config.transport.join(', ')}`));
  if (config.transport.includes('http')) {
    console.log(chalk.gray(`   HTTP: http://${devConfig.host}:${devConfig.port}`));
  }
  console.log(chalk.gray(`   Debug: ${devConfig.debug ? 'enabled' : 'disabled'}`));
  console.log(chalk.gray(`   Watching: ${devConfig.watch.join(', ')}`));
  console.log(chalk.cyan('\n📝 Press Ctrl+C to stop\n'));
}

/**
 * Build project
 */
async function buildProject(): Promise<void> {
  if (!devServer) return;

  const spinner = ora('Building project...').start();
  
  try {
    const buildConfig = devServer.config.build || {};
    const entryPoint = join(process.cwd(), 'src/index.ts');
    
    if (!existsSync(entryPoint)) {
      throw new Error('Entry point not found: src/index.ts');
    }

    devServer.buildPromise = build({
      entryPoints: [entryPoint],
      bundle: true,
      platform: 'node',
      target: buildConfig.target || 'node18',
      format: buildConfig.format === 'cjs' ? 'cjs' : 'esm',
      outdir: buildConfig.outDir || 'dist',
      sourcemap: buildConfig.sourcemap !== false,
      minify: buildConfig.minify || false,
      external: buildConfig.external || ['@mcp-framework/core'],
      logLevel: 'silent',
    });

    await devServer.buildPromise;
    spinner.succeed('Project built successfully');
    devServer.status.errors = [];
  } catch (error) {
    spinner.fail('Build failed');
    const errorMessage = error instanceof Error ? error.message : String(error);
    devServer.status.errors = [errorMessage];
    console.error(chalk.red('Build error:'), errorMessage);
    throw error;
  }
}

/**
 * Start service process
 */
async function startService(): Promise<void> {
  if (!devServer) return;

  const spinner = ora('Starting service...').start();

  try {
    const entryPoint = join(process.cwd(), 'dist/index.js');
    
    if (!existsSync(entryPoint)) {
      throw new Error('Built entry point not found. Build may have failed.');
    }

    // Stop existing process
    if (devServer.process) {
      devServer.process.kill();
    }

    // Start new process
    devServer.process = spawn('node', [entryPoint], {
      stdio: ['pipe', 'pipe', 'pipe'],
      env: {
        ...process.env,
        NODE_ENV: 'development',
        MCP_DEBUG: devServer.config.dev?.debug ? '1' : '0',
      },
    });

    // Handle process output
    devServer.process.stdout?.on('data', (data) => {
      process.stdout.write(chalk.gray('[SERVICE] ') + data.toString());
    });

    devServer.process.stderr?.on('data', (data) => {
      process.stderr.write(chalk.red('[SERVICE] ') + data.toString());
    });

    devServer.process.on('exit', (code) => {
      if (devServer) {
        devServer.status.running = false;
        if (code !== 0 && code !== null) {
          console.log(chalk.red(`Service exited with code ${code}`));
        }
      }
    });

    devServer.process.on('error', (error) => {
      console.error(chalk.red('Service error:'), error.message);
      if (devServer) {
        devServer.status.errors.push(error.message);
      }
    });

    devServer.status.running = true;
    devServer.status.lastRestart = new Date();
    spinner.succeed('Service started');
  } catch (error) {
    spinner.fail('Failed to start service');
    throw error;
  }
}

/**
 * Stop service process
 */
async function stopService(): Promise<void> {
  if (!devServer?.process) return;

  return new Promise((resolve) => {
    if (!devServer?.process) {
      resolve();
      return;
    }

    devServer.process.on('exit', () => {
      if (devServer) {
        devServer.status.running = false;
      }
      resolve();
    });

    devServer.process.kill('SIGTERM');
    
    // Force kill after 5 seconds
    setTimeout(() => {
      if (devServer?.process) {
        devServer.process.kill('SIGKILL');
      }
      resolve();
    }, 5000);
  });
}

/**
 * Setup file watcher
 */
function setupFileWatcher(watchPatterns: string[], ignorePatterns: string[]): void {
  const watcher = chokidar.watch(watchPatterns, {
    ignored: ignorePatterns,
    ignoreInitial: true,
    persistent: true,
  });

  let restartTimeout: NodeJS.Timeout;

  const handleFileChange = (path: string) => {
    console.log(chalk.yellow(`📝 File changed: ${path}`));
    
    // Debounce restarts
    clearTimeout(restartTimeout);
    restartTimeout = setTimeout(async () => {
      try {
        console.log(chalk.blue('🔄 Restarting service...'));
        
        // Wait for any ongoing build to complete
        if (devServer?.buildPromise) {
          await devServer.buildPromise;
        }
        
        await buildProject();
        await stopService();
        await startService();
        
        console.log(chalk.green('✅ Service restarted'));
      } catch (error) {
        console.error(chalk.red('❌ Restart failed:'), error instanceof Error ? error.message : String(error));
      }
    }, 500);
  };

  watcher.on('change', handleFileChange);
  watcher.on('add', handleFileChange);
  watcher.on('unlink', handleFileChange);

  watcher.on('error', (error) => {
    console.error(chalk.red('Watcher error:'), error);
  });

  console.log(chalk.gray(`👀 Watching for changes in: ${watchPatterns.join(', ')}`));
}
