/**
 * Test Command
 * 
 * Run tests for MCP project
 */

import { Command } from 'commander';
import chalk from 'chalk';
import ora from 'ora';
import { execa } from 'execa';
import { existsSync } from 'fs';
import { join } from 'path';
import { loadConfig, isMCPProject, getPackageInfo } from '../utils/config.js';
import { TestResult } from '../types.js';

/**
 * Test command options
 */
interface TestOptions {
  watch?: boolean;
  coverage?: boolean;
  verbose?: boolean;
  pattern?: string;
  timeout?: number;
  bail?: boolean;
}

/**
 * Create test command
 */
export const testCommand = new Command('test')
  .description('Run tests')
  .option('-w, --watch', 'Watch for changes and re-run tests')
  .option('-c, --coverage', 'Generate coverage report')
  .option('-v, --verbose', 'Verbose output')
  .option('-p, --pattern <pattern>', 'Test file pattern')
  .option('-t, --timeout <ms>', 'Test timeout in milliseconds', '5000')
  .option('--bail', 'Stop on first test failure')
  .action(async (options: TestOptions) => {
    try {
      const result = await runTests(options);
      
      if (result.success) {
        console.log(chalk.green('\n✅ All tests passed'));
        showTestSummary(result);
      } else {
        console.log(chalk.red('\n❌ Some tests failed'));
        showTestSummary(result);
        process.exit(1);
      }
    } catch (error) {
      console.error(chalk.red('Error:'), error instanceof Error ? error.message : String(error));
      process.exit(1);
    }
  });

/**
 * Run tests
 */
async function runTests(options: TestOptions): Promise<TestResult> {
  const cwd = process.cwd();

  // Check if this is an MCP project
  if (!isMCPProject(cwd)) {
    throw new Error('Not an MCP project. Run "mcp init" to create a new project.');
  }

  // Load project configuration
  const config = loadConfig(cwd);
  const packageInfo = getPackageInfo(cwd);

  if (!packageInfo) {
    throw new Error('package.json not found');
  }

  const startTime = Date.now();
  const result: TestResult = {
    success: false,
    passed: 0,
    failed: 0,
    skipped: 0,
    duration: 0,
  };

  // Determine test runner
  const testRunner = detectTestRunner(cwd, packageInfo);
  if (!testRunner) {
    throw new Error('No test runner found. Please install vitest, jest, or mocha.');
  }

  console.log(chalk.blue(`🧪 Running tests with ${testRunner}...\n`));

  const spinner = ora('Running tests...').start();

  try {
    // Build test command
    const testCommand = buildTestCommand(testRunner, options);
    
    // Run tests
    const testProcess = await execa(testCommand.command, testCommand.args, {
      cwd,
      stdio: options.verbose ? 'inherit' : 'pipe',
      env: {
        ...process.env,
        NODE_ENV: 'test',
      },
    });

    // Parse test results
    if (!options.verbose) {
      parseTestOutput(testProcess.stdout || '', testRunner, result);
    }

    result.success = testProcess.exitCode === 0;
    result.duration = Date.now() - startTime;

    if (result.success) {
      spinner.succeed('Tests completed');
    } else {
      spinner.fail('Tests failed');
    }

    return result;
  } catch (error: any) {
    spinner.fail('Tests failed');
    
    // Parse error output for test results
    if (error.stdout) {
      parseTestOutput(error.stdout, testRunner, result);
    }
    
    result.success = false;
    result.duration = Date.now() - startTime;
    
    return result;
  }
}

/**
 * Detect available test runner
 */
function detectTestRunner(cwd: string, packageInfo: any): string | null {
  const dependencies = {
    ...packageInfo.dependencies,
    ...packageInfo.devDependencies,
  };

  // Check for vitest (preferred)
  if (dependencies.vitest) {
    return 'vitest';
  }

  // Check for jest
  if (dependencies.jest || dependencies['@types/jest']) {
    return 'jest';
  }

  // Check for mocha
  if (dependencies.mocha) {
    return 'mocha';
  }

  return null;
}

/**
 * Build test command based on runner
 */
function buildTestCommand(runner: string, options: TestOptions): { command: string; args: string[] } {
  const args: string[] = [];

  switch (runner) {
    case 'vitest':
      if (options.watch) {
        // Vitest runs in watch mode by default in dev
      } else {
        args.push('run');
      }
      
      if (options.coverage) {
        args.push('--coverage');
      }
      
      if (options.verbose) {
        args.push('--reporter=verbose');
      }
      
      if (options.pattern) {
        args.push(options.pattern);
      }
      
      if (options.bail) {
        args.push('--bail');
      }
      
      return { command: 'npx', args: ['vitest', ...args] };

    case 'jest':
      if (options.watch) {
        args.push('--watch');
      }
      
      if (options.coverage) {
        args.push('--coverage');
      }
      
      if (options.verbose) {
        args.push('--verbose');
      }
      
      if (options.pattern) {
        args.push('--testPathPattern', options.pattern);
      }
      
      if (options.bail) {
        args.push('--bail');
      }
      
      args.push('--timeout', options.timeout || '5000');
      
      return { command: 'npx', args: ['jest', ...args] };

    case 'mocha':
      if (options.pattern) {
        args.push(options.pattern);
      } else {
        args.push('test/**/*.test.{js,ts}');
      }
      
      if (options.timeout) {
        args.push('--timeout', options.timeout);
      }
      
      if (options.bail) {
        args.push('--bail');
      }
      
      // Add TypeScript support if needed
      if (existsSync(join(process.cwd(), 'tsconfig.json'))) {
        args.unshift('--require', 'ts-node/register');
      }
      
      return { command: 'npx', args: ['mocha', ...args] };

    default:
      throw new Error(`Unsupported test runner: ${runner}`);
  }
}

/**
 * Parse test output to extract results
 */
function parseTestOutput(output: string, runner: string, result: TestResult): void {
  const lines = output.split('\n');

  switch (runner) {
    case 'vitest':
      parseVitestOutput(lines, result);
      break;
    case 'jest':
      parseJestOutput(lines, result);
      break;
    case 'mocha':
      parseMochaOutput(lines, result);
      break;
  }
}

/**
 * Parse Vitest output
 */
function parseVitestOutput(lines: string[], result: TestResult): void {
  for (const line of lines) {
    // Look for test summary
    const passMatch = line.match(/(\d+) passed/);
    if (passMatch) {
      result.passed = parseInt(passMatch[1]);
    }

    const failMatch = line.match(/(\d+) failed/);
    if (failMatch) {
      result.failed = parseInt(failMatch[1]);
    }

    const skipMatch = line.match(/(\d+) skipped/);
    if (skipMatch) {
      result.skipped = parseInt(skipMatch[1]);
    }

    // Look for coverage
    const coverageMatch = line.match(/All files\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)/);
    if (coverageMatch) {
      result.coverage = {
        statements: parseFloat(coverageMatch[1]),
        branches: parseFloat(coverageMatch[2]),
        functions: parseFloat(coverageMatch[3]),
        lines: parseFloat(coverageMatch[4]),
      };
    }
  }
}

/**
 * Parse Jest output
 */
function parseJestOutput(lines: string[], result: TestResult): void {
  for (const line of lines) {
    // Look for test summary
    const summaryMatch = line.match(/Tests:\s+(\d+) failed,\s+(\d+) passed,\s+(\d+) total/);
    if (summaryMatch) {
      result.failed = parseInt(summaryMatch[1]);
      result.passed = parseInt(summaryMatch[2]);
    }

    // Look for coverage
    const coverageMatch = line.match(/All files\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)/);
    if (coverageMatch) {
      result.coverage = {
        statements: parseFloat(coverageMatch[1]),
        branches: parseFloat(coverageMatch[2]),
        functions: parseFloat(coverageMatch[3]),
        lines: parseFloat(coverageMatch[4]),
      };
    }
  }
}

/**
 * Parse Mocha output
 */
function parseMochaOutput(lines: string[], result: TestResult): void {
  for (const line of lines) {
    // Look for test summary
    const summaryMatch = line.match(/(\d+) passing/);
    if (summaryMatch) {
      result.passed = parseInt(summaryMatch[1]);
    }

    const failMatch = line.match(/(\d+) failing/);
    if (failMatch) {
      result.failed = parseInt(failMatch[1]);
    }

    const skipMatch = line.match(/(\d+) pending/);
    if (skipMatch) {
      result.skipped = parseInt(skipMatch[1]);
    }
  }
}

/**
 * Show test summary
 */
function showTestSummary(result: TestResult): void {
  console.log(chalk.blue('\n📊 Test Summary:'));
  console.log(chalk.green(`   ✅ Passed: ${result.passed}`));
  
  if (result.failed > 0) {
    console.log(chalk.red(`   ❌ Failed: ${result.failed}`));
  }
  
  if (result.skipped > 0) {
    console.log(chalk.yellow(`   ⏭️  Skipped: ${result.skipped}`));
  }
  
  console.log(chalk.gray(`   ⏱️  Duration: ${result.duration}ms`));

  if (result.coverage) {
    console.log(chalk.blue('\n📈 Coverage:'));
    console.log(chalk.gray(`   Statements: ${result.coverage.statements}%`));
    console.log(chalk.gray(`   Branches: ${result.coverage.branches}%`));
    console.log(chalk.gray(`   Functions: ${result.coverage.functions}%`));
    console.log(chalk.gray(`   Lines: ${result.coverage.lines}%`));
  }
}
