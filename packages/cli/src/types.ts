/**
 * CLI Tool Types
 * 
 * Type definitions for CLI tool functionality
 */

/**
 * Project configuration
 */
export interface ProjectConfig {
  name: string;
  version: string;
  description?: string;
  author?: string;
  license?: string;
  transport: Array<'stdio' | 'http' | 'sse'>;
  tools?: ToolConfig[];
  resources?: ResourceConfig[];
  prompts?: PromptConfig[];
  build?: BuildConfig;
  dev?: DevConfig;
}

/**
 * Tool configuration
 */
export interface ToolConfig {
  name: string;
  description: string;
  inputSchema: any;
  handler: string;
}

/**
 * Resource configuration
 */
export interface ResourceConfig {
  pattern: string;
  name: string;
  description?: string;
  mimeType?: string;
  handler: string;
}

/**
 * Prompt configuration
 */
export interface PromptConfig {
  name: string;
  description: string;
  arguments?: Array<{
    name: string;
    description: string;
    required?: boolean;
  }>;
  handler: string;
}

/**
 * Build configuration
 */
export interface BuildConfig {
  outDir?: string;
  target?: string;
  format?: 'esm' | 'cjs';
  minify?: boolean;
  sourcemap?: boolean;
  external?: string[];
}

/**
 * Development configuration
 */
export interface DevConfig {
  port?: number;
  host?: string;
  watch?: string[];
  ignore?: string[];
  restart?: boolean;
  debug?: boolean;
}

/**
 * Template configuration
 */
export interface TemplateConfig {
  name: string;
  description: string;
  author?: string;
  version?: string;
  files: TemplateFile[];
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
  scripts?: Record<string, string>;
  prompts?: TemplatePrompt[];
}

/**
 * Template file
 */
export interface TemplateFile {
  path: string;
  content: string;
  template?: boolean;
}

/**
 * Template prompt
 */
export interface TemplatePrompt {
  name: string;
  type: 'input' | 'confirm' | 'list' | 'checkbox';
  message: string;
  default?: any;
  choices?: string[];
  validate?: (input: any) => boolean | string;
}

/**
 * CLI command context
 */
export interface CommandContext {
  cwd: string;
  config?: ProjectConfig;
  args: any;
  options: any;
}

/**
 * Build result
 */
export interface BuildResult {
  success: boolean;
  outputFiles: string[];
  errors: string[];
  warnings: string[];
  duration: number;
}

/**
 * Test result
 */
export interface TestResult {
  success: boolean;
  passed: number;
  failed: number;
  skipped: number;
  coverage?: {
    lines: number;
    functions: number;
    branches: number;
    statements: number;
  };
  duration: number;
}

/**
 * Development server status
 */
export interface DevServerStatus {
  running: boolean;
  port?: number;
  host?: string;
  transports: string[];
  lastRestart?: Date;
  errors: string[];
}
