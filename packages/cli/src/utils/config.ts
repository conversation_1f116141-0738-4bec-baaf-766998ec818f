/**
 * Configuration Utilities
 * 
 * Utilities for handling project configuration
 */

import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join, resolve } from 'path';
import { parse as parseYaml, stringify as stringifyYaml } from 'yaml';
import { ProjectConfig } from '../types.js';

/**
 * Default project configuration
 */
export const DEFAULT_CONFIG: Partial<ProjectConfig> = {
  version: '1.0.0',
  license: 'MIT',
  transport: ['stdio'],
  build: {
    outDir: 'dist',
    target: 'node18',
    format: 'esm',
    minify: false,
    sourcemap: true,
  },
  dev: {
    port: 3000,
    host: 'localhost',
    watch: ['src/**/*'],
    ignore: ['node_modules/**', 'dist/**'],
    restart: true,
    debug: false,
  },
};

/**
 * Configuration file names to search for
 */
const CONFIG_FILES = [
  'mcp.config.yaml',
  'mcp.config.yml',
  'mcp.config.json',
  'mcp.config.js',
  'mcp.config.ts',
];

/**
 * Find configuration file in directory
 */
export function findConfigFile(dir: string): string | null {
  for (const filename of CONFIG_FILES) {
    const filepath = join(dir, filename);
    if (existsSync(filepath)) {
      return filepath;
    }
  }
  return null;
}

/**
 * Load project configuration
 */
export function loadConfig(dir: string = process.cwd()): ProjectConfig | null {
  const configFile = findConfigFile(dir);
  if (!configFile) {
    return null;
  }

  try {
    const content = readFileSync(configFile, 'utf-8');
    const ext = configFile.split('.').pop();

    let config: ProjectConfig;

    switch (ext) {
      case 'yaml':
      case 'yml':
        config = parseYaml(content);
        break;
      case 'json':
        config = JSON.parse(content);
        break;
      case 'js':
      case 'ts':
        // For JS/TS files, we would need to use dynamic import
        // For now, we'll skip this and focus on YAML/JSON
        throw new Error('JS/TS config files not yet supported');
      default:
        throw new Error(`Unsupported config file format: ${ext}`);
    }

    // Merge with defaults
    return mergeConfig(DEFAULT_CONFIG, config);
  } catch (error) {
    throw new Error(`Failed to load config from ${configFile}: ${error}`);
  }
}

/**
 * Save project configuration
 */
export function saveConfig(config: ProjectConfig, dir: string = process.cwd(), format: 'yaml' | 'json' = 'yaml'): void {
  const filename = format === 'yaml' ? 'mcp.config.yaml' : 'mcp.config.json';
  const filepath = join(dir, filename);

  try {
    let content: string;
    
    if (format === 'yaml') {
      content = stringifyYaml(config, {
        indent: 2,
        lineWidth: 100,
      });
    } else {
      content = JSON.stringify(config, null, 2);
    }

    writeFileSync(filepath, content, 'utf-8');
  } catch (error) {
    throw new Error(`Failed to save config to ${filepath}: ${error}`);
  }
}

/**
 * Merge configuration objects
 */
export function mergeConfig(base: Partial<ProjectConfig>, override: Partial<ProjectConfig>): ProjectConfig {
  const merged = { ...base, ...override };

  // Deep merge nested objects
  if (base.build && override.build) {
    merged.build = { ...base.build, ...override.build };
  }

  if (base.dev && override.dev) {
    merged.dev = { ...base.dev, ...override.dev };
  }

  // Merge arrays
  if (base.transport && override.transport) {
    merged.transport = [...new Set([...base.transport, ...override.transport])];
  }

  if (base.tools && override.tools) {
    merged.tools = [...base.tools, ...override.tools];
  }

  if (base.resources && override.resources) {
    merged.resources = [...base.resources, ...override.resources];
  }

  if (base.prompts && override.prompts) {
    merged.prompts = [...base.prompts, ...override.prompts];
  }

  return merged as ProjectConfig;
}

/**
 * Validate project configuration
 */
export function validateConfig(config: ProjectConfig): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Required fields
  if (!config.name) {
    errors.push('Project name is required');
  }

  if (!config.version) {
    errors.push('Project version is required');
  }

  // Validate transport
  if (!config.transport || config.transport.length === 0) {
    errors.push('At least one transport method is required');
  }

  const validTransports = ['stdio', 'http', 'sse'];
  for (const transport of config.transport || []) {
    if (!validTransports.includes(transport)) {
      errors.push(`Invalid transport: ${transport}. Valid options: ${validTransports.join(', ')}`);
    }
  }

  // Validate tools
  if (config.tools) {
    for (const tool of config.tools) {
      if (!tool.name) {
        errors.push('Tool name is required');
      }
      if (!tool.description) {
        errors.push(`Tool '${tool.name}' description is required`);
      }
      if (!tool.handler) {
        errors.push(`Tool '${tool.name}' handler is required`);
      }
    }
  }

  // Validate resources
  if (config.resources) {
    for (const resource of config.resources) {
      if (!resource.pattern) {
        errors.push('Resource pattern is required');
      }
      if (!resource.name) {
        errors.push('Resource name is required');
      }
      if (!resource.handler) {
        errors.push(`Resource '${resource.name}' handler is required`);
      }
    }
  }

  // Validate prompts
  if (config.prompts) {
    for (const prompt of config.prompts) {
      if (!prompt.name) {
        errors.push('Prompt name is required');
      }
      if (!prompt.description) {
        errors.push(`Prompt '${prompt.name}' description is required`);
      }
      if (!prompt.handler) {
        errors.push(`Prompt '${prompt.name}' handler is required`);
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Get package.json information
 */
export function getPackageInfo(dir: string = process.cwd()): any {
  const packagePath = join(dir, 'package.json');
  if (!existsSync(packagePath)) {
    return null;
  }

  try {
    const content = readFileSync(packagePath, 'utf-8');
    return JSON.parse(content);
  } catch (error) {
    return null;
  }
}

/**
 * Check if directory is an MCP project
 */
export function isMCPProject(dir: string = process.cwd()): boolean {
  const configFile = findConfigFile(dir);
  const packageInfo = getPackageInfo(dir);
  
  return !!(configFile || (packageInfo && packageInfo.dependencies && packageInfo.dependencies['@mcp-framework/core']));
}
