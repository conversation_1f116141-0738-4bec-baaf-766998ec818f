/**
 * Template Utilities
 * 
 * Utilities for handling project templates
 */

import { readFileSync, writeFileSync, mkdirSync, existsSync } from 'fs';
import { join, dirname, resolve } from 'path';
import { copy, ensureDir } from 'fs-extra';
import { TemplateConfig, TemplateFile, TemplatePrompt } from '../types.js';

/**
 * Template variable replacement
 */
export function replaceTemplateVariables(content: string, variables: Record<string, any>): string {
  let result = content;

  // Replace {{variable}} patterns
  for (const [key, value] of Object.entries(variables)) {
    const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
    result = result.replace(regex, String(value));
  }

  // Replace conditional blocks {{#if condition}}...{{/if}}
  result = result.replace(/{{#if\s+(\w+)}}([\s\S]*?){{\/if}}/g, (match, condition, content) => {
    return variables[condition] ? content : '';
  });

  // Replace loops {{#each array}}...{{/each}}
  result = result.replace(/{{#each\s+(\w+)}}([\s\S]*?){{\/each}}/g, (match, arrayName, template) => {
    const array = variables[arrayName];
    if (!Array.isArray(array)) return '';
    
    return array.map(item => {
      let itemContent = template;
      if (typeof item === 'object') {
        for (const [key, value] of Object.entries(item)) {
          const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
          itemContent = itemContent.replace(regex, String(value));
        }
      } else {
        itemContent = itemContent.replace(/{{this}}/g, String(item));
      }
      return itemContent;
    }).join('');
  });

  return result;
}

/**
 * Load template configuration
 */
export function loadTemplate(templatePath: string): TemplateConfig {
  const configPath = join(templatePath, 'template.json');
  
  if (!existsSync(configPath)) {
    throw new Error(`Template configuration not found: ${configPath}`);
  }

  try {
    const content = readFileSync(configPath, 'utf-8');
    const config = JSON.parse(content) as TemplateConfig;
    
    // Load template files
    config.files = loadTemplateFiles(templatePath, config.files || []);
    
    return config;
  } catch (error) {
    throw new Error(`Failed to load template: ${error}`);
  }
}

/**
 * Load template files
 */
function loadTemplateFiles(templatePath: string, fileConfigs: TemplateFile[]): TemplateFile[] {
  return fileConfigs.map(fileConfig => {
    const filePath = join(templatePath, 'files', fileConfig.path);
    
    if (!existsSync(filePath)) {
      throw new Error(`Template file not found: ${filePath}`);
    }

    const content = readFileSync(filePath, 'utf-8');
    
    return {
      ...fileConfig,
      content,
    };
  });
}

/**
 * Generate project from template
 */
export async function generateFromTemplate(
  templatePath: string,
  outputPath: string,
  variables: Record<string, any>
): Promise<void> {
  const template = loadTemplate(templatePath);
  
  // Ensure output directory exists
  await ensureDir(outputPath);

  // Process template files
  for (const file of template.files) {
    const targetPath = join(outputPath, replaceTemplateVariables(file.path, variables));
    const targetDir = dirname(targetPath);
    
    // Ensure target directory exists
    await ensureDir(targetDir);
    
    // Process file content
    let content = file.content;
    if (file.template !== false) {
      content = replaceTemplateVariables(content, variables);
    }
    
    writeFileSync(targetPath, content, 'utf-8');
  }

  // Generate package.json
  const packageJson = {
    name: variables.name,
    version: variables.version || '1.0.0',
    description: variables.description || '',
    main: 'dist/index.js',
    type: 'module',
    scripts: {
      dev: 'mcp dev',
      build: 'mcp build',
      test: 'mcp test',
      start: 'node dist/index.js',
      ...template.scripts,
    },
    dependencies: {
      '@mcp-framework/core': '^0.1.0',
      ...template.dependencies,
    },
    devDependencies: {
      '@types/node': '^20.11.0',
      typescript: '^5.3.3',
      ...template.devDependencies,
    },
    keywords: ['mcp', 'model-context-protocol'],
    author: variables.author || '',
    license: variables.license || 'MIT',
  };

  writeFileSync(
    join(outputPath, 'package.json'),
    JSON.stringify(packageJson, null, 2),
    'utf-8'
  );
}

/**
 * Get available templates
 */
export function getAvailableTemplates(templatesDir: string): Array<{ name: string; config: TemplateConfig }> {
  const templates: Array<{ name: string; config: TemplateConfig }> = [];
  
  if (!existsSync(templatesDir)) {
    return templates;
  }

  const fs = require('fs');
  const entries = fs.readdirSync(templatesDir, { withFileTypes: true });
  
  for (const entry of entries) {
    if (entry.isDirectory()) {
      try {
        const templatePath = join(templatesDir, entry.name);
        const config = loadTemplate(templatePath);
        templates.push({ name: entry.name, config });
      } catch (error) {
        // Skip invalid templates
        console.warn(`Skipping invalid template: ${entry.name}`);
      }
    }
  }

  return templates;
}

/**
 * Built-in templates
 */
export const BUILTIN_TEMPLATES = {
  basic: {
    name: 'basic',
    description: 'Basic MCP service template',
    author: 'MCP Framework Team',
    version: '1.0.0',
    files: [
      {
        path: 'src/index.ts',
        content: `import { createMCPService } from '@mcp-framework/core';

const service = createMCPService('{{name}}', '{{version}}')
  .description('{{description}}')
  .tool(
    'hello',
    'Say hello to someone',
    {
      type: 'object',
      properties: {
        name: { type: 'string', description: 'Name to greet' }
      },
      required: ['name']
    },
    ({ name }) => \`Hello, \${name}!\`
  )
  .build();

async function main() {
  await service.start([{{#each transport}}'{{this}}'{{#unless @last}}, {{/unless}}{{/each}}]);
  console.log('{{name}} service started');
}

main().catch(console.error);
`,
        template: true,
      },
      {
        path: 'src/types.ts',
        content: `// Add your custom types here
export interface GreetingInput {
  name: string;
}
`,
        template: false,
      },
      {
        path: 'mcp.config.yaml',
        content: `name: {{name}}
version: {{version}}
description: {{description}}
transport: [{{#each transport}}'{{this}}'{{#unless @last}}, {{/unless}}{{/each}}]

tools:
  - name: hello
    description: Say hello to someone
    inputSchema:
      type: object
      properties:
        name:
          type: string
          description: Name to greet
      required: [name]
    handler: ./src/handlers/hello.js

build:
  outDir: dist
  target: node18
  format: esm

dev:
  port: 3000
  debug: true
`,
        template: true,
      },
      {
        path: 'README.md',
        content: `# {{name}}

{{description}}

## Installation

\`\`\`bash
npm install
\`\`\`

## Development

\`\`\`bash
npm run dev
\`\`\`

## Build

\`\`\`bash
npm run build
\`\`\`

## Usage

\`\`\`bash
npm start
\`\`\`

## License

{{license}}
`,
        template: true,
      },
      {
        path: '.gitignore',
        content: `node_modules/
dist/
.env
.env.local
*.log
.DS_Store
`,
        template: false,
      },
    ],
    prompts: [
      {
        name: 'name',
        type: 'input',
        message: 'Project name:',
        validate: (input: string) => input.length > 0 || 'Project name is required',
      },
      {
        name: 'description',
        type: 'input',
        message: 'Project description:',
        default: 'An MCP service built with MCP Framework',
      },
      {
        name: 'author',
        type: 'input',
        message: 'Author:',
        default: '',
      },
      {
        name: 'license',
        type: 'list',
        message: 'License:',
        choices: ['MIT', 'Apache-2.0', 'GPL-3.0', 'BSD-3-Clause'],
        default: 'MIT',
      },
      {
        name: 'transport',
        type: 'checkbox',
        message: 'Select transport methods:',
        choices: ['stdio', 'http', 'sse'],
        default: ['stdio'],
      },
    ],
  },
} as const;
