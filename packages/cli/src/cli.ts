#!/usr/bin/env node

/**
 * MCP Framework CLI
 * 
 * Command line interface for creating and managing MCP services
 */

import { Command } from 'commander';
import chalk from 'chalk';

import { initCommand } from './commands/init.js';
import { devCommand } from './commands/dev.js';
import { buildCommand } from './commands/build.js';
import { testCommand } from './commands/test.js';

const program = new Command();

program
  .name('mcp')
  .description('MCP Framework - Build MCP services with ease')
  .version('0.1.0');

// Commands
program.addCommand(initCommand);
program.addCommand(devCommand);
program.addCommand(buildCommand);
program.addCommand(testCommand);

// Global error handler
program.exitOverride((err) => {
  if (err.code === 'commander.help') {
    process.exit(0);
  }
  console.error(chalk.red('Error:'), err.message);
  process.exit(1);
});

// Parse arguments
program.parse();
