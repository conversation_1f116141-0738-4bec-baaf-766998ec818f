#!/usr/bin/env node

/**
 * MCP Framework CLI
 *
 * Command line interface for creating and managing MCP services
 */

import { Command } from 'commander';
import chalk from 'chalk';
import updateNotifier from 'update-notifier';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

import { initCommand } from './commands/init.js';
import { devCommand } from './commands/dev.js';
import { buildCommand } from './commands/build.js';
import { testCommand } from './commands/test.js';

// Get package info for version and update checking
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const packagePath = join(__dirname, '../package.json');
const packageInfo = JSON.parse(readFileSync(packagePath, 'utf-8'));

// Check for updates
const notifier = updateNotifier({
  pkg: packageInfo,
  updateCheckInterval: 1000 * 60 * 60 * 24, // 24 hours
});

if (notifier.update) {
  notifier.notify({
    defer: false,
    message: `Update available ${chalk.dim('{currentVersion}')} → ${chalk.green('{latestVersion}')}\n` +
             `Run ${chalk.cyan('npm install -g @mcp-framework/cli')} to update`,
  });
}

const program = new Command();

program
  .name('mcp')
  .description('MCP Framework - Build MCP services with ease')
  .version(packageInfo.version);

// Add banner for help
program.configureHelp({
  beforeAll: () => chalk.blue.bold('🚀 MCP Framework CLI\n'),
});

// Commands
program.addCommand(initCommand);
program.addCommand(devCommand);
program.addCommand(buildCommand);
program.addCommand(testCommand);

// Global error handler
program.exitOverride((err) => {
  if (err.code === 'commander.help') {
    process.exit(0);
  }
  console.error(chalk.red('Error:'), err.message);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error(chalk.red('Uncaught Exception:'), error.message);
  if (process.env.MCP_DEBUG) {
    console.error(error.stack);
  }
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error(chalk.red('Unhandled Rejection:'), reason);
  process.exit(1);
});

// Parse arguments
program.parse();
