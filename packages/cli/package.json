{"name": "@mcp-framework/cli", "version": "0.1.0", "description": "Command line interface for MCP Framework", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "bin": {"mcp": "./dist/cli.js"}, "files": ["dist", "templates", "README.md"], "scripts": {"build": "vite build", "dev": "vite build --watch", "test": "vitest", "test:run": "vitest run", "typecheck": "tsc --noEmit", "clean": "rm -rf dist"}, "keywords": ["mcp", "cli", "framework", "development-tools"], "author": "MCP Framework Team", "license": "MIT", "dependencies": {"@mcp-framework/core": "workspace:*", "commander": "^11.1.0", "inquirer": "^9.2.12", "chalk": "^5.3.0", "ora": "^8.0.1", "fs-extra": "^11.2.0", "yaml": "^2.3.4", "chokidar": "^3.5.3", "esbuild": "^0.19.12", "execa": "^8.0.1", "semver": "^7.5.4", "update-notifier": "^7.0.0", "boxen": "^7.1.1", "gradient-string": "^2.0.2"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "vite": "^5.0.12", "vite-plugin-dts": "^3.7.2"}}