import { defineConfig } from 'vite';
import dts from 'vite-plugin-dts';
import { resolve } from 'path';

export default defineConfig({
  plugins: [
    dts({
      insertTypesEntry: true,
      rollupTypes: true,
    }),
  ],
  build: {
    lib: {
      entry: {
        index: resolve(__dirname, 'src/index.ts'),
        cli: resolve(__dirname, 'src/cli.ts'),
      },
      formats: ['es'],
    },
    rollupOptions: {
      external: [
        'commander',
        'inquirer',
        'chalk',
        'ora',
        'fs-extra',
        'yaml',
        'chokidar',
        'esbuild',
        'execa',
        'semver',
        'update-notifier',
        'boxen',
        'gradient-string',
        '@mcp-framework/core',
      ],
    },
  },
  test: {
    environment: 'node',
  },
});
