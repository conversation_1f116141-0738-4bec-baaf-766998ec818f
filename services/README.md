# MCP Framework - 示例服务

本目录包含三个不同类型的MCP服务示例，展示了MCP框架的完整能力和不同的开发模式。

## 🗂️ 文件操作服务 (File Operations Service)

**位置**: `file-ops-service/`  
**传输方式**: stdio  
**API模式**: 装饰器模式  

### 功能特性
- 📄 文件读写操作
- 📁 目录列表和搜索
- 🔍 文件内容搜索
- 🛡️ 安全路径验证
- 📁 资源访问支持

### 工具列表
- `read_file` - 读取文件内容
- `write_file` - 写入文件内容
- `list_directory` - 列出目录内容
- `search_files` - 搜索文件

### 使用示例
```bash
cd file-ops-service
node src/index.js
```

### 配置文件
```yaml
name: file-ops-service
version: 1.0.0
transport: [stdio]
```

---

## 🌐 Web工具服务 (Web Tools Service)

**位置**: `web-tools-service/`  
**传输方式**: HTTP  
**API模式**: 工厂函数模式  

### 功能特性
- 🌐 HTTP请求客户端
- 🔍 URL分析和验证
- 📄 网页内容抓取
- 🔗 链接验证
- 🌍 CORS支持

### 工具列表
- `http_request` - 发送HTTP请求
- `analyze_url` - 分析URL组件
- `scrape_webpage` - 抓取网页内容
- `validate_url` - 验证URL可访问性

### 使用示例
```bash
cd web-tools-service
node src/index.js
# 访问 http://localhost:3000
```

### API端点
- `GET /` - 服务状态页面
- `POST /mcp` - MCP协议端点

---

## 🤖 AI助手服务 (AI Assistant Service)

**位置**: `ai-assistant-service/`  
**传输方式**: SSE (Server-Sent Events)  
**API模式**: 混合模式 (工具 + 提示)  

### 功能特性
- 📊 文本分析 (情感、关键词)
- 💬 对话管理和上下文
- 💻 编程助手提示
- 📝 创意写作提示
- 📈 数据分析提示
- 🔄 实时通信

### 工具列表
- `analyze_text` - 文本分析
- `manage_conversation` - 对话管理

### 提示列表
- `coding_assistant` - 编程助手
- `data_analysis` - 数据分析
- `creative_writing` - 创意写作

### 使用示例
```bash
cd ai-assistant-service
node src/index.js
# 访问 http://localhost:3001
# SSE事件流: http://localhost:3001/events
```

### 实时功能
- 📡 Server-Sent Events推送
- 💬 实时消息广播
- 📊 实时状态更新

---

## 🚀 快速开始

### 1. 安装依赖
```bash
# 为所有服务安装依赖
cd file-ops-service && npm install && cd ..
cd web-tools-service && npm install && cd ..
cd ai-assistant-service && npm install && cd ..
```

### 2. 运行测试
```bash
# 运行服务模式测试
node test-simple-services.js
```

### 3. 启动服务

**文件操作服务 (stdio)**:
```bash
cd file-ops-service
node src/index.js
```

**Web工具服务 (HTTP)**:
```bash
cd web-tools-service
node src/index.js
# 访问 http://localhost:3000
```

**AI助手服务 (SSE)**:
```bash
cd ai-assistant-service
node src/index.js
# 访问 http://localhost:3001
```

## 📊 服务对比

| 特性 | 文件操作 | Web工具 | AI助手 |
|------|----------|---------|--------|
| 传输方式 | stdio | HTTP | SSE |
| API模式 | 装饰器 | 工厂函数 | 混合 |
| 主要功能 | 文件系统 | Web请求 | AI交互 |
| 实时性 | 否 | 否 | 是 |
| 状态管理 | 无 | 无 | 有 |
| 提示支持 | 否 | 否 | 是 |

## 🎯 设计模式

### 1. 装饰器模式 (文件操作服务)
```javascript
@mcpService({
  name: 'file-ops-service',
  version: '1.0.0'
})
class FileOpsService {
  @tool({
    name: 'read_file',
    description: 'Read file contents'
  })
  async readFile({ path }) {
    // 实现
  }
}
```

### 2. 工厂函数模式 (Web工具服务)
```javascript
const service = createMCPService('web-tools', '1.0.0')
  .tool('http_request', 'Make HTTP requests', schema, handler)
  .build();
```

### 3. 混合模式 (AI助手服务)
```javascript
class AIService {
  @tool({ name: 'analyze_text' })
  async analyzeText(input) { /* */ }
  
  @prompt({ name: 'coding_assistant' })
  async codingPrompt(args) { /* */ }
}
```

## 🔧 自定义开发

### 添加新工具
```javascript
service.addTool({
  name: 'my_tool',
  description: 'My custom tool',
  inputSchema: { /* JSON Schema */ },
  handler: async (input) => {
    // 工具逻辑
    return result;
  }
});
```

### 添加新提示
```javascript
service.addPrompt({
  name: 'my_prompt',
  description: 'My custom prompt',
  handler: async (args) => {
    return [{
      role: 'user',
      content: { type: 'text', text: 'Generated prompt' }
    }];
  }
});
```

## 📚 最佳实践

1. **安全性**: 始终验证输入参数和文件路径
2. **错误处理**: 提供清晰的错误消息
3. **性能**: 对于大文件操作使用流式处理
4. **日志**: 记录重要操作和错误
5. **测试**: 为每个工具编写单元测试

## 🎉 总结

这三个服务展示了MCP框架的完整能力：

- ✅ **多种API模式** - 装饰器、工厂函数、混合
- ✅ **多种传输方式** - stdio、HTTP、SSE
- ✅ **完整功能** - 工具、资源、提示
- ✅ **实时通信** - Server-Sent Events
- ✅ **状态管理** - 对话和上下文
- ✅ **类型安全** - 完整的参数验证
- ✅ **生产就绪** - 错误处理和安全性

**MCP框架让构建强大的MCP服务变得极其简单！** 🚀
