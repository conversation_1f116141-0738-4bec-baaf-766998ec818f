/**
 * @license
 * web-streams-polyfill v3.3.3
 * Copyright 2024 <PERSON>, <PERSON><PERSON><PERSON> and other contributors.
 * This code is released under the MIT license.
 * SPDX-License-Identifier: MIT
 */
!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r((e="undefined"!=typeof globalThis?globalThis:e||self).WebStreamsPolyfill={})}(this,(function(e){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol:function(e){return"Symbol(".concat(e,")")};function t(e,r){var t,n,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function u(u){return function(l){return function(u){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,u[0]&&(i=0)),i;)try{if(t=1,n&&(o=2&u[0]?n.return:u[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,u[1])).done)return o;switch(n=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return i.label++,{value:u[1],done:!1};case 5:i.label++,n=u[1],u=[0];continue;case 7:u=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==u[0]&&2!==u[0])){i=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){i.label=u[1];break}if(6===u[0]&&i.label<o[1]){i.label=o[1],o=u;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(u);break}o[2]&&i.ops.pop(),i.trys.pop();continue}u=r.call(e,i)}catch(e){u=[6,e],n=0}finally{t=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}([u,l])}}}function n(e){var r="function"==typeof Symbol&&Symbol.iterator,t=r&&e[r],n=0;if(t)return t.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(r?"Object is not iterable.":"Symbol.iterator is not defined.")}function o(e){return this instanceof o?(this.v=e,this):new o(e)}function a(e,r,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,a=t.apply(e,r||[]),i=[];return n={},u("next"),u("throw"),u("return"),n[Symbol.asyncIterator]=function(){return this},n;function u(e){a[e]&&(n[e]=function(r){return new Promise((function(t,n){i.push([e,r,t,n])>1||l(e,r)}))})}function l(e,r){try{(t=a[e](r)).value instanceof o?Promise.resolve(t.value.v).then(s,c):f(i[0][2],t)}catch(e){f(i[0][3],e)}var t}function s(e){l("next",e)}function c(e){l("throw",e)}function f(e,r){e(r),i.shift(),i.length&&l(i[0][0],i[0][1])}}function i(e){var r,t;return r={},n("next"),n("throw",(function(e){throw e})),n("return"),r[Symbol.iterator]=function(){return this},r;function n(n,a){r[n]=e[n]?function(r){return(t=!t)?{value:o(e[n](r)),done:!1}:a?a(r):r}:a}}function u(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,t=e[Symbol.asyncIterator];return t?t.call(e):(e=n(e),r={},o("next"),o("throw"),o("return"),r[Symbol.asyncIterator]=function(){return this},r);function o(t){r[t]=e[t]&&function(r){return new Promise((function(n,o){(function(e,r,t,n){Promise.resolve(n).then((function(r){e({value:r,done:t})}),r)})(n,o,(r=e[t](r)).done,r.value)}))}}}function l(){}function s(e){return"object"==typeof e&&null!==e||"function"==typeof e}"function"==typeof SuppressedError&&SuppressedError;var c=l;function f(e,r){try{Object.defineProperty(e,"name",{value:r,configurable:!0})}catch(e){}}var d=Promise,b=Promise.prototype.then,p=Promise.reject.bind(d);function h(e){return new d(e)}function m(e){return h((function(r){return r(e)}))}function _(e){return p(e)}function y(e,r,t){return b.call(e,r,t)}function v(e,r,t){y(y(e,r,t),void 0,c)}function g(e,r){v(e,r)}function S(e,r){v(e,void 0,r)}function w(e,r,t){return y(e,r,t)}function R(e){y(e,void 0,c)}var T=function(e){if("function"==typeof queueMicrotask)T=queueMicrotask;else{var r=m(void 0);T=function(e){return y(r,e)}}return T(e)};function P(e,r,t){if("function"!=typeof e)throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,r,t)}function C(e,r,t){try{return m(P(e,r,t))}catch(e){return _(e)}}var q=function(){function e(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}return Object.defineProperty(e.prototype,"length",{get:function(){return this._size},enumerable:!1,configurable:!0}),e.prototype.push=function(e){var r=this._back,t=r;16383===r._elements.length&&(t={_elements:[],_next:void 0}),r._elements.push(e),t!==r&&(this._back=t,r._next=t),++this._size},e.prototype.shift=function(){var e=this._front,r=e,t=this._cursor,n=t+1,o=e._elements,a=o[t];return 16384===n&&(r=e._next,n=0),--this._size,this._cursor=n,e!==r&&(this._front=r),o[t]=void 0,a},e.prototype.forEach=function(e){for(var r=this._cursor,t=this._front,n=t._elements;!(r===n.length&&void 0===t._next||r===n.length&&(r=0,0===(n=(t=t._next)._elements).length));)e(n[r]),++r},e.prototype.peek=function(){var e=this._front,r=this._cursor;return e._elements[r]},e}(),E=r("[[AbortSteps]]"),O=r("[[ErrorSteps]]"),W=r("[[CancelSteps]]"),j=r("[[PullSteps]]"),B=r("[[ReleaseSteps]]");function k(e,r){e._ownerReadableStream=r,r._reader=e,"readable"===r._state?D(e):"closed"===r._state?function(e){D(e),M(e)}(e):L(e,r._storedError)}function A(e,r){return Vt(e._ownerReadableStream,r)}function z(e){var r=e._ownerReadableStream;"readable"===r._state?F(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(e,r){L(e,r)}(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),r._readableStreamController[B](),r._reader=void 0,e._ownerReadableStream=void 0}function I(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function D(e){e._closedPromise=h((function(r,t){e._closedPromise_resolve=r,e._closedPromise_reject=t}))}function L(e,r){D(e),F(e,r)}function F(e,r){void 0!==e._closedPromise_reject&&(R(e._closedPromise),e._closedPromise_reject(r),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function M(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}var x=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},Y=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function Q(e,r){if(void 0!==e&&("object"!=typeof(t=e)&&"function"!=typeof t))throw new TypeError("".concat(r," is not an object."));var t}function N(e,r){if("function"!=typeof e)throw new TypeError("".concat(r," is not a function."))}function H(e,r){if(!function(e){return"object"==typeof e&&null!==e||"function"==typeof e}(e))throw new TypeError("".concat(r," is not an object."))}function V(e,r,t){if(void 0===e)throw new TypeError("Parameter ".concat(r," is required in '").concat(t,"'."))}function U(e,r,t){if(void 0===e)throw new TypeError("".concat(r," is required in '").concat(t,"'."))}function G(e){return Number(e)}function X(e){return 0===e?0:e}function J(e,r){var t=Number.MAX_SAFE_INTEGER,n=Number(e);if(n=X(n),!x(n))throw new TypeError("".concat(r," is not a finite number"));if((n=function(e){return X(Y(e))}(n))<0||n>t)throw new TypeError("".concat(r," is outside the accepted range of ").concat(0," to ").concat(t,", inclusive"));return x(n)&&0!==n?n:0}function K(e,r){if(!Nt(e))throw new TypeError("".concat(r," is not a ReadableStream."))}function Z(e){return new ie(e)}function $(e,r){e._reader._readRequests.push(r)}function ee(e,r,t){var n=e._reader._readRequests.shift();t?n._closeSteps():n._chunkSteps(r)}function re(e){return e._reader._readRequests.length}function te(e){var r=e._reader;return void 0!==r&&!!ue(r)}var ne,oe,ae,ie=function(){function ReadableStreamDefaultReader(e){if(V(e,1,"ReadableStreamDefaultReader"),K(e,"First parameter"),Ht(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");k(this,e),this._readRequests=new q}return Object.defineProperty(ReadableStreamDefaultReader.prototype,"closed",{get:function(){return ue(this)?this._closedPromise:_(ce("closed"))},enumerable:!1,configurable:!0}),ReadableStreamDefaultReader.prototype.cancel=function(e){return void 0===e&&(e=void 0),ue(this)?void 0===this._ownerReadableStream?_(I("cancel")):A(this,e):_(ce("cancel"))},ReadableStreamDefaultReader.prototype.read=function(){if(!ue(this))return _(ce("read"));if(void 0===this._ownerReadableStream)return _(I("read from"));var e,r,t=h((function(t,n){e=t,r=n}));return le(this,{_chunkSteps:function(r){return e({value:r,done:!1})},_closeSteps:function(){return e({value:void 0,done:!0})},_errorSteps:function(e){return r(e)}}),t},ReadableStreamDefaultReader.prototype.releaseLock=function(){if(!ue(this))throw ce("releaseLock");void 0!==this._ownerReadableStream&&function(e){z(e);var r=new TypeError("Reader was released");se(e,r)}(this)},ReadableStreamDefaultReader}();function ue(e){return!!s(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readRequests")&&e instanceof ie)}function le(e,r){var t=e._ownerReadableStream;t._disturbed=!0,"closed"===t._state?r._closeSteps():"errored"===t._state?r._errorSteps(t._storedError):t._readableStreamController[j](r)}function se(e,r){var t=e._readRequests;e._readRequests=new q,t.forEach((function(e){e._errorSteps(r)}))}function ce(e){return new TypeError("ReadableStreamDefaultReader.prototype.".concat(e," can only be used on a ReadableStreamDefaultReader"))}function fe(e){return e.slice()}function de(e,r,t,n,o){new Uint8Array(e).set(new Uint8Array(t,n,o),r)}Object.defineProperties(ie.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),f(ie.prototype.cancel,"cancel"),f(ie.prototype.read,"read"),f(ie.prototype.releaseLock,"releaseLock"),"symbol"==typeof r.toStringTag&&Object.defineProperty(ie.prototype,r.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});var be=function(e){return(be="function"==typeof e.transfer?function(e){return e.transfer()}:"function"==typeof structuredClone?function(e){return structuredClone(e,{transfer:[e]})}:function(e){return e})(e)},pe=function(e){return(pe="boolean"==typeof e.detached?function(e){return e.detached}:function(e){return 0===e.byteLength})(e)};function he(e,r,t){if(e.slice)return e.slice(r,t);var n=t-r,o=new ArrayBuffer(n);return de(o,0,e,r,n),o}function me(e,r){var t=e[r];if(null!=t){if("function"!=typeof t)throw new TypeError("".concat(String(r)," is not a function"));return t}}var _e,ye=null!==(ae=null!==(ne=r.asyncIterator)&&void 0!==ne?ne:null===(oe=r.for)||void 0===oe?void 0:oe.call(r,"Symbol.asyncIterator"))&&void 0!==ae?ae:"@@asyncIterator";function ve(e,l,c){if(void 0===l&&(l="sync"),void 0===c)if("async"===l){if(void 0===(c=me(e,ye)))return function(e){var l,s=((l={})[r.iterator]=function(){return e.iterator},l),c=function(){return a(this,arguments,(function(){return t(this,(function(e){switch(e.label){case 0:return[5,n(i(u(s)))];case 1:case 2:return[4,o.apply(void 0,[e.sent()])];case 3:return[2,e.sent()]}}))}))}();return{iterator:c,nextMethod:c.next,done:!1}}(ve(e,"sync",me(e,r.iterator)))}else c=me(e,r.iterator);if(void 0===c)throw new TypeError("The object is not iterable");var f=P(c,e,[]);if(!s(f))throw new TypeError("The iterator method must return an object");return{iterator:f,nextMethod:f.next,done:!1}}var ge=((_e={})[ye]=function(){return this},_e);Object.defineProperty(ge,ye,{enumerable:!1});var Se=function(){function e(e,r){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=r}return e.prototype.next=function(){var e=this,r=function(){return e._nextSteps()};return this._ongoingPromise=this._ongoingPromise?w(this._ongoingPromise,r,r):r(),this._ongoingPromise},e.prototype.return=function(e){var r=this,t=function(){return r._returnSteps(e)};return this._ongoingPromise?w(this._ongoingPromise,t,t):t()},e.prototype._nextSteps=function(){var e=this;if(this._isFinished)return Promise.resolve({value:void 0,done:!0});var r,t,n=this._reader,o=h((function(e,n){r=e,t=n}));return le(n,{_chunkSteps:function(t){e._ongoingPromise=void 0,T((function(){return r({value:t,done:!1})}))},_closeSteps:function(){e._ongoingPromise=void 0,e._isFinished=!0,z(n),r({value:void 0,done:!0})},_errorSteps:function(r){e._ongoingPromise=void 0,e._isFinished=!0,z(n),t(r)}}),o},e.prototype._returnSteps=function(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;var r=this._reader;if(!this._preventCancel){var t=A(r,e);return z(r),w(t,(function(){return{value:e,done:!0}}))}return z(r),m({value:e,done:!0})},e}(),we={next:function(){return Re(this)?this._asyncIteratorImpl.next():_(Te("next"))},return:function(e){return Re(this)?this._asyncIteratorImpl.return(e):_(Te("return"))}};function Re(e){if(!s(e))return!1;if(!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof Se}catch(e){return!1}}function Te(e){return new TypeError("ReadableStreamAsyncIterator.".concat(e," can only be used on a ReadableSteamAsyncIterator"))}Object.setPrototypeOf(we,ge);var Pe=Number.isNaN||function(e){return e!=e};function Ce(e){var r=he(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(r)}function qe(e){var r=e._queue.shift();return e._queueTotalSize-=r.size,e._queueTotalSize<0&&(e._queueTotalSize=0),r.value}function Ee(e,r,t){if("number"!=typeof(n=t)||Pe(n)||n<0||t===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");var n;e._queue.push({value:r,size:t}),e._queueTotalSize+=t}function Oe(e){e._queue=new q,e._queueTotalSize=0}function We(e){return e===DataView}var je=function(){function ReadableStreamBYOBRequest(){throw new TypeError("Illegal constructor")}return Object.defineProperty(ReadableStreamBYOBRequest.prototype,"view",{get:function(){if(!Ae(this))throw ir("view");return this._view},enumerable:!1,configurable:!0}),ReadableStreamBYOBRequest.prototype.respond=function(e){if(!Ae(this))throw ir("respond");if(V(e,1,"respond"),e=J(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(pe(this._view.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be used as a response");nr(this._associatedReadableByteStreamController,e)},ReadableStreamBYOBRequest.prototype.respondWithNewView=function(e){if(!Ae(this))throw ir("respondWithNewView");if(V(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw new TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(pe(e.buffer))throw new TypeError("The given view's buffer has been detached and so cannot be used as a response");or(this._associatedReadableByteStreamController,e)},ReadableStreamBYOBRequest}();Object.defineProperties(je.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),f(je.prototype.respond,"respond"),f(je.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof r.toStringTag&&Object.defineProperty(je.prototype,r.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});var Be=function(){function ReadableByteStreamController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(ReadableByteStreamController.prototype,"byobRequest",{get:function(){if(!ke(this))throw ur("byobRequest");return rr(this)},enumerable:!1,configurable:!0}),Object.defineProperty(ReadableByteStreamController.prototype,"desiredSize",{get:function(){if(!ke(this))throw ur("desiredSize");return tr(this)},enumerable:!1,configurable:!0}),ReadableByteStreamController.prototype.close=function(){if(!ke(this))throw ur("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");var e=this._controlledReadableByteStream._state;if("readable"!==e)throw new TypeError("The stream (in ".concat(e," state) is not in the readable state and cannot be closed"));Ke(this)},ReadableByteStreamController.prototype.enqueue=function(e){if(!ke(this))throw ur("enqueue");if(V(e,1,"enqueue"),!ArrayBuffer.isView(e))throw new TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw new TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");var r=this._controlledReadableByteStream._state;if("readable"!==r)throw new TypeError("The stream (in ".concat(r," state) is not in the readable state and cannot be enqueued to"));Ze(this,e)},ReadableByteStreamController.prototype.error=function(e){if(void 0===e&&(e=void 0),!ke(this))throw ur("error");$e(this,e)},ReadableByteStreamController.prototype[W]=function(e){Ie(this),Oe(this);var r=this._cancelAlgorithm(e);return Je(this),r},ReadableByteStreamController.prototype[j]=function(e){var r=this._controlledReadableByteStream;if(this._queueTotalSize>0)er(this,e);else{var t=this._autoAllocateChunkSize;if(void 0!==t){var n=void 0;try{n=new ArrayBuffer(t)}catch(r){return void e._errorSteps(r)}var o={buffer:n,bufferByteLength:t,byteOffset:0,byteLength:t,bytesFilled:0,minimumFill:1,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(o)}$(r,e),ze(this)}},ReadableByteStreamController.prototype[B]=function(){if(this._pendingPullIntos.length>0){var e=this._pendingPullIntos.peek();e.readerType="none",this._pendingPullIntos=new q,this._pendingPullIntos.push(e)}},ReadableByteStreamController}();function ke(e){return!!s(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")&&e instanceof Be)}function Ae(e){return!!s(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")&&e instanceof je)}function ze(e){var r=function(e){var r=e._controlledReadableByteStream;if("readable"!==r._state)return!1;if(e._closeRequested)return!1;if(!e._started)return!1;if(te(r)&&re(r)>0)return!0;if(dr(r)&&fr(r)>0)return!0;var t=tr(e);if(t>0)return!0;return!1}(e);r&&(e._pulling?e._pullAgain=!0:(e._pulling=!0,v(e._pullAlgorithm(),(function(){return e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,ze(e)),null}),(function(r){return $e(e,r),null}))))}function Ie(e){He(e),e._pendingPullIntos=new q}function De(e,r){var t=!1;"closed"===e._state&&(t=!0);var n=Le(r);"default"===r.readerType?ee(e,n,t):function(e,r,t){var n=e._reader,o=n._readIntoRequests.shift();t?o._closeSteps(r):o._chunkSteps(r)}(e,n,t)}function Le(e){var r=e.bytesFilled,t=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,r/t)}function Fe(e,r,t,n){e._queue.push({buffer:r,byteOffset:t,byteLength:n}),e._queueTotalSize+=n}function Me(e,r,t,n){var o;try{o=he(r,t,t+n)}catch(r){throw $e(e,r),r}Fe(e,o,0,n)}function xe(e,r){r.bytesFilled>0&&Me(e,r.buffer,r.byteOffset,r.bytesFilled),Xe(e)}function Ye(e,r){var t=Math.min(e._queueTotalSize,r.byteLength-r.bytesFilled),n=r.bytesFilled+t,o=t,a=!1,i=n-n%r.elementSize;i>=r.minimumFill&&(o=i-r.bytesFilled,a=!0);for(var u=e._queue;o>0;){var l=u.peek(),s=Math.min(o,l.byteLength),c=r.byteOffset+r.bytesFilled;de(r.buffer,c,l.buffer,l.byteOffset,s),l.byteLength===s?u.shift():(l.byteOffset+=s,l.byteLength-=s),e._queueTotalSize-=s,Qe(e,s,r),o-=s}return a}function Qe(e,r,t){t.bytesFilled+=r}function Ne(e){0===e._queueTotalSize&&e._closeRequested?(Je(e),Ut(e._controlledReadableByteStream)):ze(e)}function He(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function Ve(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;var r=e._pendingPullIntos.peek();Ye(e,r)&&(Xe(e),De(e._controlledReadableByteStream,r))}}function Ue(e,r,t,n){var o,a=e._controlledReadableByteStream,i=r.constructor,u=function(e){return We(e)?1:e.BYTES_PER_ELEMENT}(i),l=r.byteOffset,s=r.byteLength,c=t*u;try{o=be(r.buffer)}catch(b){return void n._errorSteps(b)}var f={buffer:o,bufferByteLength:o.byteLength,byteOffset:l,byteLength:s,bytesFilled:0,minimumFill:c,elementSize:u,viewConstructor:i,readerType:"byob"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(f),void cr(a,n);if("closed"!==a._state){if(e._queueTotalSize>0){if(Ye(e,f)){var d=Le(f);return Ne(e),void n._chunkSteps(d)}if(e._closeRequested){var b=new TypeError("Insufficient bytes to fill elements in the given buffer");return $e(e,b),void n._errorSteps(b)}}e._pendingPullIntos.push(f),cr(a,n),ze(e)}else{var p=new i(f.buffer,f.byteOffset,0);n._closeSteps(p)}}function Ge(e,r){var t=e._pendingPullIntos.peek();He(e),"closed"===e._controlledReadableByteStream._state?function(e,r){"none"===r.readerType&&Xe(e);var t=e._controlledReadableByteStream;if(dr(t))for(;fr(t)>0;)De(t,Xe(e))}(e,t):function(e,r,t){if(Qe(0,r,t),"none"===t.readerType)return xe(e,t),void Ve(e);if(!(t.bytesFilled<t.minimumFill)){Xe(e);var n=t.bytesFilled%t.elementSize;if(n>0){var o=t.byteOffset+t.bytesFilled;Me(e,t.buffer,o-n,n)}t.bytesFilled-=n,De(e._controlledReadableByteStream,t),Ve(e)}}(e,r,t),ze(e)}function Xe(e){return e._pendingPullIntos.shift()}function Je(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function Ke(e){var r=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===r._state)if(e._queueTotalSize>0)e._closeRequested=!0;else{if(e._pendingPullIntos.length>0){var t=e._pendingPullIntos.peek();if(t.bytesFilled%t.elementSize!=0){var n=new TypeError("Insufficient bytes to fill elements in the given buffer");throw $e(e,n),n}}Je(e),Ut(r)}}function Ze(e,r){var t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state){var n=r.buffer,o=r.byteOffset,a=r.byteLength;if(pe(n))throw new TypeError("chunk's buffer is detached and so cannot be enqueued");var i=be(n);if(e._pendingPullIntos.length>0){var u=e._pendingPullIntos.peek();if(pe(u.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk");He(e),u.buffer=be(u.buffer),"none"===u.readerType&&xe(e,u)}if(te(t))if(function(e){for(var r=e._controlledReadableByteStream._reader;r._readRequests.length>0;){if(0===e._queueTotalSize)return;er(e,r._readRequests.shift())}}(e),0===re(t))Fe(e,i,o,a);else e._pendingPullIntos.length>0&&Xe(e),ee(t,new Uint8Array(i,o,a),!1);else dr(t)?(Fe(e,i,o,a),Ve(e)):Fe(e,i,o,a);ze(e)}}function $e(e,r){var t=e._controlledReadableByteStream;"readable"===t._state&&(Ie(e),Oe(e),Je(e),Gt(t,r))}function er(e,r){var t=e._queue.shift();e._queueTotalSize-=t.byteLength,Ne(e);var n=new Uint8Array(t.buffer,t.byteOffset,t.byteLength);r._chunkSteps(n)}function rr(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){var r=e._pendingPullIntos.peek(),t=new Uint8Array(r.buffer,r.byteOffset+r.bytesFilled,r.byteLength-r.bytesFilled),n=Object.create(je.prototype);!function(e,r,t){e._associatedReadableByteStreamController=r,e._view=t}(n,e,t),e._byobRequest=n}return e._byobRequest}function tr(e){var r=e._controlledReadableByteStream._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function nr(e,r){var t=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==r)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===r)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(t.bytesFilled+r>t.byteLength)throw new RangeError("bytesWritten out of range")}t.buffer=be(t.buffer),Ge(e,r)}function or(e,r){var t=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==r.byteLength)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===r.byteLength)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(t.byteOffset+t.bytesFilled!==r.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(t.bufferByteLength!==r.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(t.bytesFilled+r.byteLength>t.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");var n=r.byteLength;t.buffer=be(r.buffer),Ge(e,n)}function ar(e,r,t,n,o,a,i){r._controlledReadableByteStream=e,r._pullAgain=!1,r._pulling=!1,r._byobRequest=null,r._queue=r._queueTotalSize=void 0,Oe(r),r._closeRequested=!1,r._started=!1,r._strategyHWM=a,r._pullAlgorithm=n,r._cancelAlgorithm=o,r._autoAllocateChunkSize=i,r._pendingPullIntos=new q,e._readableStreamController=r,v(m(t()),(function(){return r._started=!0,ze(r),null}),(function(e){return $e(r,e),null}))}function ir(e){return new TypeError("ReadableStreamBYOBRequest.prototype.".concat(e," can only be used on a ReadableStreamBYOBRequest"))}function ur(e){return new TypeError("ReadableByteStreamController.prototype.".concat(e," can only be used on a ReadableByteStreamController"))}function lr(e,r){if("byob"!==(e="".concat(e)))throw new TypeError("".concat(r," '").concat(e,"' is not a valid enumeration value for ReadableStreamReaderMode"));return e}function sr(e){return new br(e)}function cr(e,r){e._reader._readIntoRequests.push(r)}function fr(e){return e._reader._readIntoRequests.length}function dr(e){var r=e._reader;return void 0!==r&&!!pr(r)}Object.defineProperties(Be.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),f(Be.prototype.close,"close"),f(Be.prototype.enqueue,"enqueue"),f(Be.prototype.error,"error"),"symbol"==typeof r.toStringTag&&Object.defineProperty(Be.prototype,r.toStringTag,{value:"ReadableByteStreamController",configurable:!0});var br=function(){function ReadableStreamBYOBReader(e){if(V(e,1,"ReadableStreamBYOBReader"),K(e,"First parameter"),Ht(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!ke(e._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");k(this,e),this._readIntoRequests=new q}return Object.defineProperty(ReadableStreamBYOBReader.prototype,"closed",{get:function(){return pr(this)?this._closedPromise:_(_r("closed"))},enumerable:!1,configurable:!0}),ReadableStreamBYOBReader.prototype.cancel=function(e){return void 0===e&&(e=void 0),pr(this)?void 0===this._ownerReadableStream?_(I("cancel")):A(this,e):_(_r("cancel"))},ReadableStreamBYOBReader.prototype.read=function(e,r){if(void 0===r&&(r={}),!pr(this))return _(_r("read"));if(!ArrayBuffer.isView(e))return _(new TypeError("view must be an array buffer view"));if(0===e.byteLength)return _(new TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return _(new TypeError("view's buffer must have non-zero byteLength"));if(pe(e.buffer))return _(new TypeError("view's buffer has been detached"));var t;try{t=function(e,r){var t;return Q(e,r),{min:J(null!==(t=null==e?void 0:e.min)&&void 0!==t?t:1,"".concat(r," has member 'min' that"))}}(r,"options")}catch(e){return _(e)}var n,o,a=t.min;if(0===a)return _(new TypeError("options.min must be greater than 0"));if(function(e){return We(e.constructor)}(e)){if(a>e.byteLength)return _(new RangeError("options.min must be less than or equal to view's byteLength"))}else if(a>e.length)return _(new RangeError("options.min must be less than or equal to view's length"));if(void 0===this._ownerReadableStream)return _(I("read from"));var i=h((function(e,r){n=e,o=r}));return hr(this,e,a,{_chunkSteps:function(e){return n({value:e,done:!1})},_closeSteps:function(e){return n({value:e,done:!0})},_errorSteps:function(e){return o(e)}}),i},ReadableStreamBYOBReader.prototype.releaseLock=function(){if(!pr(this))throw _r("releaseLock");void 0!==this._ownerReadableStream&&function(e){z(e);var r=new TypeError("Reader was released");mr(e,r)}(this)},ReadableStreamBYOBReader}();function pr(e){return!!s(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")&&e instanceof br)}function hr(e,r,t,n){var o=e._ownerReadableStream;o._disturbed=!0,"errored"===o._state?n._errorSteps(o._storedError):Ue(o._readableStreamController,r,t,n)}function mr(e,r){var t=e._readIntoRequests;e._readIntoRequests=new q,t.forEach((function(e){e._errorSteps(r)}))}function _r(e){return new TypeError("ReadableStreamBYOBReader.prototype.".concat(e," can only be used on a ReadableStreamBYOBReader"))}function yr(e,r){var t=e.highWaterMark;if(void 0===t)return r;if(Pe(t)||t<0)throw new RangeError("Invalid highWaterMark");return t}function vr(e){var r=e.size;return r||function(){return 1}}function gr(e,r){Q(e,r);var t=null==e?void 0:e.highWaterMark,n=null==e?void 0:e.size;return{highWaterMark:void 0===t?void 0:G(t),size:void 0===n?void 0:Sr(n,"".concat(r," has member 'size' that"))}}function Sr(e,r){return N(e,r),function(r){return G(e(r))}}function wr(e,r,t){return N(e,t),function(t){return C(e,r,[t])}}function Rr(e,r,t){return N(e,t),function(){return C(e,r,[])}}function Tr(e,r,t){return N(e,t),function(t){return P(e,r,[t])}}function Pr(e,r,t){return N(e,t),function(t,n){return C(e,r,[t,n])}}function Cr(e,r){if(!jr(e))throw new TypeError("".concat(r," is not a WritableStream."))}Object.defineProperties(br.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),f(br.prototype.cancel,"cancel"),f(br.prototype.read,"read"),f(br.prototype.releaseLock,"releaseLock"),"symbol"==typeof r.toStringTag&&Object.defineProperty(br.prototype,r.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});var qr="function"==typeof AbortController;var Er=function(){function WritableStream(e,r){void 0===e&&(e={}),void 0===r&&(r={}),void 0===e?e=null:H(e,"First parameter");var t=gr(r,"Second parameter"),n=function(e,r){Q(e,r);var t=null==e?void 0:e.abort,n=null==e?void 0:e.close,o=null==e?void 0:e.start,a=null==e?void 0:e.type,i=null==e?void 0:e.write;return{abort:void 0===t?void 0:wr(t,e,"".concat(r," has member 'abort' that")),close:void 0===n?void 0:Rr(n,e,"".concat(r," has member 'close' that")),start:void 0===o?void 0:Tr(o,e,"".concat(r," has member 'start' that")),write:void 0===i?void 0:Pr(i,e,"".concat(r," has member 'write' that")),type:a}}(e,"First parameter");if(Wr(this),void 0!==n.type)throw new RangeError("Invalid type is specified");var o=vr(t);!function(e,r,t,n){var o,a,i,u,l=Object.create(Xr.prototype);o=void 0!==r.start?function(){return r.start(l)}:function(){};a=void 0!==r.write?function(e){return r.write(e,l)}:function(){return m(void 0)};i=void 0!==r.close?function(){return r.close()}:function(){return m(void 0)};u=void 0!==r.abort?function(e){return r.abort(e)}:function(){return m(void 0)};Kr(e,l,o,a,i,u,t,n)}(this,n,yr(t,1),o)}return Object.defineProperty(WritableStream.prototype,"locked",{get:function(){if(!jr(this))throw ot("locked");return Br(this)},enumerable:!1,configurable:!0}),WritableStream.prototype.abort=function(e){return void 0===e&&(e=void 0),jr(this)?Br(this)?_(new TypeError("Cannot abort a stream that already has a writer")):kr(this,e):_(ot("abort"))},WritableStream.prototype.close=function(){return jr(this)?Br(this)?_(new TypeError("Cannot close a stream that already has a writer")):Lr(this)?_(new TypeError("Cannot close an already-closing stream")):Ar(this):_(ot("close"))},WritableStream.prototype.getWriter=function(){if(!jr(this))throw ot("getWriter");return Or(this)},WritableStream}();function Or(e){return new xr(e)}function Wr(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new q,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function jr(e){return!!s(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")&&e instanceof Er)}function Br(e){return void 0!==e._writer}function kr(e,r){var t;if("closed"===e._state||"errored"===e._state)return m(void 0);e._writableStreamController._abortReason=r,null===(t=e._writableStreamController._abortController)||void 0===t||t.abort(r);var n=e._state;if("closed"===n||"errored"===n)return m(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;var o=!1;"erroring"===n&&(o=!0,r=void 0);var a=h((function(t,n){e._pendingAbortRequest={_promise:void 0,_resolve:t,_reject:n,_reason:r,_wasAlreadyErroring:o}}));return e._pendingAbortRequest._promise=a,o||Ir(e,r),a}function Ar(e){var r=e._state;if("closed"===r||"errored"===r)return _(new TypeError("The stream (in ".concat(r," state) is not in the writable state and cannot be closed")));var t,n=h((function(r,t){var n={_resolve:r,_reject:t};e._closeRequest=n})),o=e._writer;return void 0!==o&&e._backpressure&&"writable"===r&&mt(o),Ee(t=e._writableStreamController,Gr,0),et(t),n}function zr(e,r){"writable"!==e._state?Dr(e):Ir(e,r)}function Ir(e,r){var t=e._writableStreamController;e._state="erroring",e._storedError=r;var n=e._writer;void 0!==n&&Hr(n,r),!function(e){if(void 0===e._inFlightWriteRequest&&void 0===e._inFlightCloseRequest)return!1;return!0}(e)&&t._started&&Dr(e)}function Dr(e){e._state="errored",e._writableStreamController[O]();var r=e._storedError;if(e._writeRequests.forEach((function(e){e._reject(r)})),e._writeRequests=new q,void 0!==e._pendingAbortRequest){var t=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,t._wasAlreadyErroring)return t._reject(r),void Fr(e);v(e._writableStreamController[E](t._reason),(function(){return t._resolve(),Fr(e),null}),(function(r){return t._reject(r),Fr(e),null}))}else Fr(e)}function Lr(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function Fr(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);var r=e._writer;void 0!==r&&ct(r,e._storedError)}function Mr(e,r){var t=e._writer;void 0!==t&&r!==e._backpressure&&(r?function(e){dt(e)}(t):mt(t)),e._backpressure=r}Object.defineProperties(Er.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),f(Er.prototype.abort,"abort"),f(Er.prototype.close,"close"),f(Er.prototype.getWriter,"getWriter"),"symbol"==typeof r.toStringTag&&Object.defineProperty(Er.prototype,r.toStringTag,{value:"WritableStream",configurable:!0});var xr=function(){function WritableStreamDefaultWriter(e){if(V(e,1,"WritableStreamDefaultWriter"),Cr(e,"First parameter"),Br(e))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;var r,t=e._state;if("writable"===t)!Lr(e)&&e._backpressure?dt(this):pt(this),lt(this);else if("erroring"===t)bt(this,e._storedError),lt(this);else if("closed"===t)pt(this),lt(r=this),ft(r);else{var n=e._storedError;bt(this,n),st(this,n)}}return Object.defineProperty(WritableStreamDefaultWriter.prototype,"closed",{get:function(){return Yr(this)?this._closedPromise:_(it("closed"))},enumerable:!1,configurable:!0}),Object.defineProperty(WritableStreamDefaultWriter.prototype,"desiredSize",{get:function(){if(!Yr(this))throw it("desiredSize");if(void 0===this._ownerWritableStream)throw ut("desiredSize");return function(e){var r=e._ownerWritableStream,t=r._state;if("errored"===t||"erroring"===t)return null;if("closed"===t)return 0;return $r(r._writableStreamController)}(this)},enumerable:!1,configurable:!0}),Object.defineProperty(WritableStreamDefaultWriter.prototype,"ready",{get:function(){return Yr(this)?this._readyPromise:_(it("ready"))},enumerable:!1,configurable:!0}),WritableStreamDefaultWriter.prototype.abort=function(e){return void 0===e&&(e=void 0),Yr(this)?void 0===this._ownerWritableStream?_(ut("abort")):function(e,r){return kr(e._ownerWritableStream,r)}(this,e):_(it("abort"))},WritableStreamDefaultWriter.prototype.close=function(){if(!Yr(this))return _(it("close"));var e=this._ownerWritableStream;return void 0===e?_(ut("close")):Lr(e)?_(new TypeError("Cannot close an already-closing stream")):Qr(this)},WritableStreamDefaultWriter.prototype.releaseLock=function(){if(!Yr(this))throw it("releaseLock");void 0!==this._ownerWritableStream&&Vr(this)},WritableStreamDefaultWriter.prototype.write=function(e){return void 0===e&&(e=void 0),Yr(this)?void 0===this._ownerWritableStream?_(ut("write to")):Ur(this,e):_(it("write"))},WritableStreamDefaultWriter}();function Yr(e){return!!s(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")&&e instanceof xr)}function Qr(e){return Ar(e._ownerWritableStream)}function Nr(e,r){"pending"===e._closedPromiseState?ct(e,r):function(e,r){st(e,r)}(e,r)}function Hr(e,r){"pending"===e._readyPromiseState?ht(e,r):function(e,r){bt(e,r)}(e,r)}function Vr(e){var r=e._ownerWritableStream,t=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");Hr(e,t),Nr(e,t),r._writer=void 0,e._ownerWritableStream=void 0}function Ur(e,r){var t=e._ownerWritableStream,n=t._writableStreamController,o=function(e,r){try{return e._strategySizeAlgorithm(r)}catch(r){return rt(e,r),1}}(n,r);if(t!==e._ownerWritableStream)return _(ut("write to"));var a=t._state;if("errored"===a)return _(t._storedError);if(Lr(t)||"closed"===a)return _(new TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===a)return _(t._storedError);var i=function(e){return h((function(r,t){var n={_resolve:r,_reject:t};e._writeRequests.push(n)}))}(t);return function(e,r,t){try{Ee(e,r,t)}catch(r){return void rt(e,r)}var n=e._controlledWritableStream;if(!Lr(n)&&"writable"===n._state){Mr(n,tt(e))}et(e)}(n,r,o),i}Object.defineProperties(xr.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),f(xr.prototype.abort,"abort"),f(xr.prototype.close,"close"),f(xr.prototype.releaseLock,"releaseLock"),f(xr.prototype.write,"write"),"symbol"==typeof r.toStringTag&&Object.defineProperty(xr.prototype,r.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});var Gr={},Xr=function(){function WritableStreamDefaultController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(WritableStreamDefaultController.prototype,"abortReason",{get:function(){if(!Jr(this))throw at("abortReason");return this._abortReason},enumerable:!1,configurable:!0}),Object.defineProperty(WritableStreamDefaultController.prototype,"signal",{get:function(){if(!Jr(this))throw at("signal");if(void 0===this._abortController)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal},enumerable:!1,configurable:!0}),WritableStreamDefaultController.prototype.error=function(e){if(void 0===e&&(e=void 0),!Jr(this))throw at("error");"writable"===this._controlledWritableStream._state&&nt(this,e)},WritableStreamDefaultController.prototype[E]=function(e){var r=this._abortAlgorithm(e);return Zr(this),r},WritableStreamDefaultController.prototype[O]=function(){Oe(this)},WritableStreamDefaultController}();function Jr(e){return!!s(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")&&e instanceof Xr)}function Kr(e,r,t,n,o,a,i,u){r._controlledWritableStream=e,e._writableStreamController=r,r._queue=void 0,r._queueTotalSize=void 0,Oe(r),r._abortReason=void 0,r._abortController=function(){if(qr)return new AbortController}(),r._started=!1,r._strategySizeAlgorithm=u,r._strategyHWM=i,r._writeAlgorithm=n,r._closeAlgorithm=o,r._abortAlgorithm=a;var l=tt(r);Mr(e,l),v(m(t()),(function(){return r._started=!0,et(r),null}),(function(t){return r._started=!0,zr(e,t),null}))}function Zr(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function $r(e){return e._strategyHWM-e._queueTotalSize}function et(e){var r=e._controlledWritableStream;if(e._started&&void 0===r._inFlightWriteRequest)if("erroring"!==r._state){if(0!==e._queue.length){var t=e._queue.peek().value;t===Gr?function(e){var r=e._controlledWritableStream;(function(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0})(r),qe(e);var t=e._closeAlgorithm();Zr(e),v(t,(function(){return function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";var r=e._writer;void 0!==r&&ft(r)}(r),null}),(function(e){return function(e,r){e._inFlightCloseRequest._reject(r),e._inFlightCloseRequest=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(r),e._pendingAbortRequest=void 0),zr(e,r)}(r,e),null}))}(e):function(e,r){var t=e._controlledWritableStream;!function(e){e._inFlightWriteRequest=e._writeRequests.shift()}(t);var n=e._writeAlgorithm(r);v(n,(function(){!function(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}(t);var r=t._state;if(qe(e),!Lr(t)&&"writable"===r){var n=tt(e);Mr(t,n)}return et(e),null}),(function(r){return"writable"===t._state&&Zr(e),function(e,r){e._inFlightWriteRequest._reject(r),e._inFlightWriteRequest=void 0,zr(e,r)}(t,r),null}))}(e,t)}}else Dr(r)}function rt(e,r){"writable"===e._controlledWritableStream._state&&nt(e,r)}function tt(e){return $r(e)<=0}function nt(e,r){var t=e._controlledWritableStream;Zr(e),Ir(t,r)}function ot(e){return new TypeError("WritableStream.prototype.".concat(e," can only be used on a WritableStream"))}function at(e){return new TypeError("WritableStreamDefaultController.prototype.".concat(e," can only be used on a WritableStreamDefaultController"))}function it(e){return new TypeError("WritableStreamDefaultWriter.prototype.".concat(e," can only be used on a WritableStreamDefaultWriter"))}function ut(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function lt(e){e._closedPromise=h((function(r,t){e._closedPromise_resolve=r,e._closedPromise_reject=t,e._closedPromiseState="pending"}))}function st(e,r){lt(e),ct(e,r)}function ct(e,r){void 0!==e._closedPromise_reject&&(R(e._closedPromise),e._closedPromise_reject(r),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function ft(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function dt(e){e._readyPromise=h((function(r,t){e._readyPromise_resolve=r,e._readyPromise_reject=t})),e._readyPromiseState="pending"}function bt(e,r){dt(e),ht(e,r)}function pt(e){dt(e),mt(e)}function ht(e,r){void 0!==e._readyPromise_reject&&(R(e._readyPromise),e._readyPromise_reject(r),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function mt(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(Xr.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(Xr.prototype,r.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});var _t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof global?global:void 0;var yt,vt=(function(e){if("function"!=typeof e&&"object"!=typeof e)return!1;if("DOMException"!==e.name)return!1;try{return new e,!0}catch(e){return!1}}(yt=null==_t?void 0:_t.DOMException)?yt:void 0)||function(){var e=function(e,r){this.message=e||"",this.name=r||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return f(e,"DOMException"),e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}();function gt(e,r,t,n,o,a){var i=Z(e),u=Or(r);e._disturbed=!0;var s=!1,c=m(void 0);return h((function(f,d){var b,p,w,T;if(void 0!==a){if(b=function(){var t=void 0!==a.reason?a.reason:new vt("Aborted","AbortError"),i=[];n||i.push((function(){return"writable"===r._state?kr(r,t):m(void 0)})),o||i.push((function(){return"readable"===e._state?Vt(e,t):m(void 0)})),E((function(){return Promise.all(i.map((function(e){return e()})))}),!0,t)},a.aborted)return void b();a.addEventListener("abort",b)}if(q(e,i._closedPromise,(function(e){return n?O(!0,e):E((function(){return kr(r,e)}),!0,e),null})),q(r,u._closedPromise,(function(r){return o?O(!0,r):E((function(){return Vt(e,r)}),!0,r),null})),p=e,w=i._closedPromise,T=function(){return t?O():E((function(){return function(e){var r=e._ownerWritableStream,t=r._state;return Lr(r)||"closed"===t?m(void 0):"errored"===t?_(r._storedError):Qr(e)}(u)})),null},"closed"===p._state?T():g(w,T),Lr(r)||"closed"===r._state){var P=new TypeError("the destination writable stream closed before all data could be piped to it");o?O(!0,P):E((function(){return Vt(e,P)}),!0,P)}function C(){var e=c;return y(c,(function(){return e!==c?C():void 0}))}function q(e,r,t){"errored"===e._state?t(e._storedError):S(r,t)}function E(e,t,n){function o(){return v(e(),(function(){return W(t,n)}),(function(e){return W(!0,e)})),null}s||(s=!0,"writable"!==r._state||Lr(r)?o():g(C(),o))}function O(e,t){s||(s=!0,"writable"!==r._state||Lr(r)?W(e,t):g(C(),(function(){return W(e,t)})))}function W(e,r){return Vr(u),z(i),void 0!==a&&a.removeEventListener("abort",b),e?d(r):f(void 0),null}R(h((function(e,r){!function t(n){n?e():y(s?m(!0):y(u._readyPromise,(function(){return h((function(e,r){le(i,{_chunkSteps:function(r){c=y(Ur(u,r),void 0,l),e(!1)},_closeSteps:function(){return e(!0)},_errorSteps:r})}))})),t,r)}(!1)})))}))}var St=function(){function ReadableStreamDefaultController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(ReadableStreamDefaultController.prototype,"desiredSize",{get:function(){if(!wt(this))throw Bt("desiredSize");return Ot(this)},enumerable:!1,configurable:!0}),ReadableStreamDefaultController.prototype.close=function(){if(!wt(this))throw Bt("close");if(!Wt(this))throw new TypeError("The stream is not in a state that permits close");Ct(this)},ReadableStreamDefaultController.prototype.enqueue=function(e){if(void 0===e&&(e=void 0),!wt(this))throw Bt("enqueue");if(!Wt(this))throw new TypeError("The stream is not in a state that permits enqueue");return qt(this,e)},ReadableStreamDefaultController.prototype.error=function(e){if(void 0===e&&(e=void 0),!wt(this))throw Bt("error");Et(this,e)},ReadableStreamDefaultController.prototype[W]=function(e){Oe(this);var r=this._cancelAlgorithm(e);return Pt(this),r},ReadableStreamDefaultController.prototype[j]=function(e){var r=this._controlledReadableStream;if(this._queue.length>0){var t=qe(this);this._closeRequested&&0===this._queue.length?(Pt(this),Ut(r)):Rt(this),e._chunkSteps(t)}else $(r,e),Rt(this)},ReadableStreamDefaultController.prototype[B]=function(){},ReadableStreamDefaultController}();function wt(e){return!!s(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")&&e instanceof St)}function Rt(e){Tt(e)&&(e._pulling?e._pullAgain=!0:(e._pulling=!0,v(e._pullAlgorithm(),(function(){return e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,Rt(e)),null}),(function(r){return Et(e,r),null}))))}function Tt(e){var r=e._controlledReadableStream;return!!Wt(e)&&(!!e._started&&(!!(Ht(r)&&re(r)>0)||Ot(e)>0))}function Pt(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function Ct(e){if(Wt(e)){var r=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(Pt(e),Ut(r))}}function qt(e,r){if(Wt(e)){var t=e._controlledReadableStream;if(Ht(t)&&re(t)>0)ee(t,r,!1);else{var n=void 0;try{n=e._strategySizeAlgorithm(r)}catch(r){throw Et(e,r),r}try{Ee(e,r,n)}catch(r){throw Et(e,r),r}}Rt(e)}}function Et(e,r){var t=e._controlledReadableStream;"readable"===t._state&&(Oe(e),Pt(e),Gt(t,r))}function Ot(e){var r=e._controlledReadableStream._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function Wt(e){var r=e._controlledReadableStream._state;return!e._closeRequested&&"readable"===r}function jt(e,r,t,n,o,a,i){r._controlledReadableStream=e,r._queue=void 0,r._queueTotalSize=void 0,Oe(r),r._started=!1,r._closeRequested=!1,r._pullAgain=!1,r._pulling=!1,r._strategySizeAlgorithm=i,r._strategyHWM=a,r._pullAlgorithm=n,r._cancelAlgorithm=o,e._readableStreamController=r,v(m(t()),(function(){return r._started=!0,Rt(r),null}),(function(e){return Et(r,e),null}))}function Bt(e){return new TypeError("ReadableStreamDefaultController.prototype.".concat(e," can only be used on a ReadableStreamDefaultController"))}function kt(e,r){return ke(e._readableStreamController)?function(e){var r,t,n,o,a,i=Z(e),u=!1,l=!1,s=!1,c=!1,f=!1,d=h((function(e){a=e}));function b(e){S(e._closedPromise,(function(r){return e!==i||($e(n._readableStreamController,r),$e(o._readableStreamController,r),c&&f||a(void 0)),null}))}function p(){pr(i)&&(z(i),b(i=Z(e))),le(i,{_chunkSteps:function(r){T((function(){l=!1,s=!1;var t=r,i=r;if(!c&&!f)try{i=Ce(r)}catch(r){return $e(n._readableStreamController,r),$e(o._readableStreamController,r),void a(Vt(e,r))}c||Ze(n._readableStreamController,t),f||Ze(o._readableStreamController,i),u=!1,l?y():s&&v()}))},_closeSteps:function(){u=!1,c||Ke(n._readableStreamController),f||Ke(o._readableStreamController),n._readableStreamController._pendingPullIntos.length>0&&nr(n._readableStreamController,0),o._readableStreamController._pendingPullIntos.length>0&&nr(o._readableStreamController,0),c&&f||a(void 0)},_errorSteps:function(){u=!1}})}function _(r,t){ue(i)&&(z(i),b(i=sr(e)));var d=t?o:n,p=t?n:o;hr(i,r,1,{_chunkSteps:function(r){T((function(){l=!1,s=!1;var n=t?f:c;if(t?c:f)n||or(d._readableStreamController,r);else{var o=void 0;try{o=Ce(r)}catch(r){return $e(d._readableStreamController,r),$e(p._readableStreamController,r),void a(Vt(e,r))}n||or(d._readableStreamController,r),Ze(p._readableStreamController,o)}u=!1,l?y():s&&v()}))},_closeSteps:function(e){u=!1;var r=t?f:c,n=t?c:f;r||Ke(d._readableStreamController),n||Ke(p._readableStreamController),void 0!==e&&(r||or(d._readableStreamController,e),!n&&p._readableStreamController._pendingPullIntos.length>0&&nr(p._readableStreamController,0)),r&&n||a(void 0)},_errorSteps:function(){u=!1}})}function y(){if(u)return l=!0,m(void 0);u=!0;var e=rr(n._readableStreamController);return null===e?p():_(e._view,!1),m(void 0)}function v(){if(u)return s=!0,m(void 0);u=!0;var e=rr(o._readableStreamController);return null===e?p():_(e._view,!0),m(void 0)}function g(n){if(c=!0,r=n,f){var o=fe([r,t]),i=Vt(e,o);a(i)}return d}function w(n){if(f=!0,t=n,c){var o=fe([r,t]),i=Vt(e,o);a(i)}return d}function R(){}return n=Yt(R,y,g),o=Yt(R,v,w),b(i),[n,o]}(e):function(e,r){var t,n,o,a,i,u=Z(e),l=!1,s=!1,c=!1,f=!1,d=h((function(e){i=e}));function b(){return l?(s=!0,m(void 0)):(l=!0,le(u,{_chunkSteps:function(e){T((function(){s=!1;var r=e,t=e;c||qt(o._readableStreamController,r),f||qt(a._readableStreamController,t),l=!1,s&&b()}))},_closeSteps:function(){l=!1,c||Ct(o._readableStreamController),f||Ct(a._readableStreamController),c&&f||i(void 0)},_errorSteps:function(){l=!1}}),m(void 0))}function p(r){if(c=!0,t=r,f){var o=fe([t,n]),a=Vt(e,o);i(a)}return d}function _(r){if(f=!0,n=r,c){var o=fe([t,n]),a=Vt(e,o);i(a)}return d}function y(){}return o=xt(y,b,p),a=xt(y,b,_),S(u._closedPromise,(function(e){return Et(o._readableStreamController,e),Et(a._readableStreamController,e),c&&f||i(void 0),null})),[o,a]}(e)}function At(e){return s(r=e)&&void 0!==r.getReader?function(e){var r;function t(){var t;try{t=e.read()}catch(e){return _(e)}return w(t,(function(e){if(!s(e))throw new TypeError("The promise returned by the reader.read() method must fulfill with an object");if(e.done)Ct(r._readableStreamController);else{var t=e.value;qt(r._readableStreamController,t)}}))}function n(r){try{return m(e.cancel(r))}catch(e){return _(e)}}return r=xt(l,t,n,0),r}(e.getReader()):function(e){var r,t=ve(e,"async");function n(){var e;try{e=function(e){var r=P(e.nextMethod,e.iterator,[]);if(!s(r))throw new TypeError("The iterator.next() method must return an object");return r}(t)}catch(e){return _(e)}return w(m(e),(function(e){if(!s(e))throw new TypeError("The promise returned by the iterator.next() method must fulfill with an object");var t=function(e){return Boolean(e.done)}(e);if(t)Ct(r._readableStreamController);else{var n=function(e){return e.value}(e);qt(r._readableStreamController,n)}}))}function o(e){var r,n,o=t.iterator;try{r=me(o,"return")}catch(e){return _(e)}if(void 0===r)return m(void 0);try{n=P(r,o,[e])}catch(e){return _(e)}return w(m(n),(function(e){if(!s(e))throw new TypeError("The promise returned by the iterator.return() method must fulfill with an object")}))}return r=xt(l,n,o,0),r}(e);var r}function zt(e,r,t){return N(e,t),function(t){return C(e,r,[t])}}function It(e,r,t){return N(e,t),function(t){return C(e,r,[t])}}function Dt(e,r,t){return N(e,t),function(t){return P(e,r,[t])}}function Lt(e,r){if("bytes"!==(e="".concat(e)))throw new TypeError("".concat(r," '").concat(e,"' is not a valid enumeration value for ReadableStreamType"));return e}function Ft(e,r){Q(e,r);var t=null==e?void 0:e.preventAbort,n=null==e?void 0:e.preventCancel,o=null==e?void 0:e.preventClose,a=null==e?void 0:e.signal;return void 0!==a&&function(e,r){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw new TypeError("".concat(r," is not an AbortSignal."))}(a,"".concat(r," has member 'signal' that")),{preventAbort:Boolean(t),preventCancel:Boolean(n),preventClose:Boolean(o),signal:a}}Object.defineProperties(St.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),f(St.prototype.close,"close"),f(St.prototype.enqueue,"enqueue"),f(St.prototype.error,"error"),"symbol"==typeof r.toStringTag&&Object.defineProperty(St.prototype,r.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});var Mt=function(){function ReadableStream(e,r){void 0===e&&(e={}),void 0===r&&(r={}),void 0===e?e=null:H(e,"First parameter");var t=gr(r,"Second parameter"),n=function(e,r){Q(e,r);var t=e,n=null==t?void 0:t.autoAllocateChunkSize,o=null==t?void 0:t.cancel,a=null==t?void 0:t.pull,i=null==t?void 0:t.start,u=null==t?void 0:t.type;return{autoAllocateChunkSize:void 0===n?void 0:J(n,"".concat(r," has member 'autoAllocateChunkSize' that")),cancel:void 0===o?void 0:zt(o,t,"".concat(r," has member 'cancel' that")),pull:void 0===a?void 0:It(a,t,"".concat(r," has member 'pull' that")),start:void 0===i?void 0:Dt(i,t,"".concat(r," has member 'start' that")),type:void 0===u?void 0:Lt(u,"".concat(r," has member 'type' that"))}}(e,"First parameter");if(Qt(this),"bytes"===n.type){if(void 0!==t.size)throw new RangeError("The strategy for a byte stream cannot have a size function");!function(e,r,t){var n,o,a,i=Object.create(Be.prototype);n=void 0!==r.start?function(){return r.start(i)}:function(){},o=void 0!==r.pull?function(){return r.pull(i)}:function(){return m(void 0)},a=void 0!==r.cancel?function(e){return r.cancel(e)}:function(){return m(void 0)};var u=r.autoAllocateChunkSize;if(0===u)throw new TypeError("autoAllocateChunkSize must be greater than 0");ar(e,i,n,o,a,t,u)}(this,n,yr(t,0))}else{var o=vr(t);!function(e,r,t,n){var o,a,i,u=Object.create(St.prototype);o=void 0!==r.start?function(){return r.start(u)}:function(){},a=void 0!==r.pull?function(){return r.pull(u)}:function(){return m(void 0)},i=void 0!==r.cancel?function(e){return r.cancel(e)}:function(){return m(void 0)},jt(e,u,o,a,i,t,n)}(this,n,yr(t,1),o)}}return Object.defineProperty(ReadableStream.prototype,"locked",{get:function(){if(!Nt(this))throw Xt("locked");return Ht(this)},enumerable:!1,configurable:!0}),ReadableStream.prototype.cancel=function(e){return void 0===e&&(e=void 0),Nt(this)?Ht(this)?_(new TypeError("Cannot cancel a stream that already has a reader")):Vt(this,e):_(Xt("cancel"))},ReadableStream.prototype.getReader=function(e){if(void 0===e&&(e=void 0),!Nt(this))throw Xt("getReader");return void 0===function(e,r){Q(e,r);var t=null==e?void 0:e.mode;return{mode:void 0===t?void 0:lr(t,"".concat(r," has member 'mode' that"))}}(e,"First parameter").mode?Z(this):sr(this)},ReadableStream.prototype.pipeThrough=function(e,r){if(void 0===r&&(r={}),!Nt(this))throw Xt("pipeThrough");V(e,1,"pipeThrough");var t=function(e,r){Q(e,r);var t=null==e?void 0:e.readable;U(t,"readable","ReadableWritablePair"),K(t,"".concat(r," has member 'readable' that"));var n=null==e?void 0:e.writable;return U(n,"writable","ReadableWritablePair"),Cr(n,"".concat(r," has member 'writable' that")),{readable:t,writable:n}}(e,"First parameter"),n=Ft(r,"Second parameter");if(Ht(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(Br(t.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return R(gt(this,t.writable,n.preventClose,n.preventAbort,n.preventCancel,n.signal)),t.readable},ReadableStream.prototype.pipeTo=function(e,r){if(void 0===r&&(r={}),!Nt(this))return _(Xt("pipeTo"));if(void 0===e)return _("Parameter 1 is required in 'pipeTo'.");if(!jr(e))return _(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));var t;try{t=Ft(r,"Second parameter")}catch(e){return _(e)}return Ht(this)?_(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):Br(e)?_(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):gt(this,e,t.preventClose,t.preventAbort,t.preventCancel,t.signal)},ReadableStream.prototype.tee=function(){if(!Nt(this))throw Xt("tee");return fe(kt(this))},ReadableStream.prototype.values=function(e){if(void 0===e&&(e=void 0),!Nt(this))throw Xt("values");var r,t,n,o,a,i=function(e,r){Q(e,r);var t=null==e?void 0:e.preventCancel;return{preventCancel:Boolean(t)}}(e,"First parameter");return r=this,t=i.preventCancel,n=Z(r),o=new Se(n,t),(a=Object.create(we))._asyncIteratorImpl=o,a},ReadableStream.prototype[ye]=function(e){return this.values(e)},ReadableStream.from=function(e){return At(e)},ReadableStream}();function xt(e,r,t,n,o){void 0===n&&(n=1),void 0===o&&(o=function(){return 1});var a=Object.create(Mt.prototype);return Qt(a),jt(a,Object.create(St.prototype),e,r,t,n,o),a}function Yt(e,r,t){var n=Object.create(Mt.prototype);return Qt(n),ar(n,Object.create(Be.prototype),e,r,t,0,void 0),n}function Qt(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function Nt(e){return!!s(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")&&e instanceof Mt)}function Ht(e){return void 0!==e._reader}function Vt(e,r){if(e._disturbed=!0,"closed"===e._state)return m(void 0);if("errored"===e._state)return _(e._storedError);Ut(e);var t=e._reader;if(void 0!==t&&pr(t)){var n=t._readIntoRequests;t._readIntoRequests=new q,n.forEach((function(e){e._closeSteps(void 0)}))}return w(e._readableStreamController[W](r),l)}function Ut(e){e._state="closed";var r=e._reader;if(void 0!==r&&(M(r),ue(r))){var t=r._readRequests;r._readRequests=new q,t.forEach((function(e){e._closeSteps()}))}}function Gt(e,r){e._state="errored",e._storedError=r;var t=e._reader;void 0!==t&&(F(t,r),ue(t)?se(t,r):mr(t,r))}function Xt(e){return new TypeError("ReadableStream.prototype.".concat(e," can only be used on a ReadableStream"))}function Jt(e,r){Q(e,r);var t=null==e?void 0:e.highWaterMark;return U(t,"highWaterMark","QueuingStrategyInit"),{highWaterMark:G(t)}}Object.defineProperties(Mt,{from:{enumerable:!0}}),Object.defineProperties(Mt.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),f(Mt.from,"from"),f(Mt.prototype.cancel,"cancel"),f(Mt.prototype.getReader,"getReader"),f(Mt.prototype.pipeThrough,"pipeThrough"),f(Mt.prototype.pipeTo,"pipeTo"),f(Mt.prototype.tee,"tee"),f(Mt.prototype.values,"values"),"symbol"==typeof r.toStringTag&&Object.defineProperty(Mt.prototype,r.toStringTag,{value:"ReadableStream",configurable:!0}),Object.defineProperty(Mt.prototype,ye,{value:Mt.prototype.values,writable:!0,configurable:!0});var Kt=function(e){return e.byteLength};f(Kt,"size");var Zt=function(){function ByteLengthQueuingStrategy(e){V(e,1,"ByteLengthQueuingStrategy"),e=Jt(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}return Object.defineProperty(ByteLengthQueuingStrategy.prototype,"highWaterMark",{get:function(){if(!en(this))throw $t("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark},enumerable:!1,configurable:!0}),Object.defineProperty(ByteLengthQueuingStrategy.prototype,"size",{get:function(){if(!en(this))throw $t("size");return Kt},enumerable:!1,configurable:!0}),ByteLengthQueuingStrategy}();function $t(e){return new TypeError("ByteLengthQueuingStrategy.prototype.".concat(e," can only be used on a ByteLengthQueuingStrategy"))}function en(e){return!!s(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")&&e instanceof Zt)}Object.defineProperties(Zt.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(Zt.prototype,r.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});var rn=function(){return 1};f(rn,"size");var tn=function(){function CountQueuingStrategy(e){V(e,1,"CountQueuingStrategy"),e=Jt(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}return Object.defineProperty(CountQueuingStrategy.prototype,"highWaterMark",{get:function(){if(!on(this))throw nn("highWaterMark");return this._countQueuingStrategyHighWaterMark},enumerable:!1,configurable:!0}),Object.defineProperty(CountQueuingStrategy.prototype,"size",{get:function(){if(!on(this))throw nn("size");return rn},enumerable:!1,configurable:!0}),CountQueuingStrategy}();function nn(e){return new TypeError("CountQueuingStrategy.prototype.".concat(e," can only be used on a CountQueuingStrategy"))}function on(e){return!!s(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")&&e instanceof tn)}function an(e,r,t){return N(e,t),function(t){return C(e,r,[t])}}function un(e,r,t){return N(e,t),function(t){return P(e,r,[t])}}function ln(e,r,t){return N(e,t),function(t,n){return C(e,r,[t,n])}}function sn(e,r,t){return N(e,t),function(t){return C(e,r,[t])}}Object.defineProperties(tn.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(tn.prototype,r.toStringTag,{value:"CountQueuingStrategy",configurable:!0});var cn=function(){function TransformStream(e,r,t){void 0===e&&(e={}),void 0===r&&(r={}),void 0===t&&(t={}),void 0===e&&(e=null);var n=gr(r,"Second parameter"),o=gr(t,"Third parameter"),a=function(e,r){Q(e,r);var t=null==e?void 0:e.cancel,n=null==e?void 0:e.flush,o=null==e?void 0:e.readableType,a=null==e?void 0:e.start,i=null==e?void 0:e.transform,u=null==e?void 0:e.writableType;return{cancel:void 0===t?void 0:sn(t,e,"".concat(r," has member 'cancel' that")),flush:void 0===n?void 0:an(n,e,"".concat(r," has member 'flush' that")),readableType:o,start:void 0===a?void 0:un(a,e,"".concat(r," has member 'start' that")),transform:void 0===i?void 0:ln(i,e,"".concat(r," has member 'transform' that")),writableType:u}}(e,"First parameter");if(void 0!==a.readableType)throw new RangeError("Invalid readableType specified");if(void 0!==a.writableType)throw new RangeError("Invalid writableType specified");var i,u=yr(o,0),l=vr(o),s=yr(n,1),c=vr(n);!function(e,r,t,n,o,a){function i(){return r}function u(r){return function(e,r){var t=e._transformStreamController;if(e._backpressure){return w(e._backpressureChangePromise,(function(){var n=e._writable;if("erroring"===n._state)throw n._storedError;return gn(t,r)}))}return gn(t,r)}(e,r)}function l(r){return function(e,r){var t=e._transformStreamController;if(void 0!==t._finishPromise)return t._finishPromise;var n=e._readable;t._finishPromise=h((function(e,r){t._finishPromise_resolve=e,t._finishPromise_reject=r}));var o=t._cancelAlgorithm(r);return yn(t),v(o,(function(){return"errored"===n._state?Rn(t,n._storedError):(Et(n._readableStreamController,r),wn(t)),null}),(function(e){return Et(n._readableStreamController,e),Rn(t,e),null})),t._finishPromise}(e,r)}function s(){return function(e){var r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;var t=e._readable;r._finishPromise=h((function(e,t){r._finishPromise_resolve=e,r._finishPromise_reject=t}));var n=r._flushAlgorithm();return yn(r),v(n,(function(){return"errored"===t._state?Rn(r,t._storedError):(Ct(t._readableStreamController),wn(r)),null}),(function(e){return Et(t._readableStreamController,e),Rn(r,e),null})),r._finishPromise}(e)}function c(){return function(e){return hn(e,!1),e._backpressureChangePromise}(e)}function f(r){return function(e,r){var t=e._transformStreamController;if(void 0!==t._finishPromise)return t._finishPromise;var n=e._writable;t._finishPromise=h((function(e,r){t._finishPromise_resolve=e,t._finishPromise_reject=r}));var o=t._cancelAlgorithm(r);return yn(t),v(o,(function(){return"errored"===n._state?Rn(t,n._storedError):(rt(n._writableStreamController,r),pn(e),wn(t)),null}),(function(r){return rt(n._writableStreamController,r),pn(e),Rn(t,r),null})),t._finishPromise}(e,r)}e._writable=function(e,r,t,n,o,a){void 0===o&&(o=1),void 0===a&&(a=function(){return 1});var i=Object.create(Er.prototype);return Wr(i),Kr(i,Object.create(Xr.prototype),e,r,t,n,o,a),i}(i,u,s,l,t,n),e._readable=xt(i,c,f,o,a),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,hn(e,!0),e._transformStreamController=void 0}(this,h((function(e){i=e})),s,c,u,l),function(e,r){var t,n,o,a=Object.create(mn.prototype);t=void 0!==r.transform?function(e){return r.transform(e,a)}:function(e){try{return vn(a,e),m(void 0)}catch(e){return _(e)}};n=void 0!==r.flush?function(){return r.flush(a)}:function(){return m(void 0)};o=void 0!==r.cancel?function(e){return r.cancel(e)}:function(){return m(void 0)};!function(e,r,t,n,o){r._controlledTransformStream=e,e._transformStreamController=r,r._transformAlgorithm=t,r._flushAlgorithm=n,r._cancelAlgorithm=o,r._finishPromise=void 0,r._finishPromise_resolve=void 0,r._finishPromise_reject=void 0}(e,a,t,n,o)}(this,a),void 0!==a.start?i(a.start(this._transformStreamController)):i(void 0)}return Object.defineProperty(TransformStream.prototype,"readable",{get:function(){if(!fn(this))throw Tn("readable");return this._readable},enumerable:!1,configurable:!0}),Object.defineProperty(TransformStream.prototype,"writable",{get:function(){if(!fn(this))throw Tn("writable");return this._writable},enumerable:!1,configurable:!0}),TransformStream}();function fn(e){return!!s(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")&&e instanceof cn)}function dn(e,r){Et(e._readable._readableStreamController,r),bn(e,r)}function bn(e,r){yn(e._transformStreamController),rt(e._writable._writableStreamController,r),pn(e)}function pn(e){e._backpressure&&hn(e,!1)}function hn(e,r){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=h((function(r){e._backpressureChangePromise_resolve=r})),e._backpressure=r}Object.defineProperties(cn.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(cn.prototype,r.toStringTag,{value:"TransformStream",configurable:!0});var mn=function(){function TransformStreamDefaultController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(TransformStreamDefaultController.prototype,"desiredSize",{get:function(){if(!_n(this))throw Sn("desiredSize");return Ot(this._controlledTransformStream._readable._readableStreamController)},enumerable:!1,configurable:!0}),TransformStreamDefaultController.prototype.enqueue=function(e){if(void 0===e&&(e=void 0),!_n(this))throw Sn("enqueue");vn(this,e)},TransformStreamDefaultController.prototype.error=function(e){if(void 0===e&&(e=void 0),!_n(this))throw Sn("error");var r;r=e,dn(this._controlledTransformStream,r)},TransformStreamDefaultController.prototype.terminate=function(){if(!_n(this))throw Sn("terminate");!function(e){var r=e._controlledTransformStream;Ct(r._readable._readableStreamController);var t=new TypeError("TransformStream terminated");bn(r,t)}(this)},TransformStreamDefaultController}();function _n(e){return!!s(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")&&e instanceof mn)}function yn(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0,e._cancelAlgorithm=void 0}function vn(e,r){var t=e._controlledTransformStream,n=t._readable._readableStreamController;if(!Wt(n))throw new TypeError("Readable side is not in a state that permits enqueue");try{qt(n,r)}catch(e){throw bn(t,e),t._readable._storedError}var o=function(e){return!Tt(e)}(n);o!==t._backpressure&&hn(t,!0)}function gn(e,r){return w(e._transformAlgorithm(r),void 0,(function(r){throw dn(e._controlledTransformStream,r),r}))}function Sn(e){return new TypeError("TransformStreamDefaultController.prototype.".concat(e," can only be used on a TransformStreamDefaultController"))}function wn(e){void 0!==e._finishPromise_resolve&&(e._finishPromise_resolve(),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function Rn(e,r){void 0!==e._finishPromise_reject&&(R(e._finishPromise),e._finishPromise_reject(r),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function Tn(e){return new TypeError("TransformStream.prototype.".concat(e," can only be used on a TransformStream"))}Object.defineProperties(mn.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),f(mn.prototype.enqueue,"enqueue"),f(mn.prototype.error,"error"),f(mn.prototype.terminate,"terminate"),"symbol"==typeof r.toStringTag&&Object.defineProperty(mn.prototype,r.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});var Pn={ReadableStream:Mt,ReadableStreamDefaultController:St,ReadableByteStreamController:Be,ReadableStreamBYOBRequest:je,ReadableStreamDefaultReader:ie,ReadableStreamBYOBReader:br,WritableStream:Er,WritableStreamDefaultController:Xr,WritableStreamDefaultWriter:xr,ByteLengthQueuingStrategy:Zt,CountQueuingStrategy:tn,TransformStream:cn,TransformStreamDefaultController:mn};if(void 0!==_t)for(var Cn in Pn)Object.prototype.hasOwnProperty.call(Pn,Cn)&&Object.defineProperty(_t,Cn,{value:Pn[Cn],writable:!0,configurable:!0});e.ByteLengthQueuingStrategy=Zt,e.CountQueuingStrategy=tn,e.ReadableByteStreamController=Be,e.ReadableStream=Mt,e.ReadableStreamBYOBReader=br,e.ReadableStreamBYOBRequest=je,e.ReadableStreamDefaultController=St,e.ReadableStreamDefaultReader=ie,e.TransformStream=cn,e.TransformStreamDefaultController=mn,e.WritableStream=Er,e.WritableStreamDefaultController=Xr,e.WritableStreamDefaultWriter=xr}));
//# sourceMappingURL=polyfill.min.js.map
