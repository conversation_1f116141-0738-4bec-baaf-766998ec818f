/**
 * @license
 * web-streams-polyfill v3.3.3
 * Copyright 2024 <PERSON>, <PERSON><PERSON><PERSON> and other contributors.
 * This code is released under the MIT license.
 * SPDX-License-Identifier: MIT
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).WebStreamsPolyfill={})}(this,(function(e){"use strict";function t(){}function r(e){return"object"==typeof e&&null!==e||"function"==typeof e}const o=t;function n(e,t){try{Object.defineProperty(e,"name",{value:t,configurable:!0})}catch(e){}}const a=Promise,i=Promise.prototype.then,l=Promise.reject.bind(a);function s(e){return new a(e)}function u(e){return s((t=>t(e)))}function c(e){return l(e)}function d(e,t,r){return i.call(e,t,r)}function f(e,t,r){d(d(e,t,r),void 0,o)}function b(e,t){f(e,t)}function m(e,t){f(e,void 0,t)}function h(e,t,r){return d(e,t,r)}function _(e){d(e,void 0,o)}let p=e=>{if("function"==typeof queueMicrotask)p=queueMicrotask;else{const e=u(void 0);p=t=>d(e,t)}return p(e)};function y(e,t,r){if("function"!=typeof e)throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}function S(e,t,r){try{return u(y(e,t,r))}catch(e){return c(e)}}class g{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(e){const t=this._back;let r=t;16383===t._elements.length&&(r={_elements:[],_next:void 0}),t._elements.push(e),r!==t&&(this._back=r,t._next=r),++this._size}shift(){const e=this._front;let t=e;const r=this._cursor;let o=r+1;const n=e._elements,a=n[r];return 16384===o&&(t=e._next,o=0),--this._size,this._cursor=o,e!==t&&(this._front=t),n[r]=void 0,a}forEach(e){let t=this._cursor,r=this._front,o=r._elements;for(;!(t===o.length&&void 0===r._next||t===o.length&&(r=r._next,o=r._elements,t=0,0===o.length));)e(o[t]),++t}peek(){const e=this._front,t=this._cursor;return e._elements[t]}}const v=Symbol("[[AbortSteps]]"),w=Symbol("[[ErrorSteps]]"),R=Symbol("[[CancelSteps]]"),T=Symbol("[[PullSteps]]"),C=Symbol("[[ReleaseSteps]]");function P(e,t){e._ownerReadableStream=t,t._reader=e,"readable"===t._state?B(e):"closed"===t._state?function(e){B(e),k(e)}(e):O(e,t._storedError)}function q(e,t){return Er(e._ownerReadableStream,t)}function E(e){const t=e._ownerReadableStream;"readable"===t._state?j(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(e,t){O(e,t)}(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),t._readableStreamController[C](),t._reader=void 0,e._ownerReadableStream=void 0}function W(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function B(e){e._closedPromise=s(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r}))}function O(e,t){B(e),j(e,t)}function j(e,t){void 0!==e._closedPromise_reject&&(_(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function k(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}const A=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},D=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function z(e,t){if(void 0!==e&&("object"!=typeof(r=e)&&"function"!=typeof r))throw new TypeError(`${t} is not an object.`);var r}function L(e,t){if("function"!=typeof e)throw new TypeError(`${t} is not a function.`)}function F(e,t){if(!function(e){return"object"==typeof e&&null!==e||"function"==typeof e}(e))throw new TypeError(`${t} is not an object.`)}function I(e,t,r){if(void 0===e)throw new TypeError(`Parameter ${t} is required in '${r}'.`)}function $(e,t,r){if(void 0===e)throw new TypeError(`${t} is required in '${r}'.`)}function M(e){return Number(e)}function Y(e){return 0===e?0:e}function Q(e,t){const r=Number.MAX_SAFE_INTEGER;let o=Number(e);if(o=Y(o),!A(o))throw new TypeError(`${t} is not a finite number`);if(o=function(e){return Y(D(e))}(o),o<0||o>r)throw new TypeError(`${t} is outside the accepted range of 0 to ${r}, inclusive`);return A(o)&&0!==o?o:0}function x(e,t){if(!Pr(e))throw new TypeError(`${t} is not a ReadableStream.`)}function N(e){return new ReadableStreamDefaultReader(e)}function H(e,t){e._reader._readRequests.push(t)}function V(e,t,r){const o=e._reader._readRequests.shift();r?o._closeSteps():o._chunkSteps(t)}function U(e){return e._reader._readRequests.length}function G(e){const t=e._reader;return void 0!==t&&!!X(t)}class ReadableStreamDefaultReader{constructor(e){if(I(e,1,"ReadableStreamDefaultReader"),x(e,"First parameter"),qr(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");P(this,e),this._readRequests=new g}get closed(){return X(this)?this._closedPromise:c(Z("closed"))}cancel(e=void 0){return X(this)?void 0===this._ownerReadableStream?c(W("cancel")):q(this,e):c(Z("cancel"))}read(){if(!X(this))return c(Z("read"));if(void 0===this._ownerReadableStream)return c(W("read from"));let e,t;const r=s(((r,o)=>{e=r,t=o}));return J(this,{_chunkSteps:t=>e({value:t,done:!1}),_closeSteps:()=>e({value:void 0,done:!0}),_errorSteps:e=>t(e)}),r}releaseLock(){if(!X(this))throw Z("releaseLock");void 0!==this._ownerReadableStream&&function(e){E(e);const t=new TypeError("Reader was released");K(e,t)}(this)}}function X(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readRequests")&&e instanceof ReadableStreamDefaultReader)}function J(e,t){const r=e._ownerReadableStream;r._disturbed=!0,"closed"===r._state?t._closeSteps():"errored"===r._state?t._errorSteps(r._storedError):r._readableStreamController[T](t)}function K(e,t){const r=e._readRequests;e._readRequests=new g,r.forEach((e=>{e._errorSteps(t)}))}function Z(e){return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}Object.defineProperties(ReadableStreamDefaultReader.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),n(ReadableStreamDefaultReader.prototype.cancel,"cancel"),n(ReadableStreamDefaultReader.prototype.read,"read"),n(ReadableStreamDefaultReader.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableStreamDefaultReader.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});const ee=Object.getPrototypeOf(Object.getPrototypeOf((async function*(){})).prototype);class te{constructor(e,t){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=t}next(){const e=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?h(this._ongoingPromise,e,e):e(),this._ongoingPromise}return(e){const t=()=>this._returnSteps(e);return this._ongoingPromise?h(this._ongoingPromise,t,t):t()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});const e=this._reader;let t,r;const o=s(((e,o)=>{t=e,r=o}));return J(e,{_chunkSteps:e=>{this._ongoingPromise=void 0,p((()=>t({value:e,done:!1})))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,E(e),t({value:void 0,done:!0})},_errorSteps:t=>{this._ongoingPromise=void 0,this._isFinished=!0,E(e),r(t)}}),o}_returnSteps(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;const t=this._reader;if(!this._preventCancel){const r=q(t,e);return E(t),h(r,(()=>({value:e,done:!0})))}return E(t),u({value:e,done:!0})}}const re={next(){return oe(this)?this._asyncIteratorImpl.next():c(ne("next"))},return(e){return oe(this)?this._asyncIteratorImpl.return(e):c(ne("return"))}};function oe(e){if(!r(e))return!1;if(!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof te}catch(e){return!1}}function ne(e){return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}Object.setPrototypeOf(re,ee);const ae=Number.isNaN||function(e){return e!=e};var ie,le,se;function ue(e){return e.slice()}function ce(e,t,r,o,n){new Uint8Array(e).set(new Uint8Array(r,o,n),t)}let de=e=>(de="function"==typeof e.transfer?e=>e.transfer():"function"==typeof structuredClone?e=>structuredClone(e,{transfer:[e]}):e=>e,de(e)),fe=e=>(fe="boolean"==typeof e.detached?e=>e.detached:e=>0===e.byteLength,fe(e));function be(e,t,r){if(e.slice)return e.slice(t,r);const o=r-t,n=new ArrayBuffer(o);return ce(n,0,e,t,o),n}function me(e,t){const r=e[t];if(null!=r){if("function"!=typeof r)throw new TypeError(`${String(t)} is not a function`);return r}}const he=null!==(se=null!==(ie=Symbol.asyncIterator)&&void 0!==ie?ie:null===(le=Symbol.for)||void 0===le?void 0:le.call(Symbol,"Symbol.asyncIterator"))&&void 0!==se?se:"@@asyncIterator";function _e(e,t="sync",o){if(void 0===o)if("async"===t){if(void 0===(o=me(e,he))){return function(e){const t={[Symbol.iterator]:()=>e.iterator},r=async function*(){return yield*t}();return{iterator:r,nextMethod:r.next,done:!1}}(_e(e,"sync",me(e,Symbol.iterator)))}}else o=me(e,Symbol.iterator);if(void 0===o)throw new TypeError("The object is not iterable");const n=y(o,e,[]);if(!r(n))throw new TypeError("The iterator method must return an object");return{iterator:n,nextMethod:n.next,done:!1}}function pe(e){const t=be(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}function ye(e){const t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function Se(e,t,r){if("number"!=typeof(o=r)||ae(o)||o<0||r===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");var o;e._queue.push({value:t,size:r}),e._queueTotalSize+=r}function ge(e){e._queue=new g,e._queueTotalSize=0}function ve(e){return e===DataView}class ReadableStreamBYOBRequest{constructor(){throw new TypeError("Illegal constructor")}get view(){if(!Re(this))throw Ge("view");return this._view}respond(e){if(!Re(this))throw Ge("respond");if(I(e,1,"respond"),e=Q(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(fe(this._view.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be used as a response");He(this._associatedReadableByteStreamController,e)}respondWithNewView(e){if(!Re(this))throw Ge("respondWithNewView");if(I(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw new TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(fe(e.buffer))throw new TypeError("The given view's buffer has been detached and so cannot be used as a response");Ve(this._associatedReadableByteStreamController,e)}}Object.defineProperties(ReadableStreamBYOBRequest.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),n(ReadableStreamBYOBRequest.prototype.respond,"respond"),n(ReadableStreamBYOBRequest.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableStreamBYOBRequest.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class ReadableByteStreamController{constructor(){throw new TypeError("Illegal constructor")}get byobRequest(){if(!we(this))throw Xe("byobRequest");return xe(this)}get desiredSize(){if(!we(this))throw Xe("desiredSize");return Ne(this)}close(){if(!we(this))throw Xe("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");const e=this._controlledReadableByteStream._state;if("readable"!==e)throw new TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`);$e(this)}enqueue(e){if(!we(this))throw Xe("enqueue");if(I(e,1,"enqueue"),!ArrayBuffer.isView(e))throw new TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw new TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");const t=this._controlledReadableByteStream._state;if("readable"!==t)throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be enqueued to`);Me(this,e)}error(e=void 0){if(!we(this))throw Xe("error");Ye(this,e)}[R](e){Ce(this),ge(this);const t=this._cancelAlgorithm(e);return Ie(this),t}[T](e){const t=this._controlledReadableByteStream;if(this._queueTotalSize>0)return void Qe(this,e);const r=this._autoAllocateChunkSize;if(void 0!==r){let t;try{t=new ArrayBuffer(r)}catch(t){return void e._errorSteps(t)}const o={buffer:t,bufferByteLength:r,byteOffset:0,byteLength:r,bytesFilled:0,minimumFill:1,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(o)}H(t,e),Te(this)}[C](){if(this._pendingPullIntos.length>0){const e=this._pendingPullIntos.peek();e.readerType="none",this._pendingPullIntos=new g,this._pendingPullIntos.push(e)}}}function we(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")&&e instanceof ReadableByteStreamController)}function Re(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")&&e instanceof ReadableStreamBYOBRequest)}function Te(e){const t=function(e){const t=e._controlledReadableByteStream;if("readable"!==t._state)return!1;if(e._closeRequested)return!1;if(!e._started)return!1;if(G(t)&&U(t)>0)return!0;if(tt(t)&&et(t)>0)return!0;const r=Ne(e);if(r>0)return!0;return!1}(e);if(!t)return;if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0;f(e._pullAlgorithm(),(()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,Te(e)),null)),(t=>(Ye(e,t),null)))}function Ce(e){Ae(e),e._pendingPullIntos=new g}function Pe(e,t){let r=!1;"closed"===e._state&&(r=!0);const o=qe(t);"default"===t.readerType?V(e,o,r):function(e,t,r){const o=e._reader,n=o._readIntoRequests.shift();r?n._closeSteps(t):n._chunkSteps(t)}(e,o,r)}function qe(e){const t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}function Ee(e,t,r,o){e._queue.push({buffer:t,byteOffset:r,byteLength:o}),e._queueTotalSize+=o}function We(e,t,r,o){let n;try{n=be(t,r,r+o)}catch(t){throw Ye(e,t),t}Ee(e,n,0,o)}function Be(e,t){t.bytesFilled>0&&We(e,t.buffer,t.byteOffset,t.bytesFilled),Fe(e)}function Oe(e,t){const r=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),o=t.bytesFilled+r;let n=r,a=!1;const i=o-o%t.elementSize;i>=t.minimumFill&&(n=i-t.bytesFilled,a=!0);const l=e._queue;for(;n>0;){const r=l.peek(),o=Math.min(n,r.byteLength),a=t.byteOffset+t.bytesFilled;ce(t.buffer,a,r.buffer,r.byteOffset,o),r.byteLength===o?l.shift():(r.byteOffset+=o,r.byteLength-=o),e._queueTotalSize-=o,je(e,o,t),n-=o}return a}function je(e,t,r){r.bytesFilled+=t}function ke(e){0===e._queueTotalSize&&e._closeRequested?(Ie(e),Wr(e._controlledReadableByteStream)):Te(e)}function Ae(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function De(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;const t=e._pendingPullIntos.peek();Oe(e,t)&&(Fe(e),Pe(e._controlledReadableByteStream,t))}}function ze(e,t,r,o){const n=e._controlledReadableByteStream,a=t.constructor,i=function(e){return ve(e)?1:e.BYTES_PER_ELEMENT}(a),{byteOffset:l,byteLength:s}=t,u=r*i;let c;try{c=de(t.buffer)}catch(e){return void o._errorSteps(e)}const d={buffer:c,bufferByteLength:c.byteLength,byteOffset:l,byteLength:s,bytesFilled:0,minimumFill:u,elementSize:i,viewConstructor:a,readerType:"byob"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(d),void Ze(n,o);if("closed"!==n._state){if(e._queueTotalSize>0){if(Oe(e,d)){const t=qe(d);return ke(e),void o._chunkSteps(t)}if(e._closeRequested){const t=new TypeError("Insufficient bytes to fill elements in the given buffer");return Ye(e,t),void o._errorSteps(t)}}e._pendingPullIntos.push(d),Ze(n,o),Te(e)}else{const e=new a(d.buffer,d.byteOffset,0);o._closeSteps(e)}}function Le(e,t){const r=e._pendingPullIntos.peek();Ae(e);"closed"===e._controlledReadableByteStream._state?function(e,t){"none"===t.readerType&&Fe(e);const r=e._controlledReadableByteStream;if(tt(r))for(;et(r)>0;)Pe(r,Fe(e))}(e,r):function(e,t,r){if(je(0,t,r),"none"===r.readerType)return Be(e,r),void De(e);if(r.bytesFilled<r.minimumFill)return;Fe(e);const o=r.bytesFilled%r.elementSize;if(o>0){const t=r.byteOffset+r.bytesFilled;We(e,r.buffer,t-o,o)}r.bytesFilled-=o,Pe(e._controlledReadableByteStream,r),De(e)}(e,t,r),Te(e)}function Fe(e){return e._pendingPullIntos.shift()}function Ie(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function $e(e){const t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state)if(e._queueTotalSize>0)e._closeRequested=!0;else{if(e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek();if(t.bytesFilled%t.elementSize!=0){const t=new TypeError("Insufficient bytes to fill elements in the given buffer");throw Ye(e,t),t}}Ie(e),Wr(t)}}function Me(e,t){const r=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==r._state)return;const{buffer:o,byteOffset:n,byteLength:a}=t;if(fe(o))throw new TypeError("chunk's buffer is detached and so cannot be enqueued");const i=de(o);if(e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek();if(fe(t.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk");Ae(e),t.buffer=de(t.buffer),"none"===t.readerType&&Be(e,t)}if(G(r))if(function(e){const t=e._controlledReadableByteStream._reader;for(;t._readRequests.length>0;){if(0===e._queueTotalSize)return;Qe(e,t._readRequests.shift())}}(e),0===U(r))Ee(e,i,n,a);else{e._pendingPullIntos.length>0&&Fe(e);V(r,new Uint8Array(i,n,a),!1)}else tt(r)?(Ee(e,i,n,a),De(e)):Ee(e,i,n,a);Te(e)}function Ye(e,t){const r=e._controlledReadableByteStream;"readable"===r._state&&(Ce(e),ge(e),Ie(e),Br(r,t))}function Qe(e,t){const r=e._queue.shift();e._queueTotalSize-=r.byteLength,ke(e);const o=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);t._chunkSteps(o)}function xe(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),o=Object.create(ReadableStreamBYOBRequest.prototype);!function(e,t,r){e._associatedReadableByteStreamController=t,e._view=r}(o,e,r),e._byobRequest=o}return e._byobRequest}function Ne(e){const t=e._controlledReadableByteStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function He(e,t){const r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===t)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(r.bytesFilled+t>r.byteLength)throw new RangeError("bytesWritten out of range")}r.buffer=de(r.buffer),Le(e,t)}function Ve(e,t){const r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t.byteLength)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===t.byteLength)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(r.bufferByteLength!==t.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(r.bytesFilled+t.byteLength>r.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");const o=t.byteLength;r.buffer=de(t.buffer),Le(e,o)}function Ue(e,t,r,o,n,a,i){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,ge(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=a,t._pullAlgorithm=o,t._cancelAlgorithm=n,t._autoAllocateChunkSize=i,t._pendingPullIntos=new g,e._readableStreamController=t;f(u(r()),(()=>(t._started=!0,Te(t),null)),(e=>(Ye(t,e),null)))}function Ge(e){return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function Xe(e){return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function Je(e,t){if("byob"!==(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}function Ke(e){return new ReadableStreamBYOBReader(e)}function Ze(e,t){e._reader._readIntoRequests.push(t)}function et(e){return e._reader._readIntoRequests.length}function tt(e){const t=e._reader;return void 0!==t&&!!rt(t)}Object.defineProperties(ReadableByteStreamController.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),n(ReadableByteStreamController.prototype.close,"close"),n(ReadableByteStreamController.prototype.enqueue,"enqueue"),n(ReadableByteStreamController.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableByteStreamController.prototype,Symbol.toStringTag,{value:"ReadableByteStreamController",configurable:!0});class ReadableStreamBYOBReader{constructor(e){if(I(e,1,"ReadableStreamBYOBReader"),x(e,"First parameter"),qr(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!we(e._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");P(this,e),this._readIntoRequests=new g}get closed(){return rt(this)?this._closedPromise:c(at("closed"))}cancel(e=void 0){return rt(this)?void 0===this._ownerReadableStream?c(W("cancel")):q(this,e):c(at("cancel"))}read(e,t={}){if(!rt(this))return c(at("read"));if(!ArrayBuffer.isView(e))return c(new TypeError("view must be an array buffer view"));if(0===e.byteLength)return c(new TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return c(new TypeError("view's buffer must have non-zero byteLength"));if(fe(e.buffer))return c(new TypeError("view's buffer has been detached"));let r;try{r=function(e,t){var r;return z(e,t),{min:Q(null!==(r=null==e?void 0:e.min)&&void 0!==r?r:1,`${t} has member 'min' that`)}}(t,"options")}catch(e){return c(e)}const o=r.min;if(0===o)return c(new TypeError("options.min must be greater than 0"));if(function(e){return ve(e.constructor)}(e)){if(o>e.byteLength)return c(new RangeError("options.min must be less than or equal to view's byteLength"))}else if(o>e.length)return c(new RangeError("options.min must be less than or equal to view's length"));if(void 0===this._ownerReadableStream)return c(W("read from"));let n,a;const i=s(((e,t)=>{n=e,a=t}));return ot(this,e,o,{_chunkSteps:e=>n({value:e,done:!1}),_closeSteps:e=>n({value:e,done:!0}),_errorSteps:e=>a(e)}),i}releaseLock(){if(!rt(this))throw at("releaseLock");void 0!==this._ownerReadableStream&&function(e){E(e);const t=new TypeError("Reader was released");nt(e,t)}(this)}}function rt(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")&&e instanceof ReadableStreamBYOBReader)}function ot(e,t,r,o){const n=e._ownerReadableStream;n._disturbed=!0,"errored"===n._state?o._errorSteps(n._storedError):ze(n._readableStreamController,t,r,o)}function nt(e,t){const r=e._readIntoRequests;e._readIntoRequests=new g,r.forEach((e=>{e._errorSteps(t)}))}function at(e){return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function it(e,t){const{highWaterMark:r}=e;if(void 0===r)return t;if(ae(r)||r<0)throw new RangeError("Invalid highWaterMark");return r}function lt(e){const{size:t}=e;return t||(()=>1)}function st(e,t){z(e,t);const r=null==e?void 0:e.highWaterMark,o=null==e?void 0:e.size;return{highWaterMark:void 0===r?void 0:M(r),size:void 0===o?void 0:ut(o,`${t} has member 'size' that`)}}function ut(e,t){return L(e,t),t=>M(e(t))}function ct(e,t,r){return L(e,r),r=>S(e,t,[r])}function dt(e,t,r){return L(e,r),()=>S(e,t,[])}function ft(e,t,r){return L(e,r),r=>y(e,t,[r])}function bt(e,t,r){return L(e,r),(r,o)=>S(e,t,[r,o])}function mt(e,t){if(!yt(e))throw new TypeError(`${t} is not a WritableStream.`)}Object.defineProperties(ReadableStreamBYOBReader.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),n(ReadableStreamBYOBReader.prototype.cancel,"cancel"),n(ReadableStreamBYOBReader.prototype.read,"read"),n(ReadableStreamBYOBReader.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableStreamBYOBReader.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});const ht="function"==typeof AbortController;class WritableStream{constructor(e={},t={}){void 0===e?e=null:F(e,"First parameter");const r=st(t,"Second parameter"),o=function(e,t){z(e,t);const r=null==e?void 0:e.abort,o=null==e?void 0:e.close,n=null==e?void 0:e.start,a=null==e?void 0:e.type,i=null==e?void 0:e.write;return{abort:void 0===r?void 0:ct(r,e,`${t} has member 'abort' that`),close:void 0===o?void 0:dt(o,e,`${t} has member 'close' that`),start:void 0===n?void 0:ft(n,e,`${t} has member 'start' that`),write:void 0===i?void 0:bt(i,e,`${t} has member 'write' that`),type:a}}(e,"First parameter");pt(this);if(void 0!==o.type)throw new RangeError("Invalid type is specified");const n=lt(r);!function(e,t,r,o){const n=Object.create(WritableStreamDefaultController.prototype);let a,i,l,s;a=void 0!==t.start?()=>t.start(n):()=>{};i=void 0!==t.write?e=>t.write(e,n):()=>u(void 0);l=void 0!==t.close?()=>t.close():()=>u(void 0);s=void 0!==t.abort?e=>t.abort(e):()=>u(void 0);zt(e,n,a,i,l,s,r,o)}(this,o,it(r,1),n)}get locked(){if(!yt(this))throw Qt("locked");return St(this)}abort(e=void 0){return yt(this)?St(this)?c(new TypeError("Cannot abort a stream that already has a writer")):gt(this,e):c(Qt("abort"))}close(){return yt(this)?St(this)?c(new TypeError("Cannot close a stream that already has a writer")):Ct(this)?c(new TypeError("Cannot close an already-closing stream")):vt(this):c(Qt("close"))}getWriter(){if(!yt(this))throw Qt("getWriter");return _t(this)}}function _t(e){return new WritableStreamDefaultWriter(e)}function pt(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new g,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function yt(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")&&e instanceof WritableStream)}function St(e){return void 0!==e._writer}function gt(e,t){var r;if("closed"===e._state||"errored"===e._state)return u(void 0);e._writableStreamController._abortReason=t,null===(r=e._writableStreamController._abortController)||void 0===r||r.abort(t);const o=e._state;if("closed"===o||"errored"===o)return u(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;let n=!1;"erroring"===o&&(n=!0,t=void 0);const a=s(((r,o)=>{e._pendingAbortRequest={_promise:void 0,_resolve:r,_reject:o,_reason:t,_wasAlreadyErroring:n}}));return e._pendingAbortRequest._promise=a,n||Rt(e,t),a}function vt(e){const t=e._state;if("closed"===t||"errored"===t)return c(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));const r=s(((t,r)=>{const o={_resolve:t,_reject:r};e._closeRequest=o})),o=e._writer;var n;return void 0!==o&&e._backpressure&&"writable"===t&&tr(o),Se(n=e._writableStreamController,At,0),It(n),r}function wt(e,t){"writable"!==e._state?Tt(e):Rt(e,t)}function Rt(e,t){const r=e._writableStreamController;e._state="erroring",e._storedError=t;const o=e._writer;void 0!==o&&Ot(o,t),!function(e){if(void 0===e._inFlightWriteRequest&&void 0===e._inFlightCloseRequest)return!1;return!0}(e)&&r._started&&Tt(e)}function Tt(e){e._state="errored",e._writableStreamController[w]();const t=e._storedError;if(e._writeRequests.forEach((e=>{e._reject(t)})),e._writeRequests=new g,void 0===e._pendingAbortRequest)return void Pt(e);const r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring)return r._reject(t),void Pt(e);f(e._writableStreamController[v](r._reason),(()=>(r._resolve(),Pt(e),null)),(t=>(r._reject(t),Pt(e),null)))}function Ct(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function Pt(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);const t=e._writer;void 0!==t&&Gt(t,e._storedError)}function qt(e,t){const r=e._writer;void 0!==r&&t!==e._backpressure&&(t?function(e){Jt(e)}(r):tr(r)),e._backpressure=t}Object.defineProperties(WritableStream.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),n(WritableStream.prototype.abort,"abort"),n(WritableStream.prototype.close,"close"),n(WritableStream.prototype.getWriter,"getWriter"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(WritableStream.prototype,Symbol.toStringTag,{value:"WritableStream",configurable:!0});class WritableStreamDefaultWriter{constructor(e){if(I(e,1,"WritableStreamDefaultWriter"),mt(e,"First parameter"),St(e))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;const t=e._state;if("writable"===t)!Ct(e)&&e._backpressure?Jt(this):Zt(this),Vt(this);else if("erroring"===t)Kt(this,e._storedError),Vt(this);else if("closed"===t)Zt(this),Vt(r=this),Xt(r);else{const t=e._storedError;Kt(this,t),Ut(this,t)}var r}get closed(){return Et(this)?this._closedPromise:c(Nt("closed"))}get desiredSize(){if(!Et(this))throw Nt("desiredSize");if(void 0===this._ownerWritableStream)throw Ht("desiredSize");return function(e){const t=e._ownerWritableStream,r=t._state;if("errored"===r||"erroring"===r)return null;if("closed"===r)return 0;return Ft(t._writableStreamController)}(this)}get ready(){return Et(this)?this._readyPromise:c(Nt("ready"))}abort(e=void 0){return Et(this)?void 0===this._ownerWritableStream?c(Ht("abort")):function(e,t){return gt(e._ownerWritableStream,t)}(this,e):c(Nt("abort"))}close(){if(!Et(this))return c(Nt("close"));const e=this._ownerWritableStream;return void 0===e?c(Ht("close")):Ct(e)?c(new TypeError("Cannot close an already-closing stream")):Wt(this)}releaseLock(){if(!Et(this))throw Nt("releaseLock");void 0!==this._ownerWritableStream&&jt(this)}write(e=void 0){return Et(this)?void 0===this._ownerWritableStream?c(Ht("write to")):kt(this,e):c(Nt("write"))}}function Et(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")&&e instanceof WritableStreamDefaultWriter)}function Wt(e){return vt(e._ownerWritableStream)}function Bt(e,t){"pending"===e._closedPromiseState?Gt(e,t):function(e,t){Ut(e,t)}(e,t)}function Ot(e,t){"pending"===e._readyPromiseState?er(e,t):function(e,t){Kt(e,t)}(e,t)}function jt(e){const t=e._ownerWritableStream,r=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");Ot(e,r),Bt(e,r),t._writer=void 0,e._ownerWritableStream=void 0}function kt(e,t){const r=e._ownerWritableStream,o=r._writableStreamController,n=function(e,t){try{return e._strategySizeAlgorithm(t)}catch(t){return $t(e,t),1}}(o,t);if(r!==e._ownerWritableStream)return c(Ht("write to"));const a=r._state;if("errored"===a)return c(r._storedError);if(Ct(r)||"closed"===a)return c(new TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===a)return c(r._storedError);const i=function(e){return s(((t,r)=>{const o={_resolve:t,_reject:r};e._writeRequests.push(o)}))}(r);return function(e,t,r){try{Se(e,t,r)}catch(t){return void $t(e,t)}const o=e._controlledWritableStream;if(!Ct(o)&&"writable"===o._state){qt(o,Mt(e))}It(e)}(o,t,n),i}Object.defineProperties(WritableStreamDefaultWriter.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),n(WritableStreamDefaultWriter.prototype.abort,"abort"),n(WritableStreamDefaultWriter.prototype.close,"close"),n(WritableStreamDefaultWriter.prototype.releaseLock,"releaseLock"),n(WritableStreamDefaultWriter.prototype.write,"write"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(WritableStreamDefaultWriter.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});const At={};class WritableStreamDefaultController{constructor(){throw new TypeError("Illegal constructor")}get abortReason(){if(!Dt(this))throw xt("abortReason");return this._abortReason}get signal(){if(!Dt(this))throw xt("signal");if(void 0===this._abortController)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(e=void 0){if(!Dt(this))throw xt("error");"writable"===this._controlledWritableStream._state&&Yt(this,e)}[v](e){const t=this._abortAlgorithm(e);return Lt(this),t}[w](){ge(this)}}function Dt(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")&&e instanceof WritableStreamDefaultController)}function zt(e,t,r,o,n,a,i,l){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,ge(t),t._abortReason=void 0,t._abortController=function(){if(ht)return new AbortController}(),t._started=!1,t._strategySizeAlgorithm=l,t._strategyHWM=i,t._writeAlgorithm=o,t._closeAlgorithm=n,t._abortAlgorithm=a;const s=Mt(t);qt(e,s);f(u(r()),(()=>(t._started=!0,It(t),null)),(r=>(t._started=!0,wt(e,r),null)))}function Lt(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function Ft(e){return e._strategyHWM-e._queueTotalSize}function It(e){const t=e._controlledWritableStream;if(!e._started)return;if(void 0!==t._inFlightWriteRequest)return;if("erroring"===t._state)return void Tt(t);if(0===e._queue.length)return;const r=e._queue.peek().value;r===At?function(e){const t=e._controlledWritableStream;(function(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0})(t),ye(e);const r=e._closeAlgorithm();Lt(e),f(r,(()=>(function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";const t=e._writer;void 0!==t&&Xt(t)}(t),null)),(e=>(function(e,t){e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),wt(e,t)}(t,e),null)))}(e):function(e,t){const r=e._controlledWritableStream;!function(e){e._inFlightWriteRequest=e._writeRequests.shift()}(r);const o=e._writeAlgorithm(t);f(o,(()=>{!function(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}(r);const t=r._state;if(ye(e),!Ct(r)&&"writable"===t){const t=Mt(e);qt(r,t)}return It(e),null}),(t=>("writable"===r._state&&Lt(e),function(e,t){e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,wt(e,t)}(r,t),null)))}(e,r)}function $t(e,t){"writable"===e._controlledWritableStream._state&&Yt(e,t)}function Mt(e){return Ft(e)<=0}function Yt(e,t){const r=e._controlledWritableStream;Lt(e),Rt(r,t)}function Qt(e){return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function xt(e){return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function Nt(e){return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function Ht(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function Vt(e){e._closedPromise=s(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"}))}function Ut(e,t){Vt(e),Gt(e,t)}function Gt(e,t){void 0!==e._closedPromise_reject&&(_(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function Xt(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function Jt(e){e._readyPromise=s(((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r})),e._readyPromiseState="pending"}function Kt(e,t){Jt(e),er(e,t)}function Zt(e){Jt(e),tr(e)}function er(e,t){void 0!==e._readyPromise_reject&&(_(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function tr(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(WritableStreamDefaultController.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(WritableStreamDefaultController.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});const rr="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof global?global:void 0;const or=function(){const e=null==rr?void 0:rr.DOMException;return function(e){if("function"!=typeof e&&"object"!=typeof e)return!1;if("DOMException"!==e.name)return!1;try{return new e,!0}catch(e){return!1}}(e)?e:void 0}()||function(){const e=function(e,t){this.message=e||"",this.name=t||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return n(e,"DOMException"),e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}();function nr(e,r,o,n,a,i){const l=N(e),h=_t(r);e._disturbed=!0;let p=!1,y=u(void 0);return s(((S,g)=>{let v;if(void 0!==i){if(v=()=>{const t=void 0!==i.reason?i.reason:new or("Aborted","AbortError"),o=[];n||o.push((()=>"writable"===r._state?gt(r,t):u(void 0))),a||o.push((()=>"readable"===e._state?Er(e,t):u(void 0))),q((()=>Promise.all(o.map((e=>e())))),!0,t)},i.aborted)return void v();i.addEventListener("abort",v)}var w,R,T;if(P(e,l._closedPromise,(e=>(n?W(!0,e):q((()=>gt(r,e)),!0,e),null))),P(r,h._closedPromise,(t=>(a?W(!0,t):q((()=>Er(e,t)),!0,t),null))),w=e,R=l._closedPromise,T=()=>(o?W():q((()=>function(e){const t=e._ownerWritableStream,r=t._state;return Ct(t)||"closed"===r?u(void 0):"errored"===r?c(t._storedError):Wt(e)}(h))),null),"closed"===w._state?T():b(R,T),Ct(r)||"closed"===r._state){const t=new TypeError("the destination writable stream closed before all data could be piped to it");a?W(!0,t):q((()=>Er(e,t)),!0,t)}function C(){const e=y;return d(y,(()=>e!==y?C():void 0))}function P(e,t,r){"errored"===e._state?r(e._storedError):m(t,r)}function q(e,t,o){function n(){return f(e(),(()=>B(t,o)),(e=>B(!0,e))),null}p||(p=!0,"writable"!==r._state||Ct(r)?n():b(C(),n))}function W(e,t){p||(p=!0,"writable"!==r._state||Ct(r)?B(e,t):b(C(),(()=>B(e,t))))}function B(e,t){return jt(h),E(l),void 0!==i&&i.removeEventListener("abort",v),e?g(t):S(void 0),null}_(s(((e,r)=>{!function o(n){n?e():d(p?u(!0):d(h._readyPromise,(()=>s(((e,r)=>{J(l,{_chunkSteps:r=>{y=d(kt(h,r),void 0,t),e(!1)},_closeSteps:()=>e(!0),_errorSteps:r})})))),o,r)}(!1)})))}))}class ReadableStreamDefaultController{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!ar(this))throw hr("desiredSize");return fr(this)}close(){if(!ar(this))throw hr("close");if(!br(this))throw new TypeError("The stream is not in a state that permits close");ur(this)}enqueue(e=void 0){if(!ar(this))throw hr("enqueue");if(!br(this))throw new TypeError("The stream is not in a state that permits enqueue");return cr(this,e)}error(e=void 0){if(!ar(this))throw hr("error");dr(this,e)}[R](e){ge(this);const t=this._cancelAlgorithm(e);return sr(this),t}[T](e){const t=this._controlledReadableStream;if(this._queue.length>0){const r=ye(this);this._closeRequested&&0===this._queue.length?(sr(this),Wr(t)):ir(this),e._chunkSteps(r)}else H(t,e),ir(this)}[C](){}}function ar(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")&&e instanceof ReadableStreamDefaultController)}function ir(e){if(!lr(e))return;if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0;f(e._pullAlgorithm(),(()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,ir(e)),null)),(t=>(dr(e,t),null)))}function lr(e){const t=e._controlledReadableStream;if(!br(e))return!1;if(!e._started)return!1;if(qr(t)&&U(t)>0)return!0;return fr(e)>0}function sr(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function ur(e){if(!br(e))return;const t=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(sr(e),Wr(t))}function cr(e,t){if(!br(e))return;const r=e._controlledReadableStream;if(qr(r)&&U(r)>0)V(r,t,!1);else{let r;try{r=e._strategySizeAlgorithm(t)}catch(t){throw dr(e,t),t}try{Se(e,t,r)}catch(t){throw dr(e,t),t}}ir(e)}function dr(e,t){const r=e._controlledReadableStream;"readable"===r._state&&(ge(e),sr(e),Br(r,t))}function fr(e){const t=e._controlledReadableStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function br(e){const t=e._controlledReadableStream._state;return!e._closeRequested&&"readable"===t}function mr(e,t,r,o,n,a,i){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,ge(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=i,t._strategyHWM=a,t._pullAlgorithm=o,t._cancelAlgorithm=n,e._readableStreamController=t;f(u(r()),(()=>(t._started=!0,ir(t),null)),(e=>(dr(t,e),null)))}function hr(e){return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function _r(e,t){return we(e._readableStreamController)?function(e){let t,r,o,n,a,i=N(e),l=!1,c=!1,d=!1,f=!1,b=!1;const h=s((e=>{a=e}));function _(e){m(e._closedPromise,(t=>(e!==i||(Ye(o._readableStreamController,t),Ye(n._readableStreamController,t),f&&b||a(void 0)),null)))}function y(){rt(i)&&(E(i),i=N(e),_(i));J(i,{_chunkSteps:t=>{p((()=>{c=!1,d=!1;const r=t;let i=t;if(!f&&!b)try{i=pe(t)}catch(t){return Ye(o._readableStreamController,t),Ye(n._readableStreamController,t),void a(Er(e,t))}f||Me(o._readableStreamController,r),b||Me(n._readableStreamController,i),l=!1,c?g():d&&v()}))},_closeSteps:()=>{l=!1,f||$e(o._readableStreamController),b||$e(n._readableStreamController),o._readableStreamController._pendingPullIntos.length>0&&He(o._readableStreamController,0),n._readableStreamController._pendingPullIntos.length>0&&He(n._readableStreamController,0),f&&b||a(void 0)},_errorSteps:()=>{l=!1}})}function S(t,r){X(i)&&(E(i),i=Ke(e),_(i));const s=r?n:o,u=r?o:n;ot(i,t,1,{_chunkSteps:t=>{p((()=>{c=!1,d=!1;const o=r?b:f;if(r?f:b)o||Ve(s._readableStreamController,t);else{let r;try{r=pe(t)}catch(t){return Ye(s._readableStreamController,t),Ye(u._readableStreamController,t),void a(Er(e,t))}o||Ve(s._readableStreamController,t),Me(u._readableStreamController,r)}l=!1,c?g():d&&v()}))},_closeSteps:e=>{l=!1;const t=r?b:f,o=r?f:b;t||$e(s._readableStreamController),o||$e(u._readableStreamController),void 0!==e&&(t||Ve(s._readableStreamController,e),!o&&u._readableStreamController._pendingPullIntos.length>0&&He(u._readableStreamController,0)),t&&o||a(void 0)},_errorSteps:()=>{l=!1}})}function g(){if(l)return c=!0,u(void 0);l=!0;const e=xe(o._readableStreamController);return null===e?y():S(e._view,!1),u(void 0)}function v(){if(l)return d=!0,u(void 0);l=!0;const e=xe(n._readableStreamController);return null===e?y():S(e._view,!0),u(void 0)}function w(o){if(f=!0,t=o,b){const o=ue([t,r]),n=Er(e,o);a(n)}return h}function R(o){if(b=!0,r=o,f){const o=ue([t,r]),n=Er(e,o);a(n)}return h}function T(){}return o=Tr(T,g,w),n=Tr(T,v,R),_(i),[o,n]}(e):function(e,t){const r=N(e);let o,n,a,i,l,c=!1,d=!1,f=!1,b=!1;const h=s((e=>{l=e}));function _(){if(c)return d=!0,u(void 0);c=!0;return J(r,{_chunkSteps:e=>{p((()=>{d=!1;const t=e,r=e;f||cr(a._readableStreamController,t),b||cr(i._readableStreamController,r),c=!1,d&&_()}))},_closeSteps:()=>{c=!1,f||ur(a._readableStreamController),b||ur(i._readableStreamController),f&&b||l(void 0)},_errorSteps:()=>{c=!1}}),u(void 0)}function y(t){if(f=!0,o=t,b){const t=ue([o,n]),r=Er(e,t);l(r)}return h}function S(t){if(b=!0,n=t,f){const t=ue([o,n]),r=Er(e,t);l(r)}return h}function g(){}return a=Rr(g,_,y),i=Rr(g,_,S),m(r._closedPromise,(e=>(dr(a._readableStreamController,e),dr(i._readableStreamController,e),f&&b||l(void 0),null))),[a,i]}(e)}function pr(e){return r(o=e)&&void 0!==o.getReader?function(e){let o;function n(){let t;try{t=e.read()}catch(e){return c(e)}return h(t,(e=>{if(!r(e))throw new TypeError("The promise returned by the reader.read() method must fulfill with an object");if(e.done)ur(o._readableStreamController);else{const t=e.value;cr(o._readableStreamController,t)}}))}function a(t){try{return u(e.cancel(t))}catch(e){return c(e)}}return o=Rr(t,n,a,0),o}(e.getReader()):function(e){let o;const n=_e(e,"async");function a(){let e;try{e=function(e){const t=y(e.nextMethod,e.iterator,[]);if(!r(t))throw new TypeError("The iterator.next() method must return an object");return t}(n)}catch(e){return c(e)}return h(u(e),(e=>{if(!r(e))throw new TypeError("The promise returned by the iterator.next() method must fulfill with an object");const t=function(e){return Boolean(e.done)}(e);if(t)ur(o._readableStreamController);else{const t=function(e){return e.value}(e);cr(o._readableStreamController,t)}}))}function i(e){const t=n.iterator;let o,a;try{o=me(t,"return")}catch(e){return c(e)}if(void 0===o)return u(void 0);try{a=y(o,t,[e])}catch(e){return c(e)}return h(u(a),(e=>{if(!r(e))throw new TypeError("The promise returned by the iterator.return() method must fulfill with an object")}))}return o=Rr(t,a,i,0),o}(e);var o}function yr(e,t,r){return L(e,r),r=>S(e,t,[r])}function Sr(e,t,r){return L(e,r),r=>S(e,t,[r])}function gr(e,t,r){return L(e,r),r=>y(e,t,[r])}function vr(e,t){if("bytes"!==(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}function wr(e,t){z(e,t);const r=null==e?void 0:e.preventAbort,o=null==e?void 0:e.preventCancel,n=null==e?void 0:e.preventClose,a=null==e?void 0:e.signal;return void 0!==a&&function(e,t){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw new TypeError(`${t} is not an AbortSignal.`)}(a,`${t} has member 'signal' that`),{preventAbort:Boolean(r),preventCancel:Boolean(o),preventClose:Boolean(n),signal:a}}Object.defineProperties(ReadableStreamDefaultController.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),n(ReadableStreamDefaultController.prototype.close,"close"),n(ReadableStreamDefaultController.prototype.enqueue,"enqueue"),n(ReadableStreamDefaultController.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableStreamDefaultController.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});class ReadableStream{constructor(e={},t={}){void 0===e?e=null:F(e,"First parameter");const r=st(t,"Second parameter"),o=function(e,t){z(e,t);const r=e,o=null==r?void 0:r.autoAllocateChunkSize,n=null==r?void 0:r.cancel,a=null==r?void 0:r.pull,i=null==r?void 0:r.start,l=null==r?void 0:r.type;return{autoAllocateChunkSize:void 0===o?void 0:Q(o,`${t} has member 'autoAllocateChunkSize' that`),cancel:void 0===n?void 0:yr(n,r,`${t} has member 'cancel' that`),pull:void 0===a?void 0:Sr(a,r,`${t} has member 'pull' that`),start:void 0===i?void 0:gr(i,r,`${t} has member 'start' that`),type:void 0===l?void 0:vr(l,`${t} has member 'type' that`)}}(e,"First parameter");if(Cr(this),"bytes"===o.type){if(void 0!==r.size)throw new RangeError("The strategy for a byte stream cannot have a size function");!function(e,t,r){const o=Object.create(ReadableByteStreamController.prototype);let n,a,i;n=void 0!==t.start?()=>t.start(o):()=>{},a=void 0!==t.pull?()=>t.pull(o):()=>u(void 0),i=void 0!==t.cancel?e=>t.cancel(e):()=>u(void 0);const l=t.autoAllocateChunkSize;if(0===l)throw new TypeError("autoAllocateChunkSize must be greater than 0");Ue(e,o,n,a,i,r,l)}(this,o,it(r,0))}else{const e=lt(r);!function(e,t,r,o){const n=Object.create(ReadableStreamDefaultController.prototype);let a,i,l;a=void 0!==t.start?()=>t.start(n):()=>{},i=void 0!==t.pull?()=>t.pull(n):()=>u(void 0),l=void 0!==t.cancel?e=>t.cancel(e):()=>u(void 0),mr(e,n,a,i,l,r,o)}(this,o,it(r,1),e)}}get locked(){if(!Pr(this))throw Or("locked");return qr(this)}cancel(e=void 0){return Pr(this)?qr(this)?c(new TypeError("Cannot cancel a stream that already has a reader")):Er(this,e):c(Or("cancel"))}getReader(e=void 0){if(!Pr(this))throw Or("getReader");return void 0===function(e,t){z(e,t);const r=null==e?void 0:e.mode;return{mode:void 0===r?void 0:Je(r,`${t} has member 'mode' that`)}}(e,"First parameter").mode?N(this):Ke(this)}pipeThrough(e,t={}){if(!Pr(this))throw Or("pipeThrough");I(e,1,"pipeThrough");const r=function(e,t){z(e,t);const r=null==e?void 0:e.readable;$(r,"readable","ReadableWritablePair"),x(r,`${t} has member 'readable' that`);const o=null==e?void 0:e.writable;return $(o,"writable","ReadableWritablePair"),mt(o,`${t} has member 'writable' that`),{readable:r,writable:o}}(e,"First parameter"),o=wr(t,"Second parameter");if(qr(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(St(r.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return _(nr(this,r.writable,o.preventClose,o.preventAbort,o.preventCancel,o.signal)),r.readable}pipeTo(e,t={}){if(!Pr(this))return c(Or("pipeTo"));if(void 0===e)return c("Parameter 1 is required in 'pipeTo'.");if(!yt(e))return c(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));let r;try{r=wr(t,"Second parameter")}catch(e){return c(e)}return qr(this)?c(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):St(e)?c(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):nr(this,e,r.preventClose,r.preventAbort,r.preventCancel,r.signal)}tee(){if(!Pr(this))throw Or("tee");return ue(_r(this))}values(e=void 0){if(!Pr(this))throw Or("values");return function(e,t){const r=N(e),o=new te(r,t),n=Object.create(re);return n._asyncIteratorImpl=o,n}(this,function(e,t){z(e,t);const r=null==e?void 0:e.preventCancel;return{preventCancel:Boolean(r)}}(e,"First parameter").preventCancel)}[he](e){return this.values(e)}static from(e){return pr(e)}}function Rr(e,t,r,o=1,n=(()=>1)){const a=Object.create(ReadableStream.prototype);Cr(a);return mr(a,Object.create(ReadableStreamDefaultController.prototype),e,t,r,o,n),a}function Tr(e,t,r){const o=Object.create(ReadableStream.prototype);Cr(o);return Ue(o,Object.create(ReadableByteStreamController.prototype),e,t,r,0,void 0),o}function Cr(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function Pr(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")&&e instanceof ReadableStream)}function qr(e){return void 0!==e._reader}function Er(e,r){if(e._disturbed=!0,"closed"===e._state)return u(void 0);if("errored"===e._state)return c(e._storedError);Wr(e);const o=e._reader;if(void 0!==o&&rt(o)){const e=o._readIntoRequests;o._readIntoRequests=new g,e.forEach((e=>{e._closeSteps(void 0)}))}return h(e._readableStreamController[R](r),t)}function Wr(e){e._state="closed";const t=e._reader;if(void 0!==t&&(k(t),X(t))){const e=t._readRequests;t._readRequests=new g,e.forEach((e=>{e._closeSteps()}))}}function Br(e,t){e._state="errored",e._storedError=t;const r=e._reader;void 0!==r&&(j(r,t),X(r)?K(r,t):nt(r,t))}function Or(e){return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function jr(e,t){z(e,t);const r=null==e?void 0:e.highWaterMark;return $(r,"highWaterMark","QueuingStrategyInit"),{highWaterMark:M(r)}}Object.defineProperties(ReadableStream,{from:{enumerable:!0}}),Object.defineProperties(ReadableStream.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),n(ReadableStream.from,"from"),n(ReadableStream.prototype.cancel,"cancel"),n(ReadableStream.prototype.getReader,"getReader"),n(ReadableStream.prototype.pipeThrough,"pipeThrough"),n(ReadableStream.prototype.pipeTo,"pipeTo"),n(ReadableStream.prototype.tee,"tee"),n(ReadableStream.prototype.values,"values"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableStream.prototype,Symbol.toStringTag,{value:"ReadableStream",configurable:!0}),Object.defineProperty(ReadableStream.prototype,he,{value:ReadableStream.prototype.values,writable:!0,configurable:!0});const kr=e=>e.byteLength;n(kr,"size");class ByteLengthQueuingStrategy{constructor(e){I(e,1,"ByteLengthQueuingStrategy"),e=jr(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!Dr(this))throw Ar("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!Dr(this))throw Ar("size");return kr}}function Ar(e){return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function Dr(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")&&e instanceof ByteLengthQueuingStrategy)}Object.defineProperties(ByteLengthQueuingStrategy.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ByteLengthQueuingStrategy.prototype,Symbol.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});const zr=()=>1;n(zr,"size");class CountQueuingStrategy{constructor(e){I(e,1,"CountQueuingStrategy"),e=jr(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!Fr(this))throw Lr("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!Fr(this))throw Lr("size");return zr}}function Lr(e){return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function Fr(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")&&e instanceof CountQueuingStrategy)}function Ir(e,t,r){return L(e,r),r=>S(e,t,[r])}function $r(e,t,r){return L(e,r),r=>y(e,t,[r])}function Mr(e,t,r){return L(e,r),(r,o)=>S(e,t,[r,o])}function Yr(e,t,r){return L(e,r),r=>S(e,t,[r])}Object.defineProperties(CountQueuingStrategy.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(CountQueuingStrategy.prototype,Symbol.toStringTag,{value:"CountQueuingStrategy",configurable:!0});class TransformStream{constructor(e={},t={},r={}){void 0===e&&(e=null);const o=st(t,"Second parameter"),n=st(r,"Third parameter"),a=function(e,t){z(e,t);const r=null==e?void 0:e.cancel,o=null==e?void 0:e.flush,n=null==e?void 0:e.readableType,a=null==e?void 0:e.start,i=null==e?void 0:e.transform,l=null==e?void 0:e.writableType;return{cancel:void 0===r?void 0:Yr(r,e,`${t} has member 'cancel' that`),flush:void 0===o?void 0:Ir(o,e,`${t} has member 'flush' that`),readableType:n,start:void 0===a?void 0:$r(a,e,`${t} has member 'start' that`),transform:void 0===i?void 0:Mr(i,e,`${t} has member 'transform' that`),writableType:l}}(e,"First parameter");if(void 0!==a.readableType)throw new RangeError("Invalid readableType specified");if(void 0!==a.writableType)throw new RangeError("Invalid writableType specified");const i=it(n,0),l=lt(n),d=it(o,1),b=lt(o);let m;!function(e,t,r,o,n,a){function i(){return t}function l(t){return function(e,t){const r=e._transformStreamController;if(e._backpressure){return h(e._backpressureChangePromise,(()=>{const o=e._writable;if("erroring"===o._state)throw o._storedError;return Jr(r,t)}))}return Jr(r,t)}(e,t)}function u(t){return function(e,t){const r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;const o=e._readable;r._finishPromise=s(((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t}));const n=r._cancelAlgorithm(t);return Gr(r),f(n,(()=>("errored"===o._state?eo(r,o._storedError):(dr(o._readableStreamController,t),Zr(r)),null)),(e=>(dr(o._readableStreamController,e),eo(r,e),null))),r._finishPromise}(e,t)}function c(){return function(e){const t=e._transformStreamController;if(void 0!==t._finishPromise)return t._finishPromise;const r=e._readable;t._finishPromise=s(((e,r)=>{t._finishPromise_resolve=e,t._finishPromise_reject=r}));const o=t._flushAlgorithm();return Gr(t),f(o,(()=>("errored"===r._state?eo(t,r._storedError):(ur(r._readableStreamController),Zr(t)),null)),(e=>(dr(r._readableStreamController,e),eo(t,e),null))),t._finishPromise}(e)}function d(){return function(e){return Vr(e,!1),e._backpressureChangePromise}(e)}function b(t){return function(e,t){const r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;const o=e._writable;r._finishPromise=s(((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t}));const n=r._cancelAlgorithm(t);return Gr(r),f(n,(()=>("errored"===o._state?eo(r,o._storedError):($t(o._writableStreamController,t),Hr(e),Zr(r)),null)),(t=>($t(o._writableStreamController,t),Hr(e),eo(r,t),null))),r._finishPromise}(e,t)}e._writable=function(e,t,r,o,n=1,a=(()=>1)){const i=Object.create(WritableStream.prototype);return pt(i),zt(i,Object.create(WritableStreamDefaultController.prototype),e,t,r,o,n,a),i}(i,l,c,u,r,o),e._readable=Rr(i,d,b,n,a),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,Vr(e,!0),e._transformStreamController=void 0}(this,s((e=>{m=e})),d,b,i,l),function(e,t){const r=Object.create(TransformStreamDefaultController.prototype);let o,n,a;o=void 0!==t.transform?e=>t.transform(e,r):e=>{try{return Xr(r,e),u(void 0)}catch(e){return c(e)}};n=void 0!==t.flush?()=>t.flush(r):()=>u(void 0);a=void 0!==t.cancel?e=>t.cancel(e):()=>u(void 0);!function(e,t,r,o,n){t._controlledTransformStream=e,e._transformStreamController=t,t._transformAlgorithm=r,t._flushAlgorithm=o,t._cancelAlgorithm=n,t._finishPromise=void 0,t._finishPromise_resolve=void 0,t._finishPromise_reject=void 0}(e,r,o,n,a)}(this,a),void 0!==a.start?m(a.start(this._transformStreamController)):m(void 0)}get readable(){if(!Qr(this))throw to("readable");return this._readable}get writable(){if(!Qr(this))throw to("writable");return this._writable}}function Qr(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")&&e instanceof TransformStream)}function xr(e,t){dr(e._readable._readableStreamController,t),Nr(e,t)}function Nr(e,t){Gr(e._transformStreamController),$t(e._writable._writableStreamController,t),Hr(e)}function Hr(e){e._backpressure&&Vr(e,!1)}function Vr(e,t){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=s((t=>{e._backpressureChangePromise_resolve=t})),e._backpressure=t}Object.defineProperties(TransformStream.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(TransformStream.prototype,Symbol.toStringTag,{value:"TransformStream",configurable:!0});class TransformStreamDefaultController{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!Ur(this))throw Kr("desiredSize");return fr(this._controlledTransformStream._readable._readableStreamController)}enqueue(e=void 0){if(!Ur(this))throw Kr("enqueue");Xr(this,e)}error(e=void 0){if(!Ur(this))throw Kr("error");var t;t=e,xr(this._controlledTransformStream,t)}terminate(){if(!Ur(this))throw Kr("terminate");!function(e){const t=e._controlledTransformStream;ur(t._readable._readableStreamController);const r=new TypeError("TransformStream terminated");Nr(t,r)}(this)}}function Ur(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")&&e instanceof TransformStreamDefaultController)}function Gr(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0,e._cancelAlgorithm=void 0}function Xr(e,t){const r=e._controlledTransformStream,o=r._readable._readableStreamController;if(!br(o))throw new TypeError("Readable side is not in a state that permits enqueue");try{cr(o,t)}catch(e){throw Nr(r,e),r._readable._storedError}const n=function(e){return!lr(e)}(o);n!==r._backpressure&&Vr(r,!0)}function Jr(e,t){return h(e._transformAlgorithm(t),void 0,(t=>{throw xr(e._controlledTransformStream,t),t}))}function Kr(e){return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function Zr(e){void 0!==e._finishPromise_resolve&&(e._finishPromise_resolve(),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function eo(e,t){void 0!==e._finishPromise_reject&&(_(e._finishPromise),e._finishPromise_reject(t),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function to(e){return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}Object.defineProperties(TransformStreamDefaultController.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),n(TransformStreamDefaultController.prototype.enqueue,"enqueue"),n(TransformStreamDefaultController.prototype.error,"error"),n(TransformStreamDefaultController.prototype.terminate,"terminate"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(TransformStreamDefaultController.prototype,Symbol.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});const ro={ReadableStream:ReadableStream,ReadableStreamDefaultController:ReadableStreamDefaultController,ReadableByteStreamController:ReadableByteStreamController,ReadableStreamBYOBRequest:ReadableStreamBYOBRequest,ReadableStreamDefaultReader:ReadableStreamDefaultReader,ReadableStreamBYOBReader:ReadableStreamBYOBReader,WritableStream:WritableStream,WritableStreamDefaultController:WritableStreamDefaultController,WritableStreamDefaultWriter:WritableStreamDefaultWriter,ByteLengthQueuingStrategy:ByteLengthQueuingStrategy,CountQueuingStrategy:CountQueuingStrategy,TransformStream:TransformStream,TransformStreamDefaultController:TransformStreamDefaultController};if(void 0!==rr)for(const e in ro)Object.prototype.hasOwnProperty.call(ro,e)&&Object.defineProperty(rr,e,{value:ro[e],writable:!0,configurable:!0});e.ByteLengthQueuingStrategy=ByteLengthQueuingStrategy,e.CountQueuingStrategy=CountQueuingStrategy,e.ReadableByteStreamController=ReadableByteStreamController,e.ReadableStream=ReadableStream,e.ReadableStreamBYOBReader=ReadableStreamBYOBReader,e.ReadableStreamBYOBRequest=ReadableStreamBYOBRequest,e.ReadableStreamDefaultController=ReadableStreamDefaultController,e.ReadableStreamDefaultReader=ReadableStreamDefaultReader,e.TransformStream=TransformStream,e.TransformStreamDefaultController=TransformStreamDefaultController,e.WritableStream=WritableStream,e.WritableStreamDefaultController=WritableStreamDefaultController,e.WritableStreamDefaultWriter=WritableStreamDefaultWriter}));
//# sourceMappingURL=polyfill.es2018.min.js.map
