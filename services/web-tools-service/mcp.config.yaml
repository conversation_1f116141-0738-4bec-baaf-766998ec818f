name: web-tools-service
version: 1.0.0
description: Web tools and HTTP utilities service
transport: [http]

tools:
  - name: http_request
    description: Make HTTP requests to any URL
    inputSchema:
      type: object
      properties:
        url:
          type: string
          description: URL to request
        method:
          type: string
          enum: [GET, POST, PUT, DELETE]
          default: GET
        headers:
          type: object
          description: HTTP headers
        body:
          type: string
          description: Request body
      required: [url]
    handler: ./src/handlers/httpRequest.js

  - name: analyze_url
    description: Analyze and parse URL components
    inputSchema:
      type: object
      properties:
        url:
          type: string
          description: URL to analyze
      required: [url]
    handler: ./src/handlers/analyzeUrl.js

  - name: scrape_webpage
    description: Extract text content from a webpage
    inputSchema:
      type: object
      properties:
        url:
          type: string
          description: URL to scrape
        selector:
          type: string
          description: CSS selector (optional)
      required: [url]
    handler: ./src/handlers/scrapeWebpage.js

  - name: validate_url
    description: Validate if a URL is accessible
    inputSchema:
      type: object
      properties:
        url:
          type: string
          description: URL to validate
      required: [url]
    handler: ./src/handlers/validateUrl.js

build:
  outDir: dist
  target: node18
  format: esm

dev:
  port: 3000
  debug: true
  watch: [src/**/*]
