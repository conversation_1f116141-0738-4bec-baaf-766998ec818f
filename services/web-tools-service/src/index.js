#!/usr/bin/env node

/**
 * Web Tools MCP Service
 * 
 * Demonstrates factory function API with web tools
 * Transport: HTTP
 */

import { createServer } from 'http';
import { URL } from 'url';

console.log('🌐 Starting Web Tools MCP Service...');

// Simulated MCP Framework factory functions
function createMCPService(name, version) {
  return new ServiceBuilder(name, version);
}

class ServiceBuilder {
  constructor(name, version) {
    this.config = { name, version };
    this.tools = [];
    this.resources = [];
    this.prompts = [];
  }

  description(desc) {
    this.config.description = desc;
    return this;
  }

  tool(name, description, inputSchema, handler) {
    this.tools.push({ name, description, inputSchema, handler });
    return this;
  }

  resource(pattern, name, description, handler, mimeType) {
    this.resources.push({ pattern, name, description, handler, mimeType });
    return this;
  }

  build() {
    return new MCPService(this.config, this.tools, this.resources, this.prompts);
  }
}

class MCPService {
  constructor(config, tools, resources, prompts) {
    this.config = config;
    this.tools = new Map(tools.map(t => [t.name, t]));
    this.resources = resources;
    this.prompts = prompts;
    this.server = null;
  }

  async start(transports) {
    console.log(`📡 Service: ${this.config.name} v${this.config.version}`);
    console.log(`📝 Description: ${this.config.description}`);
    console.log(`🔧 Tools: ${Array.from(this.tools.keys()).join(', ')}`);
    
    if (transports.includes('http')) {
      await this.startHTTPServer();
    }
    
    // Demo the service
    await this.demo();
  }

  async startHTTPServer(port = 3000) {
    this.server = createServer(async (req, res) => {
      // Enable CORS
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

      if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
      }

      if (req.method === 'POST' && req.url === '/mcp') {
        await this.handleMCPRequest(req, res);
      } else if (req.method === 'GET' && req.url === '/') {
        this.handleStatusRequest(req, res);
      } else {
        res.writeHead(404, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Not found' }));
      }
    });

    this.server.listen(port, () => {
      console.log(`🌐 HTTP server listening on http://localhost:${port}`);
      console.log(`📡 MCP endpoint: http://localhost:${port}/mcp`);
    });
  }

  async handleMCPRequest(req, res) {
    try {
      const body = await this.readRequestBody(req);
      const message = JSON.parse(body);
      
      let result;
      if (message.method === 'tools/list') {
        result = this.listTools();
      } else if (message.method === 'tools/call') {
        result = await this.callTool(message.params.name, message.params.arguments);
      } else {
        throw new Error(`Unknown method: ${message.method}`);
      }

      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        jsonrpc: '2.0',
        result,
        id: message.id
      }));
    } catch (error) {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        jsonrpc: '2.0',
        error: { code: -32603, message: error.message },
        id: null
      }));
    }
  }

  handleStatusRequest(req, res) {
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(`
      <html>
        <head><title>${this.config.name}</title></head>
        <body>
          <h1>🌐 ${this.config.name}</h1>
          <p>${this.config.description}</p>
          <h2>Available Tools:</h2>
          <ul>
            ${Array.from(this.tools.values()).map(tool => 
              `<li><strong>${tool.name}</strong>: ${tool.description}</li>`
            ).join('')}
          </ul>
          <p>MCP Endpoint: <code>/mcp</code></p>
        </body>
      </html>
    `);
  }

  async readRequestBody(req) {
    return new Promise((resolve, reject) => {
      let body = '';
      req.on('data', chunk => body += chunk.toString());
      req.on('end', () => resolve(body));
      req.on('error', reject);
    });
  }

  listTools() {
    return {
      tools: Array.from(this.tools.values()).map(tool => ({
        name: tool.name,
        description: tool.description,
        inputSchema: tool.inputSchema
      }))
    };
  }

  async callTool(name, input) {
    const tool = this.tools.get(name);
    if (!tool) {
      throw new Error(`Tool not found: ${name}`);
    }
    
    const result = await tool.handler(input);
    return {
      content: [{
        type: 'text',
        text: typeof result === 'string' ? result : JSON.stringify(result, null, 2)
      }]
    };
  }

  async demo() {
    console.log('\n🧪 Running demo...');
    
    try {
      // Demo 1: HTTP Request
      console.log('\n🌐 Demo 1: HTTP Request');
      const httpResult = await this.callTool('http_request', {
        url: 'https://httpbin.org/json',
        method: 'GET'
      });
      console.log('   ✅ HTTP request successful');

      // Demo 2: URL Analysis
      console.log('\n🔍 Demo 2: URL Analysis');
      const urlResult = await this.callTool('analyze_url', {
        url: 'https://example.com/path?param=value#section'
      });
      console.log('   ✅ URL analysis completed');

      // Demo 3: Web Scraping
      console.log('\n📄 Demo 3: Web Scraping');
      const scrapeResult = await this.callTool('scrape_webpage', {
        url: 'https://httpbin.org/html'
      });
      console.log('   ✅ Web scraping completed');

      console.log('\n✅ Demo completed successfully!');
    } catch (error) {
      console.error('❌ Demo failed:', error.message);
    }
  }
}

// Create Web Tools Service
const service = createMCPService('web-tools-service', '1.0.0')
  .description('Web tools and HTTP utilities service')
  .tool(
    'http_request',
    'Make HTTP requests to any URL',
    {
      type: 'object',
      properties: {
        url: { type: 'string', description: 'URL to request' },
        method: { type: 'string', enum: ['GET', 'POST', 'PUT', 'DELETE'], default: 'GET' },
        headers: { type: 'object', description: 'HTTP headers' },
        body: { type: 'string', description: 'Request body' }
      },
      required: ['url']
    },
    async ({ url, method = 'GET', headers = {}, body }) => {
      try {
        // Simulate fetch (in real implementation, would use node-fetch)
        const response = await simulateFetch(url, { method, headers, body });
        return {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
          body: response.body
        };
      } catch (error) {
        throw new Error(`HTTP request failed: ${error.message}`);
      }
    }
  )
  .tool(
    'analyze_url',
    'Analyze and parse URL components',
    {
      type: 'object',
      properties: {
        url: { type: 'string', description: 'URL to analyze' }
      },
      required: ['url']
    },
    async ({ url }) => {
      try {
        const parsed = new URL(url);
        return {
          protocol: parsed.protocol,
          hostname: parsed.hostname,
          port: parsed.port,
          pathname: parsed.pathname,
          search: parsed.search,
          hash: parsed.hash,
          origin: parsed.origin,
          searchParams: Object.fromEntries(parsed.searchParams)
        };
      } catch (error) {
        throw new Error(`Invalid URL: ${error.message}`);
      }
    }
  )
  .tool(
    'scrape_webpage',
    'Extract text content from a webpage',
    {
      type: 'object',
      properties: {
        url: { type: 'string', description: 'URL to scrape' },
        selector: { type: 'string', description: 'CSS selector (optional)' }
      },
      required: ['url']
    },
    async ({ url, selector }) => {
      try {
        // Simulate web scraping
        const content = await simulateWebScraping(url, selector);
        return {
          url,
          title: content.title,
          text: content.text,
          links: content.links,
          images: content.images
        };
      } catch (error) {
        throw new Error(`Web scraping failed: ${error.message}`);
      }
    }
  )
  .tool(
    'validate_url',
    'Validate if a URL is accessible',
    {
      type: 'object',
      properties: {
        url: { type: 'string', description: 'URL to validate' }
      },
      required: ['url']
    },
    async ({ url }) => {
      try {
        const response = await simulateFetch(url, { method: 'HEAD' });
        return {
          url,
          valid: response.status < 400,
          status: response.status,
          statusText: response.statusText,
          responseTime: response.responseTime
        };
      } catch (error) {
        return {
          url,
          valid: false,
          error: error.message
        };
      }
    }
  )
  .build();

// Simulation functions (in real implementation, these would use actual libraries)
async function simulateFetch(url, options = {}) {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  return {
    status: 200,
    statusText: 'OK',
    headers: { 'content-type': 'application/json' },
    body: JSON.stringify({ 
      url, 
      method: options.method || 'GET',
      timestamp: new Date().toISOString(),
      simulated: true 
    }),
    responseTime: 100
  };
}

async function simulateWebScraping(url, selector) {
  // Simulate scraping delay
  await new Promise(resolve => setTimeout(resolve, 200));
  
  return {
    title: 'Example Page Title',
    text: 'This is simulated webpage content. In a real implementation, this would contain the actual scraped text.',
    links: [
      { text: 'Example Link', href: 'https://example.com' },
      { text: 'Another Link', href: 'https://example.org' }
    ],
    images: [
      { alt: 'Example Image', src: 'https://example.com/image.jpg' }
    ]
  };
}

// Start the service
await service.start(['http']);
