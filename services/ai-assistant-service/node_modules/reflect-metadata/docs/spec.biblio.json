{"https://rbuckton.github.io/reflect-metadata": [{"type": "clause", "id": "introduction", "aoid": null, "title": "Metadata Proposal - ECMAScript", "titleHTML": "Metadata Proposal - ECMAScript", "number": "", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "Metadata Proposal - ECMAScript"}, {"type": "clause", "id": "syntax", "aoid": null, "title": "Syntax", "titleHTML": "Syntax", "number": "1", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "Syntax"}, {"type": "op", "aoid": "GetOrCreateMetadataMap", "refId": "getorcreatemetadatamap", "location": "", "referencingIds": [], "key": "GetOrCreateMetadataMap"}, {"type": "clause", "id": "getorcreatemetadatamap", "aoid": "GetOrCreateMetadataMap", "title": "GetOrCreateMetadataMap ( O, P, Create )", "titleHTML": "GetOrCreateMetadataMap ( O, P, Create )", "number": "2.1.1", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": ["_ref_8", "_ref_17", "_ref_22", "_ref_42", "_ref_52"], "key": "GetOrCreateMetadataMap ( O, P, Create )"}, {"type": "clause", "id": "operations-on-objects", "aoid": null, "title": "Operations on Objects", "titleHTML": "Operations on Objects", "number": "2.1", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "Operations on Objects"}, {"type": "clause", "id": "abstract-operations", "aoid": null, "title": "Abstract Operations", "titleHTML": "Abstract Operations", "number": "2", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "Abstract Operations"}, {"type": "op", "aoid": "OrdinaryHasMetadata", "refId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "location": "", "referencingIds": [], "key": "OrdinaryHasMetadata"}, {"type": "clause", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aoid": "OrdinaryHasMetadata", "title": "OrdinaryHasMetadata ( MetadataKey, O, P )", "titleHTML": "OrdinaryHasMetadata ( MetadataKey, O, P )", "number": "3.1.1.1", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": ["_ref_3"], "key": "OrdinaryHasMetadata ( MetadataKey, O, P )"}, {"type": "clause", "id": "ordinary-object-internal-methods-and-internal-slots-hasmetadata", "aoid": null, "title": "[[HasMetadata]] ( <PERSON>ada<PERSON>, <PERSON> )", "titleHTML": "[[HasMetadata]] ( <PERSON>ada<PERSON>, <PERSON> )", "number": "3.1.1", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "[[HasMetadata]] ( <PERSON>ada<PERSON>, <PERSON> )"}, {"type": "op", "aoid": "OrdinaryHasOwnMetadata", "refId": "ordinaryhasownmetadata", "location": "", "referencingIds": [], "key": "OrdinaryHasOwnMetadata"}, {"type": "clause", "id": "ordinaryhasownmetadata", "aoid": "OrdinaryHasOwnMetadata", "title": "OrdinaryHasOwnMetadata ( MetadataKey, O, P )", "titleHTML": "OrdinaryHasOwnMetadata ( MetadataKey, O, P )", "number": "3.1.2.1", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": ["_ref_5", "_ref_6", "_ref_13"], "key": "OrdinaryHasOwnMetadata ( MetadataKey, O, P )"}, {"type": "clause", "id": "ordinary-object-internal-methods-and-internal-slots-hasownmetadata", "aoid": null, "title": "[[HasOwnMetadata]] ( Metadata<PERSON>, P )", "titleHTML": "[[HasOwnMetadata]] ( Metadata<PERSON>, P )", "number": "3.1.2", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "[[HasOwnMetadata]] ( Metadata<PERSON>, P )"}, {"type": "op", "aoid": "OrdinaryGetMetadata", "refId": "ordinarygetmetadata", "location": "", "referencingIds": [], "key": "OrdinaryGetMetadata"}, {"type": "clause", "id": "ordinarygetmetadata", "aoid": "OrdinaryGetMetadata", "title": "OrdinaryGetMetadata ( MetadataKey, O, P )", "titleHTML": "OrdinaryGetMetadata ( MetadataKey, O, P )", "number": "3.1.3.1", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": ["_ref_11"], "key": "OrdinaryGetMetadata ( MetadataKey, O, P )"}, {"type": "clause", "id": "ordinary-object-internal-methods-and-internal-slots-getmetadata", "aoid": null, "title": "[[GetMetadata]] ( <PERSON>ada<PERSON>, <PERSON> )", "titleHTML": "[[GetMetadata]] ( <PERSON>ada<PERSON>, <PERSON> )", "number": "3.1.3", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "[[GetMetadata]] ( <PERSON>ada<PERSON>, <PERSON> )"}, {"type": "op", "aoid": "OrdinaryGetOwnMetadata", "refId": "ordinarygetownmetadata", "location": "", "referencingIds": [], "key": "OrdinaryGetOwnMetadata"}, {"type": "clause", "id": "ordinarygetownmetadata", "aoid": "OrdinaryGetOwnMetadata", "title": "OrdinaryGetOwnMetadata ( MetadataKey, O, P )", "titleHTML": "OrdinaryGetOwnMetadata ( MetadataKey, O, P )", "number": "3.1.4.1", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": ["_ref_14", "_ref_15"], "key": "OrdinaryGetOwnMetadata ( MetadataKey, O, P )"}, {"type": "clause", "id": "ordinary-object-internal-methods-and-internal-slots-getownmetadata", "aoid": null, "title": "[[GetOwnMetadata]] ( MetadataKey, P, ParamIndex )", "titleHTML": "[[GetOwnMetadata]] ( MetadataKey, P, ParamIndex )", "number": "3.1.4", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "[[GetOwnMetadata]] ( MetadataKey, P, ParamIndex )"}, {"type": "op", "aoid": "OrdinaryDefineOwnMetadata", "refId": "ordinarydefineownmetadata", "location": "", "referencingIds": [], "key": "OrdinaryDefineOwnMetadata"}, {"type": "clause", "id": "ordinarydefineownmetadata", "aoid": "OrdinaryDefineOwnMetadata", "title": "OrdinaryDefineOwnMetadata ( MetadataKey, MetadataValue, O, P )", "titleHTML": "OrdinaryDefineOwnMetadata ( MetadataKey, MetadataValue, O, P )", "number": "3.1.5.1", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": ["_ref_19"], "key": "OrdinaryDefineOwnMetadata ( MetadataKey, MetadataValue, O, P )"}, {"type": "clause", "id": "ordinary-object-internal-methods-and-internal-slots-defineownmetadata", "aoid": null, "title": "[[DefineOwnMetadata]] ( MetadataKey, MetadataValue, P )", "titleHTML": "[[DefineOwnMetadata]] ( MetadataKey, MetadataValue, P )", "number": "3.1.5", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "[[DefineOwnMetadata]] ( MetadataKey, MetadataValue, P )"}, {"type": "op", "aoid": "OrdinaryMetadataKeys", "refId": "ordinarymetadatakeys", "location": "", "referencingIds": [], "key": "OrdinaryMetadataKeys"}, {"type": "clause", "id": "ordinarymetadatakeys", "aoid": "OrdinaryMetadataKeys", "title": "OrdinaryMetadataKeys ( O, P )", "titleHTML": "OrdinaryMetadataKeys ( O, P )", "number": "3.1.6.1", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": ["_ref_24"], "key": "OrdinaryMetadataKeys ( O, P )"}, {"type": "clause", "id": "ordinary-object-internal-methods-and-internal-slots-metadatakeys", "aoid": null, "title": "[[Metada<PERSON><PERSON><PERSON><PERSON>]] ( P )", "titleHTML": "[[Metada<PERSON><PERSON><PERSON><PERSON>]] ( P )", "number": "3.1.6", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "[[Metada<PERSON><PERSON><PERSON><PERSON>]] ( P )"}, {"type": "op", "aoid": "OrdinaryOwnMetadataKeys", "refId": "ordinaryownmetadatakeys", "location": "", "referencingIds": [], "key": "OrdinaryOwnMetadataKeys"}, {"type": "clause", "id": "ordinaryownmetadatakeys", "aoid": "OrdinaryOwnMetadataKeys", "title": "OrdinaryOwnMetadataKeys ( O, P )", "titleHTML": "OrdinaryOwnMetadataKeys ( O, P )", "number": "3.1.7.1", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": ["_ref_26", "_ref_39"], "key": "OrdinaryOwnMetadataKeys ( O, P )"}, {"type": "clause", "id": "ordinary-object-internal-methods-and-internal-slots-ownmetadatakeys", "aoid": null, "title": "[[OwnMetadataKeys]] ( P )", "titleHTML": "[[OwnMetadataKeys]] ( P )", "number": "3.1.7", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "[[OwnMetadataKeys]] ( P )"}, {"type": "clause", "id": "ordinary-object-internal-methods-and-internal-slots-deletemetadata", "aoid": null, "title": "[[DeleteMetadata]]( <PERSON>ada<PERSON>, <PERSON> )", "titleHTML": "[[DeleteMetadata]]( <PERSON>ada<PERSON>, <PERSON> )", "number": "3.1.8", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "[[DeleteMetadata]]( <PERSON>ada<PERSON>, <PERSON> )"}, {"type": "clause", "id": "ordinary-object-internal-methods-and-internal-slots", "aoid": null, "title": "Ordinary Object Internal Methods and Internal Slots", "titleHTML": "Ordinary Object Internal Methods and Internal Slots", "number": "3.1", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "Ordinary Object Internal Methods and Internal Slots"}, {"type": "clause", "id": "ordinary-and-exotic-objects-behaviors", "aoid": null, "title": "Ordinary and Exotic Objects Behaviors", "titleHTML": "Ordinary and Exotic Objects Behaviors", "number": "3", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "Ordinary and Exotic Objects Behaviors"}, {"type": "clause", "id": "reflect-metadatadecoratorfunctions", "aoid": null, "title": "Metadata Decorator Functions", "titleHTML": "Metadata Decorator Functions", "number": "4.1.1", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "Metadata Decorator Functions"}, {"type": "clause", "id": "reflect.metadata", "aoid": null, "title": "Reflect.metadata ( metadataKey, metadataValue )", "titleHTML": "Reflect.metadata ( metadataKey, metadataValue )", "number": "4.1.2", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "Reflect.metadata ( metadataKey, metadataValue )"}, {"type": "clause", "id": "reflect.definemetadata", "aoid": null, "title": "Reflect.defineMetadata ( metadataKey, metadataValue, target [, propertyKey] )", "titleHTML": "Reflect.defineMetadata ( metadataKey, metadataValue, target [, propertyKey] )", "number": "4.1.3", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "Reflect.defineMetadata ( metadataKey, metadataValue, target [, propertyKey] )"}, {"type": "clause", "id": "<PERSON>.hasmetadata", "aoid": null, "title": "Reflect.hasMetadata ( metadataKey, target [, propertyKey] )", "titleHTML": "Reflect.hasMetadata ( metadataKey, target [, propertyKey] )", "number": "4.1.4", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "Reflect.hasMetadata ( metadataKey, target [, propertyKey] )"}, {"type": "clause", "id": "<PERSON>.has<PERSON>adata", "aoid": null, "title": "Reflect.hasOwnMetadata ( metadataKey, target [, propertyKey] )", "titleHTML": "Reflect.hasOwnMetadata ( metadataKey, target [, propertyKey] )", "number": "4.1.5", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "Reflect.hasOwnMetadata ( metadataKey, target [, propertyKey] )"}, {"type": "clause", "id": "<PERSON>.getmetadata", "aoid": null, "title": "Reflect.getMetadata ( metadataKey, target [, propertyKey] )", "titleHTML": "Reflect.getMetadata ( metadataKey, target [, propertyKey] )", "number": "4.1.6", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "Reflect.getMetadata ( metadataKey, target [, propertyKey] )"}, {"type": "clause", "id": "<PERSON>.getownmetadata", "aoid": null, "title": "Reflect.getOwnMetadata ( metadataKey, target [, propertyKey] )", "titleHTML": "Reflect.getOwnMetadata ( metadataKey, target [, propertyKey] )", "number": "4.1.7", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "Reflect.getOwnMetadata ( metadataKey, target [, propertyKey] )"}, {"type": "clause", "id": "reflect.getmetada<PERSON>ys", "aoid": null, "title": "Reflect.getMetadataKeys ( target [, propertyKey] )", "titleHTML": "Reflect.getMetadataKeys ( target [, propertyKey] )", "number": "4.1.8", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "Reflect.getMetadataKeys ( target [, propertyKey] )"}, {"type": "clause", "id": "reflect.getownmetadatakeys", "aoid": null, "title": "Reflect.getOwnMetadataKeys ( target [, propertyKey] )", "titleHTML": "Reflect.getOwnMetadataKeys ( target [, propertyKey] )", "number": "4.1.9", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "Reflect.getOwnMetadataKeys ( target [, propertyKey] )"}, {"type": "clause", "id": "<PERSON>.deletemetadata", "aoid": null, "title": "Reflect.deleteMetadata ( metadataKey, target [, propertyKey] )", "titleHTML": "Reflect.deleteMetadata ( metadataKey, target [, propertyKey] )", "number": "4.1.10", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "Reflect.deleteMetadata ( metadataKey, target [, propertyKey] )"}, {"type": "clause", "id": "reflect", "aoid": null, "title": "The Reflect Object", "titleHTML": "The Reflect Object", "number": "4.1", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "The Reflect Object"}, {"type": "clause", "id": "reflection", "aoid": null, "title": "Reflection", "titleHTML": "Reflection", "number": "4", "namespace": "https://rbuckton.github.io/reflect-metadata", "location": "", "referencingIds": [], "key": "Reflection"}]}