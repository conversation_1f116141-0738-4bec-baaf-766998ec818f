body {
  display: flex;
  font-size: 18px;
  line-height: 1.5;
  font-family: Cambria, Palatino <PERSON>, Palatino, Liberation Serif, serif;
  padding: 0;
  margin: 0;
  color: #111;
}

#spec-container {
  padding: 0 20px;
  flex-grow: 1;
  flex-basis: 66%;
  box-sizing: border-box;
  overflow: hidden;
}

body.oldtoc {
  margin: 0 auto;
}

a {
    text-decoration: none;
    color: #206ca7;
}

a:visited {
  color: #206ca7;
}

a:hover {
    text-decoration: underline;
    color: #239dee;
}


code {
    font-weight: bold;
    font-family: Consolas, Monaco, monospace;
    white-space: pre;
}

pre code {
  font-weight: inherit;
}

pre code.hljs {
  background-color: #fff;
  margin: 0;
  padding: 0;
}

ol.toc {
    list-style: none;
    padding-left: 0;
}

ol.toc ol.toc {
    padding-left: 2ex;
    list-style: none;
}

var {
  color: #2aa198;
  transition: background-color 0.25s ease;
  cursor: pointer;
}

var.referenced {
  background-color: #ffff33;
}

emu-const {
  font-family: sans-serif;
}

emu-val {
  font-weight: bold;
}

/* depth 1 */
emu-alg ol,
/* depth 4 */
emu-alg ol ol ol ol,
emu-alg ol.nested-thrice,
emu-alg ol.nested-twice ol,
emu-alg ol.nested-once ol ol {
  list-style-type: decimal;
}

/* depth 2 */
emu-alg ol ol,
emu-alg ol.nested-once,
/* depth 5 */
emu-alg ol ol ol ol ol,
emu-alg ol.nested-four-times,
emu-alg ol.nested-thrice ol,
emu-alg ol.nested-twice ol ol,
emu-alg ol.nested-once ol ol ol {
  list-style-type: lower-alpha;
}

/* depth 3 */
emu-alg ol ol ol,
emu-alg ol.nested-twice,
emu-alg ol.nested-once ol,
/* depth 6 */
emu-alg ol ol ol ol ol ol,
emu-alg ol.nested-lots,
emu-alg ol.nested-four-times ol,
emu-alg ol.nested-thrice ol ol,
emu-alg ol.nested-twice ol ol ol,
emu-alg ol.nested-once ol ol ol ol,
/* depth 7+ */
emu-alg ol.nested-lots ol {
  list-style-type: lower-roman;
}

emu-eqn {
  display: block;
  margin-left: 4em;
}

emu-eqn.inline {
  display: inline;
  margin: 0;
}

emu-eqn div:first-child {
  margin-left: -2em;
}

emu-note {
    margin: 1em 0;
    color: #666;
    border-left: 5px solid #ccc;
    display: flex;
    flex-direction: row;
}

emu-note > span.note {
    flex-basis: 100px;
    min-width: 100px;
    flex-grow: 0;
    flex-shrink: 1;
    text-transform: uppercase;
    padding-left: 5px;
}

emu-note[type=editor] {
  border-left-color: #faa;
}

emu-note > div.note-contents {
  flex-grow: 1;
  flex-shrink: 1;
}

emu-note > div.note-contents > p:first-of-type {
  margin-top: 0;
}

emu-note > div.note-contents > p:last-of-type {
  margin-bottom: 0;
}

emu-table td code {
  white-space: normal;
} 

emu-figure {
  display: block;
}

emu-example {
  display: block;
  margin: 1em 3em;
}

emu-example figure figcaption {
  margin-top: 0.5em;
  text-align: left;
}

emu-figure figure,
emu-example figure,
emu-table figure {
  display: flex;
  flex-direction: column;
  align-items: center;
}

emu-production {
    display: block;
}

emu-grammar[type="example"] emu-production,
emu-grammar[type="definition"] emu-production {
    margin-top: 1em;
    margin-bottom: 1em;
    margin-left: 5ex;
}

emu-grammar.inline, emu-production.inline,
emu-grammar.inline emu-production emu-rhs, emu-production.inline emu-rhs,
emu-grammar[collapsed] emu-production emu-rhs, emu-production[collapsed] emu-rhs {
    display: inline;
    padding-left: 1ex;
    margin-left: 0;
}

emu-grammar[collapsed] emu-production, emu-production[collapsed] {
    margin: 0;
}

emu-constraints {
    font-size: .75em;
    margin-right: 1ex;
}

emu-gann {
    margin-right: 1ex;
}

emu-gann emu-t:last-child,
emu-gann emu-gprose:last-child,
emu-gann emu-nt:last-child {
    margin-right: 0;
}

emu-geq {
    margin-left: 1ex;
    font-weight: bold;
}

emu-oneof {
    font-weight: bold;
    margin-left: 1ex;
}

emu-nt {
    display: inline-block;
    font-style: italic;
    white-space: nowrap;
    text-indent: 0;
}

emu-nt a, emu-nt a:visited {
  color: #333;
}

emu-rhs emu-nt {
    margin-right: 1ex;
}

emu-t {
    display: inline-block;
    font-family: monospace;
    font-weight: bold;
    white-space: nowrap;
    text-indent: 0;
}

emu-production emu-t {
    margin-right: 1ex;
}

emu-rhs {
    display: block;
    padding-left: 75px;
    text-indent: -25px;
}

emu-mods {
    font-size: .85em;
    vertical-align: sub;
    font-style: normal;
    font-weight: normal;
}

emu-params, emu-opt {
  margin-right: 1ex;
  font-family: monospace;
}

emu-params, emu-constraints {
  color: #2aa198;
}

emu-opt {
  color: #b58900;
}

emu-gprose {
    font-size: 0.9em;
    font-family: Helvetica, Arial, sans-serif;
}

emu-production emu-gprose {
    margin-right: 1ex;
}

h1.shortname {
  color: #f60;
  font-size: 1.5em;
  margin: 0;
}

h1.version {
  color: #f60;
  font-size: 1.5em;
  margin: 0;
}

h1.title {
  margin-top: 0;
  color: #f60;
}

h1.first {
  margin-top: 0;
}

h1, h2, h3, h4, h5, h6 {
    position: relative;
}

h1 .secnum {
  text-decoration: none;
  margin-right: 5px;
}

h1 span.title {
  order: 2;
}


h1 { font-size: 2.67em; margin-top: 2em; margin-bottom: 0; line-height: 1em;}
h2 { font-size: 2em; }
h3 { font-size: 1.56em; }
h4 { font-size: 1.25em; }
h5 { font-size: 1.11em; }
h6 { font-size: 1em; }

h1:hover span.utils {
  display: block;
}

span.utils {
  font-size: 18px;
  line-height: 18px;
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  font-weight: normal;
}

span.utils:before {
    content: "⤷";
    display: inline-block;
    padding: 0 5px;
}

span.utils > * {
  display: inline-block;
  margin-right: 20px;
}

h1 span.utils span.anchor a,
h2 span.utils span.anchor a,
h3 span.utils span.anchor a,
h4 span.utils span.anchor a,
h5 span.utils span.anchor a,
h6 span.utils span.anchor a {
  text-decoration: none;
  font-variant: small-caps;
}

h1 span.utils span.anchor a:hover,
h2 span.utils span.anchor a:hover,
h3 span.utils span.anchor a:hover,
h4 span.utils span.anchor a:hover,
h5 span.utils span.anchor a:hover,
h6 span.utils span.anchor a:hover {
  color: #333;
}

emu-intro h1, emu-clause h1, emu-annex h1 { font-size: 2em; }
emu-intro h2, emu-clause h2, emu-annex h2 { font-size: 1.56em; }
emu-intro h3, emu-clause h3, emu-annex h3 { font-size: 1.25em; }
emu-intro h4, emu-clause h4, emu-annex h4 { font-size: 1.11em; }
emu-intro h5, emu-clause h5, emu-annex h5 { font-size: 1em; }
emu-intro h6, emu-clause h6, emu-annex h6 { font-size: 0.9em; }
emu-intro emu-intro h1, emu-clause emu-clause h1, emu-annex emu-annex h1 { font-size: 1.56em; }
emu-intro emu-intro h2, emu-clause emu-clause h2, emu-annex emu-annex h2 { font-size: 1.25em; }
emu-intro emu-intro h3, emu-clause emu-clause h3, emu-annex emu-annex h3 { font-size: 1.11em; }
emu-intro emu-intro h4, emu-clause emu-clause h4, emu-annex emu-annex h4 { font-size: 1em; }
emu-intro emu-intro h5, emu-clause emu-clause h5, emu-annex emu-annex h5 { font-size: 0.9em; }
emu-intro emu-intro emu-intro h1, emu-clause emu-clause emu-clause h1, emu-annex emu-annex emu-annex h1 { font-size: 1.25em; }
emu-intro emu-intro emu-intro h2, emu-clause emu-clause emu-clause h2, emu-annex emu-annex emu-annex h2 { font-size: 1.11em; }
emu-intro emu-intro emu-intro h3, emu-clause emu-clause emu-clause h3, emu-annex emu-annex emu-annex h3 { font-size: 1em; }
emu-intro emu-intro emu-intro h4, emu-clause emu-clause emu-clause h4, emu-annex emu-annex emu-annex h4 { font-size: 0.9em; }
emu-intro emu-intro emu-intro emu-intro h1, emu-clause emu-clause emu-clause emu-clause h1, emu-annex emu-annex emu-annex emu-annex h1 { font-size: 1.11em; }
emu-intro emu-intro emu-intro emu-intro h2, emu-clause emu-clause emu-clause emu-clause h2, emu-annex emu-annex emu-annex emu-annex h2 { font-size: 1em; }
emu-intro emu-intro emu-intro emu-intro h3, emu-clause emu-clause emu-clause emu-clause h3, emu-annex emu-annex emu-annex emu-annex h3 { font-size: 0.9em; }
emu-intro emu-intro emu-intro emu-intro emu-intro h1, emu-clause emu-clause emu-clause emu-clause emu-clause h1, emu-annex emu-annex emu-annex emu-annex emu-annex h1 { font-size: 1em; }
emu-intro emu-intro emu-intro emu-intro emu-intro h2, emu-clause emu-clause emu-clause emu-clause emu-clause h2, emu-annex emu-annex emu-annex emu-annex emu-annex h2 { font-size: 0.9em; }
emu-intro emu-intro emu-intro emu-intro emu-intro emu-intro h1, emu-clause emu-clause emu-clause emu-clause emu-clause emu-clause h1, emu-annex emu-annex emu-annex emu-annex emu-annex emu-annex h1 { font-size: 0.9em }

emu-clause, emu-intro, emu-annex {
    display: block;
}

/* Figures and tables */
figure { display: block; margin: 1em 0 3em 0; }
figure object { display: block; margin: 0 auto; }
figure table.real-table { margin: 0 auto; }
figure figcaption {
    display: block;
    color: #555555;
    font-weight: bold;
    text-align: center;
}

emu-table table {
  margin: 0 auto;
}

emu-table table, table.real-table {
    border-collapse: collapse;
}

emu-table td, emu-table th, table.real-table td, table.real-table th {
    border: 1px solid black;
    padding: 0.4em;
    vertical-align: baseline;
}
emu-table th, emu-table thead td, table.real-table th {
    background-color: #eeeeee;
}

/* Note: the left content edges of table.lightweight-table >tbody >tr >td
   and div.display line up. */
table.lightweight-table {
    border-collapse: collapse;
    margin: 0 0 0 1.5em;
}
table.lightweight-table td, table.lightweight-table th {
    border: none;
    padding: 0 0.5em;
    vertical-align: baseline;
}

/* diff styles */
ins {
    background-color: #e0f8e0;
    text-decoration: none;
    border-bottom: 1px solid #396;
}

del {
    background-color: #fee;
}

ins.block, del.block,
emu-production > ins, emu-production > del,
emu-grammar > ins, emu-grammar > del {
  display: block;
}
emu-rhs > ins, emu-rhs > del { 
  display: inline;
}

tr.ins > td > ins {
  border-bottom: none;
}

tr.ins > td {
  background-color: #e0f8e0;
}

tr.del > td {
  background-color: #fee;
}

/* Menu Styles */
#menu-toggle {
  font-size: 2em;

  position: fixed;
  top: 0;
  left: 0;
  width: 1.5em;
  height: 1.5em;
  z-index: 3;
  visibility: hidden;
  color: #1567a2;
  background-color: #fff;

  line-height: 1.5em;
  text-align: center;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;;

  cursor: pointer;
}

#menu {
  display: flex;
  flex-direction: column;
  width: 33%; height: 100vh;
  max-width: 500px;
  box-sizing: border-box;
  background-color: #ddd;
  overflow: hidden;
  transition: opacity 0.1s linear;
  padding: 0 5px;
  position: fixed;
  left: 0; top: 0;
  border-right: 2px solid #bbb;

  z-index: 2;
}

#menu-spacer {
  flex-basis: 33%;
  max-width: 500px;
  flex-grow: 0;
  flex-shrink: 0;
}

#menu a {
  color: #1567a2;
}

#menu.active {
  display: flex;
  opacity: 1;
  z-index: 2;
}

#menu-pins {
  flex-grow: 1;
  display: none;
}

#menu-pins.active {
  display: block;
}

#menu-pins-list {
  margin: 0;
  padding: 0;
  counter-reset: pins-counter;
}

#menu-pins-list > li:before {
  content: counter(pins-counter);
  counter-increment: pins-counter;
  display: inline-block;
  width: 25px;
  text-align: center;
  border: 1px solid #bbb;
  padding: 2px;
  margin: 4px;
  box-sizing: border-box;
  line-height: 1em;
  background-color: #ccc;
  border-radius: 4px;
}
#menu-toc > ol {
  padding: 0;
  flex-grow: 1;
}

#menu-toc > ol li {
  padding: 0;
}

#menu-toc > ol , #menu-toc > ol ol {
  list-style-type: none;
  margin: 0;
  padding: 0;
}

#menu-toc > ol ol {
  padding-left: 0.75em;
}

#menu-toc li {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

#menu-toc .item-toggle {
  display: inline-block;
  transform: rotate(-45deg) translate(-5px, -5px);
  transition: transform 0.1s ease;
  text-align: center;
  width: 20px;

  color: #aab;

  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;;

  cursor: pointer;
}

#menu-toc .item-toggle-none {
  display: inline-block;
  width: 20px;
}

#menu-toc li.active > .item-toggle {
  transform: rotate(45deg) translate(-5px, -5px);
}

#menu-toc li > ol {
  display: none;
}

#menu-toc li.active > ol {
  display: block;
}

#menu-toc li.revealed > a {
  background-color: #bbb;
  font-weight: bold;
  /*
  background-color: #222;
  color: #c6d8e4;
  */
}

#menu-toc li.revealed-leaf> a {
  color: #206ca7;
  /*
  background-color: #222;
  color: #c6d8e4;
  */
}

#menu-toc li.revealed > .item-toggle {
  transform: rotate(45deg) translate(-5px, -5px);
}

#menu-toc li.revealed > ol {
  display: block;
}

#menu-toc li > a {
  padding: 2px 5px;
}

#menu > * {
  margin-bottom: 5px;
}

.menu-pane-header {
  padding: 0 5px;
  text-transform: uppercase;
  background-color: #aaa;
  color: #335;
  font-weight: bold;
  letter-spacing: 2px;
  flex-grow: 0;
  flex-shrink: 0;
  font-size: 0.8em;
}

#menu-toc {
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  flex-grow: 1;
}

#menu-toc ol.toc {
  overflow-x: hidden;
  overflow-y: auto;
}

#menu-search {
  position: relative;
  flex-grow: 0;
  flex-shrink: 0;
  width: 100%;
  
  display: flex;
  flex-direction: column;
  
  max-height: 300px;
}

#menu-trace-list {
  display: none;
}

#menu-search-box {
  box-sizing: border-box;
  display: block;
  width: 100%;
  margin: 5px 0 0 0;
  font-size: 1em;
  padding: 2px;
  background-color: #bbb;
  border: 1px solid #999;
}

#menu-search-results {
  overflow-x: hidden;
  overflow-y: auto;
}

li.menu-search-result-clause:before {
    content: 'clause';
    width: 40px;
    display: inline-block;
    text-align: right;
    padding-right: 1ex;
    color: #666;
    font-size: 75%;
}
li.menu-search-result-op:before {
    content: 'op';
    width: 40px;
    display: inline-block;
    text-align: right;
    padding-right: 1ex;
    color: #666;
    font-size: 75%;
}

li.menu-search-result-prod:before {
    content: 'prod';
    width: 40px;
    display: inline-block;
    text-align: right;
    padding-right: 1ex;
    color: #666;
    font-size: 75%
}


li.menu-search-result-term:before {
    content: 'term';
    width: 40px;
    display: inline-block;
    text-align: right;
    padding-right: 1ex;
    color: #666;
    font-size: 75%
}

#menu-search-results ul {
  padding: 0 5px;
  margin: 0;
}

#menu-search-results li {
  white-space: nowrap;
  text-overflow: ellipsis;
}


#menu-trace-list {
  counter-reset: item;
  margin: 0 0 0 20px;
  padding: 0;
}
#menu-trace-list li {
  display: block;
  white-space: nowrap;
}

#menu-trace-list li .secnum:after {
  content: " ";
}
#menu-trace-list li:before {
    content: counter(item) " ";
    background-color: #222;
    counter-increment: item;
    color: #999;
    width: 20px;
    height: 20px;
    line-height: 20px;
    display: inline-block;
    text-align: center;
    margin: 2px 4px 2px 0;
}

@media (max-width: 1000px) {
  body {
    margin: 0;
    display: block;
  }

  #menu {
    display: none;
    padding-top: 3em;
    width: 450px;
  }

  #menu.active {
    position: fixed;
    height: 100%;
    left: 0;
    top: 0;
    right: 300px;
  }

  #menu-toggle {
    visibility: visible;
  }

  #spec-container {
    padding: 0 5px;
  }

  #references-pane-spacer {
    display: none;
  }
}

@media only screen and (max-width: 800px) {
  #menu {
    width: 100%;
  }

  h1 .secnum:empty {
    margin: 0; padding: 0;
  }
}


/* Toolbox */
.toolbox {
	position: absolute;
	background: #ddd;
	border: 1px solid #aaa;
  display: none;
  color: #eee;
  padding: 5px;
  border-radius: 3px;
}

.toolbox.active {
  display: inline-block;
}

.toolbox a {
  text-decoration: none;
  padding: 0 5px;
}

.toolbox a:hover {
  text-decoration: underline;
}

.toolbox:after, .toolbox:before {
	top: 100%;
	left: 15px;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
}

.toolbox:after {
	border-color: rgba(0, 0, 0, 0);
	border-top-color: #ddd;
	border-width: 10px;
	margin-left: -10px;
}
.toolbox:before {
	border-color: rgba(204, 204, 204, 0);
	border-top-color: #aaa;
	border-width: 12px;
	margin-left: -12px;
}

#references-pane-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 250px;
  display: none;
  background-color: #ddd;
  z-index: 1;
}

#references-pane-table-container {
  overflow-x: hidden;
  overflow-y: auto;
}

#references-pane-spacer {
  flex-basis: 33%;
  max-width: 500px;
}

#references-pane {
  flex-grow: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

#references-pane-container.active {
  display: flex;
}

#references-pane-close:after {
  content: '✖';
  float: right;
  cursor: pointer;
}

#references-pane table tr td:first-child {
  text-align: right;
  padding-right: 5px;
}

@media print {
  #menu-toggle {
    display: none; 
  }
}
