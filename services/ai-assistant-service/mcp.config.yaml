name: ai-assistant-service
version: 1.0.0
description: AI assistant with prompts and real-time communication
transport: [sse]

tools:
  - name: analyze_text
    description: Analyze text for sentiment, keywords, and insights
    inputSchema:
      type: object
      properties:
        text:
          type: string
          description: Text to analyze
        analysis_type:
          type: string
          enum: [sentiment, keywords, summary, all]
          default: all
      required: [text]
    handler: ./src/handlers/analyzeText.js

  - name: manage_conversation
    description: Manage conversation context and history
    inputSchema:
      type: object
      properties:
        action:
          type: string
          enum: [create, add, get, clear]
          description: Action to perform
        conversation_id:
          type: string
          description: Conversation ID
        message:
          type: string
          description: Message to add
        role:
          type: string
          enum: [user, assistant, system]
          default: user
      required: [action]
    handler: ./src/handlers/manageConversation.js

prompts:
  - name: coding_assistant
    description: Generate coding assistance prompts
    arguments:
      - name: language
        description: Programming language
        required: true
      - name: task
        description: Coding task description
        required: true
      - name: difficulty
        description: Difficulty level
        required: false
    handler: ./src/handlers/codingAssistant.js

  - name: data_analysis
    description: Generate data analysis prompts
    arguments:
      - name: data_type
        description: Type of data to analyze
        required: true
      - name: goal
        description: Analysis goal
        required: true
      - name: format
        description: Output format preference
        required: false
    handler: ./src/handlers/dataAnalysis.js

  - name: creative_writing
    description: Generate creative writing prompts
    arguments:
      - name: genre
        description: Writing genre
        required: true
      - name: theme
        description: Writing theme or topic
        required: true
      - name: length
        description: Desired length
        required: false
    handler: ./src/handlers/creativeWriting.js

build:
  outDir: dist
  target: node18
  format: esm

dev:
  port: 3001
  debug: true
  watch: [src/**/*]
