#!/usr/bin/env node

/**
 * AI Assistant MCP Service
 * 
 * Demonstrates mixed API with prompts and SSE transport
 * Transport: SSE (Server-Sent Events)
 */

import 'reflect-metadata';
import { createServer } from 'http';
import { URL } from 'url';

console.log('🤖 Starting AI Assistant MCP Service...');

// Simulated MCP Framework components
const TOOL_METADATA = Symbol('tools');
const PROMPT_METADATA = Symbol('prompts');

function tool(options) {
  return function(target, propertyKey, descriptor) {
    const tools = Reflect.getMetadata(TOOL_METADATA, target.constructor) || [];
    tools.push({
      name: options.name || propertyKey,
      description: options.description,
      inputSchema: options.inputSchema,
      handler: descriptor.value
    });
    Reflect.defineMetadata(TOOL_METADATA, tools, target.constructor);
    return descriptor;
  };
}

function prompt(options) {
  return function(target, propertyKey, descriptor) {
    const prompts = Reflect.getMetadata(PROMPT_METADATA, target.constructor) || [];
    prompts.push({
      name: options.name || propertyKey,
      description: options.description,
      arguments: options.arguments,
      handler: descriptor.value
    });
    Reflect.defineMetadata(PROMPT_METADATA, prompts, target.constructor);
    return descriptor;
  };
}

function mcpService(config) {
  return function(constructor) {
    Reflect.defineMetadata('service:config', config, constructor);
    return constructor;
  };
}

// AI Assistant Service Implementation
@mcpService({
  name: 'ai-assistant-service',
  version: '1.0.0',
  description: 'AI assistant with prompts and real-time communication'
})
class AIAssistantService {
  constructor() {
    this.conversations = new Map();
    this.templates = new Map();
    this.initializeTemplates();
  }

  initializeTemplates() {
    this.templates.set('coding', {
      system: 'You are an expert programmer who writes clean, efficient code.',
      userPrefix: 'Please help me with this coding task:'
    });
    
    this.templates.set('analysis', {
      system: 'You are a data analyst who provides clear insights.',
      userPrefix: 'Please analyze the following data:'
    });
    
    this.templates.set('creative', {
      system: 'You are a creative writer with vivid imagination.',
      userPrefix: 'Please help me create:'
    });
  }

  @tool({
    name: 'analyze_text',
    description: 'Analyze text for sentiment, keywords, and insights',
    inputSchema: {
      type: 'object',
      properties: {
        text: { type: 'string', description: 'Text to analyze' },
        analysis_type: { 
          type: 'string', 
          enum: ['sentiment', 'keywords', 'summary', 'all'],
          default: 'all'
        }
      },
      required: ['text']
    }
  })
  async analyzeText({ text, analysis_type = 'all' }) {
    const analysis = {};
    
    if (analysis_type === 'sentiment' || analysis_type === 'all') {
      analysis.sentiment = this.analyzeSentiment(text);
    }
    
    if (analysis_type === 'keywords' || analysis_type === 'all') {
      analysis.keywords = this.extractKeywords(text);
    }
    
    if (analysis_type === 'summary' || analysis_type === 'all') {
      analysis.summary = this.generateSummary(text);
    }
    
    return {
      text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
      analysis_type,
      results: analysis,
      timestamp: new Date().toISOString()
    };
  }

  @tool({
    name: 'manage_conversation',
    description: 'Manage conversation context and history',
    inputSchema: {
      type: 'object',
      properties: {
        action: { 
          type: 'string', 
          enum: ['create', 'add', 'get', 'clear'],
          description: 'Action to perform'
        },
        conversation_id: { type: 'string', description: 'Conversation ID' },
        message: { type: 'string', description: 'Message to add' },
        role: { 
          type: 'string', 
          enum: ['user', 'assistant', 'system'],
          default: 'user'
        }
      },
      required: ['action']
    }
  })
  async manageConversation({ action, conversation_id, message, role = 'user' }) {
    switch (action) {
      case 'create':
        const newId = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        this.conversations.set(newId, []);
        return { conversation_id: newId, created: true };
        
      case 'add':
        if (!conversation_id || !message) {
          throw new Error('conversation_id and message required for add action');
        }
        const conversation = this.conversations.get(conversation_id) || [];
        conversation.push({ role, content: message, timestamp: new Date().toISOString() });
        this.conversations.set(conversation_id, conversation);
        return { conversation_id, added: true, message_count: conversation.length };
        
      case 'get':
        if (!conversation_id) {
          throw new Error('conversation_id required for get action');
        }
        return { 
          conversation_id, 
          messages: this.conversations.get(conversation_id) || [],
          message_count: (this.conversations.get(conversation_id) || []).length
        };
        
      case 'clear':
        if (conversation_id) {
          this.conversations.delete(conversation_id);
          return { conversation_id, cleared: true };
        } else {
          const count = this.conversations.size;
          this.conversations.clear();
          return { cleared_all: true, count };
        }
        
      default:
        throw new Error(`Unknown action: ${action}`);
    }
  }

  @prompt({
    name: 'coding_assistant',
    description: 'Generate coding assistance prompts',
    arguments: [
      { name: 'language', description: 'Programming language', required: true },
      { name: 'task', description: 'Coding task description', required: true },
      { name: 'difficulty', description: 'Difficulty level', required: false }
    ]
  })
  async codingAssistant({ language, task, difficulty = 'intermediate' }) {
    const template = this.templates.get('coding');
    
    return [
      {
        role: 'system',
        content: {
          type: 'text',
          text: `${template.system} You specialize in ${language} programming. Provide ${difficulty} level solutions.`
        }
      },
      {
        role: 'user',
        content: {
          type: 'text',
          text: `${template.userPrefix} I need help with ${language}. Task: ${task}. Please provide a complete solution with explanations.`
        }
      }
    ];
  }

  @prompt({
    name: 'data_analysis',
    description: 'Generate data analysis prompts',
    arguments: [
      { name: 'data_type', description: 'Type of data to analyze', required: true },
      { name: 'goal', description: 'Analysis goal', required: true },
      { name: 'format', description: 'Output format preference', required: false }
    ]
  })
  async dataAnalysis({ data_type, goal, format = 'detailed report' }) {
    const template = this.templates.get('analysis');
    
    return [
      {
        role: 'system',
        content: {
          type: 'text',
          text: `${template.system} Focus on ${data_type} data and provide insights in ${format} format.`
        }
      },
      {
        role: 'user',
        content: {
          type: 'text',
          text: `${template.userPrefix} ${data_type} data. Goal: ${goal}. Please provide a comprehensive analysis.`
        }
      }
    ];
  }

  @prompt({
    name: 'creative_writing',
    description: 'Generate creative writing prompts',
    arguments: [
      { name: 'genre', description: 'Writing genre', required: true },
      { name: 'theme', description: 'Writing theme or topic', required: true },
      { name: 'length', description: 'Desired length', required: false }
    ]
  })
  async creativeWriting({ genre, theme, length = 'medium' }) {
    const template = this.templates.get('creative');
    
    return [
      {
        role: 'system',
        content: {
          type: 'text',
          text: `${template.system} You excel at ${genre} writing and create ${length} length pieces.`
        }
      },
      {
        role: 'user',
        content: {
          type: 'text',
          text: `${template.userPrefix} a ${genre} piece about ${theme}. Make it engaging and ${length} in length.`
        }
      }
    ];
  }

  // Helper methods
  analyzeSentiment(text) {
    // Simple sentiment analysis simulation
    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic'];
    const negativeWords = ['bad', 'terrible', 'awful', 'horrible', 'disappointing'];
    
    const words = text.toLowerCase().split(/\s+/);
    const positive = words.filter(word => positiveWords.includes(word)).length;
    const negative = words.filter(word => negativeWords.includes(word)).length;
    
    let sentiment = 'neutral';
    let score = 0;
    
    if (positive > negative) {
      sentiment = 'positive';
      score = (positive - negative) / words.length;
    } else if (negative > positive) {
      sentiment = 'negative';
      score = (negative - positive) / words.length;
    }
    
    return { sentiment, score, positive_words: positive, negative_words: negative };
  }

  extractKeywords(text) {
    // Simple keyword extraction simulation
    const stopWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3 && !stopWords.includes(word));
    
    const frequency = {};
    words.forEach(word => {
      frequency[word] = (frequency[word] || 0) + 1;
    });
    
    return Object.entries(frequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word, count]) => ({ word, count }));
  }

  generateSummary(text) {
    // Simple summary generation simulation
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const summary = sentences.slice(0, Math.min(3, Math.ceil(sentences.length / 3)));

    return {
      original_sentences: sentences.length,
      summary_sentences: summary.length,
      summary: summary.join('. ') + (summary.length > 0 ? '.' : ''),
      compression_ratio: summary.length / sentences.length
    };
  }
}

// SSE Service Runner
class SSEServiceRunner {
  constructor(serviceClass) {
    this.service = new serviceClass();
    this.tools = Reflect.getMetadata(TOOL_METADATA, serviceClass) || [];
    this.prompts = Reflect.getMetadata(PROMPT_METADATA, serviceClass) || [];
    this.config = Reflect.getMetadata('service:config', serviceClass) || {};
    this.connections = new Map();
  }

  async start(port = 3001) {
    console.log(`📡 Service: ${this.config.name} v${this.config.version}`);
    console.log(`📝 Description: ${this.config.description}`);
    console.log(`🔧 Tools: ${this.tools.map(t => t.name).join(', ')}`);
    console.log(`💬 Prompts: ${this.prompts.map(p => p.name).join(', ')}`);

    await this.startSSEServer(port);
    await this.demo();
  }

  async startSSEServer(port) {
    this.server = createServer(async (req, res) => {
      // Enable CORS
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Cache-Control');

      if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
      }

      const url = new URL(req.url, `http://${req.headers.host}`);

      if (req.method === 'GET' && url.pathname === '/events') {
        this.handleSSEConnection(req, res);
      } else if (req.method === 'POST' && url.pathname === '/mcp') {
        await this.handleMCPRequest(req, res);
      } else if (req.method === 'GET' && url.pathname === '/') {
        this.handleStatusRequest(req, res);
      } else {
        res.writeHead(404, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Not found' }));
      }
    });

    this.server.listen(port, () => {
      console.log(`🌐 SSE server listening on http://localhost:${port}`);
      console.log(`📡 Events endpoint: http://localhost:${port}/events`);
      console.log(`📡 MCP endpoint: http://localhost:${port}/mcp`);
    });
  }

  handleSSEConnection(req, res) {
    const connectionId = `sse_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Set SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
    });

    // Send initial connection message
    res.write(`data: ${JSON.stringify({ type: 'connected', id: connectionId })}\n\n`);

    // Store connection
    this.connections.set(connectionId, res);

    // Handle connection close
    req.on('close', () => {
      this.connections.delete(connectionId);
      console.log(`📡 SSE connection closed: ${connectionId}`);
    });

    console.log(`📡 New SSE connection: ${connectionId}`);
  }

  async handleMCPRequest(req, res) {
    try {
      const body = await this.readRequestBody(req);
      const message = JSON.parse(body);

      let result;
      if (message.method === 'tools/list') {
        result = this.listTools();
      } else if (message.method === 'tools/call') {
        result = await this.callTool(message.params.name, message.params.arguments);
      } else if (message.method === 'prompts/list') {
        result = this.listPrompts();
      } else if (message.method === 'prompts/get') {
        result = await this.getPrompt(message.params.name, message.params.arguments);
      } else {
        throw new Error(`Unknown method: ${message.method}`);
      }

      // Send response via HTTP
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        jsonrpc: '2.0',
        result,
        id: message.id
      }));

      // Also broadcast via SSE
      this.broadcast({
        type: 'mcp_response',
        method: message.method,
        result,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        jsonrpc: '2.0',
        error: { code: -32603, message: error.message },
        id: null
      }));
    }
  }

  handleStatusRequest(req, res) {
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(`
      <html>
        <head><title>${this.config.name}</title></head>
        <body>
          <h1>🤖 ${this.config.name}</h1>
          <p>${this.config.description}</p>
          <h2>Available Tools:</h2>
          <ul>
            ${this.tools.map(tool =>
              `<li><strong>${tool.name}</strong>: ${tool.description}</li>`
            ).join('')}
          </ul>
          <h2>Available Prompts:</h2>
          <ul>
            ${this.prompts.map(prompt =>
              `<li><strong>${prompt.name}</strong>: ${prompt.description}</li>`
            ).join('')}
          </ul>
          <p>SSE Events: <code>/events</code></p>
          <p>MCP Endpoint: <code>/mcp</code></p>
          <p>Active Connections: ${this.connections.size}</p>
        </body>
      </html>
    `);
  }

  async readRequestBody(req) {
    return new Promise((resolve, reject) => {
      let body = '';
      req.on('data', chunk => body += chunk.toString());
      req.on('end', () => resolve(body));
      req.on('error', reject);
    });
  }

  listTools() {
    return {
      tools: this.tools.map(tool => ({
        name: tool.name,
        description: tool.description,
        inputSchema: tool.inputSchema
      }))
    };
  }

  listPrompts() {
    return {
      prompts: this.prompts.map(prompt => ({
        name: prompt.name,
        description: prompt.description,
        arguments: prompt.arguments
      }))
    };
  }

  async callTool(name, input) {
    const tool = this.tools.find(t => t.name === name);
    if (!tool) {
      throw new Error(`Tool not found: ${name}`);
    }

    const result = await tool.handler.call(this.service, input);
    return {
      content: [{
        type: 'text',
        text: typeof result === 'string' ? result : JSON.stringify(result, null, 2)
      }]
    };
  }

  async getPrompt(name, args) {
    const prompt = this.prompts.find(p => p.name === name);
    if (!prompt) {
      throw new Error(`Prompt not found: ${name}`);
    }

    const messages = await prompt.handler.call(this.service, args || {});
    return {
      description: prompt.description,
      messages
    };
  }

  broadcast(data) {
    const message = `data: ${JSON.stringify(data)}\n\n`;
    for (const [id, res] of this.connections) {
      try {
        res.write(message);
      } catch (error) {
        this.connections.delete(id);
      }
    }
  }

  async demo() {
    console.log('\n🧪 Running demo...');

    try {
      // Demo 1: Text Analysis
      console.log('\n📊 Demo 1: Text Analysis');
      const analysisResult = await this.callTool('analyze_text', {
        text: 'This is a wonderful example of amazing text analysis. The results are fantastic and excellent!'
      });
      console.log('   ✅ Text analysis completed');

      // Demo 2: Conversation Management
      console.log('\n💬 Demo 2: Conversation Management');
      const convResult = await this.callTool('manage_conversation', { action: 'create' });
      console.log('   ✅ Conversation created');

      // Demo 3: Coding Prompt
      console.log('\n💻 Demo 3: Coding Prompt');
      const promptResult = await this.getPrompt('coding_assistant', {
        language: 'JavaScript',
        task: 'Create a REST API',
        difficulty: 'intermediate'
      });
      console.log('   ✅ Coding prompt generated');

      // Broadcast demo completion
      this.broadcast({
        type: 'demo_completed',
        message: 'AI Assistant demo completed successfully',
        timestamp: new Date().toISOString()
      });

      console.log('\n✅ Demo completed successfully!');
    } catch (error) {
      console.error('❌ Demo failed:', error.message);
    }
  }
}

// Start the AI Assistant service
const runner = new SSEServiceRunner(AIAssistantService);
await runner.start();
