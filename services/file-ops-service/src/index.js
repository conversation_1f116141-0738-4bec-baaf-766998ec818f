#!/usr/bin/env node

/**
 * File Operations MCP Service
 * 
 * Demonstrates decorator-based API with file operations
 * Transport: stdio
 */

import 'reflect-metadata';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🗂️  Starting File Operations MCP Service...');

// Simulated MCP Framework decorators and service
const TOOL_METADATA = Symbol('tools');
const RESOURCE_METADATA = Symbol('resources');

function tool(options) {
  return function(target, propertyKey, descriptor) {
    const tools = Reflect.getMetadata(TOOL_METADATA, target.constructor) || [];
    tools.push({
      name: options.name || propertyKey,
      description: options.description,
      inputSchema: options.inputSchema,
      handler: descriptor.value
    });
    Reflect.defineMetadata(TOOL_METADATA, tools, target.constructor);
    return descriptor;
  };
}

function resource(options) {
  return function(target, propertyKey, descriptor) {
    const resources = Reflect.getMetadata(RESOURCE_METADATA, target.constructor) || [];
    resources.push({
      pattern: options.pattern,
      name: options.name,
      description: options.description,
      handler: descriptor.value
    });
    Reflect.defineMetadata(RESOURCE_METADATA, resources, target.constructor);
    return descriptor;
  };
}

function mcpService(config) {
  return function(constructor) {
    Reflect.defineMetadata('service:config', config, constructor);
    return constructor;
  };
}

// File Operations Service Implementation
@mcpService({
  name: 'file-ops-service',
  version: '1.0.0',
  description: 'File operations service with decorators'
})
class FileOpsService {
  constructor() {
    this.allowedPaths = [process.cwd()]; // Security: only allow current directory
  }

  @tool({
    name: 'read_file',
    description: 'Read contents of a file',
    inputSchema: {
      type: 'object',
      properties: {
        path: { type: 'string', description: 'File path to read' }
      },
      required: ['path']
    }
  })
  async readFile({ path: filePath }) {
    try {
      const resolvedPath = path.resolve(filePath);
      
      // Security check
      if (!this.isPathAllowed(resolvedPath)) {
        throw new Error('Access denied: path outside allowed directories');
      }

      const content = await fs.readFile(resolvedPath, 'utf-8');
      const stats = await fs.stat(resolvedPath);
      
      return {
        path: filePath,
        content,
        size: stats.size,
        modified: stats.mtime.toISOString()
      };
    } catch (error) {
      throw new Error(`Failed to read file: ${error.message}`);
    }
  }

  @tool({
    name: 'write_file',
    description: 'Write content to a file',
    inputSchema: {
      type: 'object',
      properties: {
        path: { type: 'string', description: 'File path to write' },
        content: { type: 'string', description: 'Content to write' }
      },
      required: ['path', 'content']
    }
  })
  async writeFile({ path: filePath, content }) {
    try {
      const resolvedPath = path.resolve(filePath);
      
      if (!this.isPathAllowed(resolvedPath)) {
        throw new Error('Access denied: path outside allowed directories');
      }

      await fs.writeFile(resolvedPath, content, 'utf-8');
      const stats = await fs.stat(resolvedPath);
      
      return {
        path: filePath,
        size: stats.size,
        written: new Date().toISOString()
      };
    } catch (error) {
      throw new Error(`Failed to write file: ${error.message}`);
    }
  }

  @tool({
    name: 'list_directory',
    description: 'List contents of a directory',
    inputSchema: {
      type: 'object',
      properties: {
        path: { type: 'string', description: 'Directory path to list' }
      },
      required: ['path']
    }
  })
  async listDirectory({ path: dirPath }) {
    try {
      const resolvedPath = path.resolve(dirPath);
      
      if (!this.isPathAllowed(resolvedPath)) {
        throw new Error('Access denied: path outside allowed directories');
      }

      const entries = await fs.readdir(resolvedPath, { withFileTypes: true });
      const items = [];

      for (const entry of entries) {
        const fullPath = path.join(resolvedPath, entry.name);
        const stats = await fs.stat(fullPath);
        
        items.push({
          name: entry.name,
          type: entry.isDirectory() ? 'directory' : 'file',
          size: stats.size,
          modified: stats.mtime.toISOString()
        });
      }

      return {
        path: dirPath,
        items,
        count: items.length
      };
    } catch (error) {
      throw new Error(`Failed to list directory: ${error.message}`);
    }
  }

  @tool({
    name: 'search_files',
    description: 'Search for files by name pattern',
    inputSchema: {
      type: 'object',
      properties: {
        pattern: { type: 'string', description: 'Search pattern (glob-like)' },
        directory: { type: 'string', description: 'Directory to search in', default: '.' }
      },
      required: ['pattern']
    }
  })
  async searchFiles({ pattern, directory = '.' }) {
    try {
      const resolvedDir = path.resolve(directory);
      
      if (!this.isPathAllowed(resolvedDir)) {
        throw new Error('Access denied: path outside allowed directories');
      }

      const results = await this.searchInDirectory(resolvedDir, pattern);
      
      return {
        pattern,
        directory,
        results,
        count: results.length
      };
    } catch (error) {
      throw new Error(`Failed to search files: ${error.message}`);
    }
  }

  @resource({
    pattern: 'file://**',
    name: 'File System',
    description: 'Access to local file system'
  })
  async handleFileResource(uri) {
    const filePath = uri.replace('file://', '');
    const resolvedPath = path.resolve(filePath);
    
    if (!this.isPathAllowed(resolvedPath)) {
      throw new Error('Access denied: path outside allowed directories');
    }

    try {
      const content = await fs.readFile(resolvedPath, 'utf-8');
      const stats = await fs.stat(resolvedPath);
      
      return [{
        uri,
        mimeType: this.getMimeType(resolvedPath),
        text: content,
        size: stats.size
      }];
    } catch (error) {
      throw new Error(`Failed to access resource: ${error.message}`);
    }
  }

  // Helper methods
  isPathAllowed(targetPath) {
    return this.allowedPaths.some(allowedPath => 
      targetPath.startsWith(path.resolve(allowedPath))
    );
  }

  async searchInDirectory(dir, pattern) {
    const results = [];
    const entries = await fs.readdir(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      
      if (entry.isFile() && this.matchesPattern(entry.name, pattern)) {
        const stats = await fs.stat(fullPath);
        results.push({
          path: fullPath,
          name: entry.name,
          size: stats.size,
          modified: stats.mtime.toISOString()
        });
      } else if (entry.isDirectory()) {
        // Recursive search
        const subResults = await this.searchInDirectory(fullPath, pattern);
        results.push(...subResults);
      }
    }
    
    return results;
  }

  matchesPattern(filename, pattern) {
    // Simple pattern matching (could be enhanced with proper glob)
    const regex = new RegExp(pattern.replace(/\*/g, '.*'), 'i');
    return regex.test(filename);
  }

  getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
      '.txt': 'text/plain',
      '.js': 'application/javascript',
      '.json': 'application/json',
      '.html': 'text/html',
      '.css': 'text/css',
      '.md': 'text/markdown'
    };
    return mimeTypes[ext] || 'text/plain';
  }
}

// Simulated MCP Service Runner
class MCPServiceRunner {
  constructor(serviceClass) {
    this.service = new serviceClass();
    this.tools = Reflect.getMetadata(TOOL_METADATA, serviceClass) || [];
    this.resources = Reflect.getMetadata(RESOURCE_METADATA, serviceClass) || [];
    this.config = Reflect.getMetadata('service:config', serviceClass) || {};
  }

  async start() {
    console.log(`📡 Service: ${this.config.name} v${this.config.version}`);
    console.log(`📝 Description: ${this.config.description}`);
    console.log(`🔧 Tools: ${this.tools.map(t => t.name).join(', ')}`);
    console.log(`📁 Resources: ${this.resources.length} patterns`);
    
    // Demo the service
    await this.demo();
  }

  async demo() {
    console.log('\n🧪 Running demo...');
    
    try {
      // Demo 1: List current directory
      console.log('\n📂 Demo 1: List current directory');
      const listResult = await this.callTool('list_directory', { path: '.' });
      console.log(`   Found ${listResult.count} items`);
      listResult.items.slice(0, 3).forEach(item => {
        console.log(`   ${item.type === 'directory' ? '📁' : '📄'} ${item.name}`);
      });

      // Demo 2: Search for JavaScript files
      console.log('\n🔍 Demo 2: Search for JavaScript files');
      const searchResult = await this.callTool('search_files', { pattern: '*.js' });
      console.log(`   Found ${searchResult.count} JavaScript files`);
      searchResult.results.slice(0, 3).forEach(file => {
        console.log(`   📄 ${file.name} (${file.size} bytes)`);
      });

      // Demo 3: Read package.json if exists
      console.log('\n📖 Demo 3: Read package.json');
      try {
        const readResult = await this.callTool('read_file', { path: 'package.json' });
        const pkg = JSON.parse(readResult.content);
        console.log(`   📦 Package: ${pkg.name} v${pkg.version}`);
      } catch (error) {
        console.log(`   ⚠️  No package.json found`);
      }

      console.log('\n✅ Demo completed successfully!');
    } catch (error) {
      console.error('❌ Demo failed:', error.message);
    }
  }

  async callTool(name, input) {
    const tool = this.tools.find(t => t.name === name);
    if (!tool) {
      throw new Error(`Tool not found: ${name}`);
    }
    return await tool.handler.call(this.service, input);
  }
}

// Start the service
const runner = new MCPServiceRunner(FileOpsService);
await runner.start();
