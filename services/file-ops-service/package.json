{"name": "file-ops-mcp-service", "version": "1.0.0", "description": "MCP service for file operations using decorators", "type": "module", "main": "dist/index.js", "scripts": {"start": "node src/index.js", "dev": "node src/index.js", "test": "node test.js"}, "keywords": ["mcp", "file-operations", "decorators"], "author": "MCP Framework", "license": "MIT", "dependencies": {"reflect-metadata": "^0.1.13"}}