name: file-ops-service
version: 1.0.0
description: File operations service using decorators
transport: [stdio]

tools:
  - name: read_file
    description: Read contents of a file
    inputSchema:
      type: object
      properties:
        path:
          type: string
          description: File path to read
      required: [path]
    handler: ./src/handlers/readFile.js

  - name: write_file
    description: Write content to a file
    inputSchema:
      type: object
      properties:
        path:
          type: string
          description: File path to write
        content:
          type: string
          description: Content to write
      required: [path, content]
    handler: ./src/handlers/writeFile.js

  - name: list_directory
    description: List contents of a directory
    inputSchema:
      type: object
      properties:
        path:
          type: string
          description: Directory path to list
      required: [path]
    handler: ./src/handlers/listDirectory.js

  - name: search_files
    description: Search for files by name pattern
    inputSchema:
      type: object
      properties:
        pattern:
          type: string
          description: Search pattern (glob-like)
        directory:
          type: string
          description: Directory to search in
          default: "."
      required: [pattern]
    handler: ./src/handlers/searchFiles.js

resources:
  - pattern: "file://**"
    name: File System
    description: Access to local file system
    handler: ./src/handlers/fileResource.js

build:
  outDir: dist
  target: node18
  format: esm

dev:
  debug: true
  watch: [src/**/*]
