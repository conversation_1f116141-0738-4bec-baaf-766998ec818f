{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "allowImportingTsExtensions": false, "resolveJsonModule": true, "isolatedModules": false, "noEmit": false, "strict": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "declaration": true, "outDir": "dist", "baseUrl": ".", "paths": {"@mcp-framework/core": ["./packages/core/src"]}}, "include": ["packages/core/src/**/*"], "exclude": ["node_modules", "dist", "packages/cli"]}