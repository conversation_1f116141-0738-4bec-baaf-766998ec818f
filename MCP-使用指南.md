# 🚀 MCP框架使用指南 - 让AI帮你干活！

## 🤔 MCP到底是什么？

**最简单的理解**：MCP让Claude这样的AI助手能够调用你电脑上的工具，帮你做各种事情。

### 📱 生活化比喻
- **你** = 老板
- **Claude** = 助手
- **MCP服务** = 助手可以使用的工具箱
- **MCP协议** = 你和助手沟通的语言

## 🎯 实际能做什么？

### 场景1：文件管理助手
```
你对Claude说："帮我读一下项目的README文件"
Claude通过MCP调用文件工具 → 读取文件 → 告诉你内容

你说："帮我在docs文件夹创建一个API文档"
Claude通过MCP调用写文件工具 → 创建文件 → 完成任务
```

### 场景2：网页信息助手
```
你说："帮我查一下GitHub上这个项目的最新版本"
Claude通过MCP调用网页工具 → 访问GitHub → 获取信息 → 告诉你结果

你说："帮我分析这个API的返回格式"
Claude通过MCP调用HTTP工具 → 请求API → 分析结果 → 给你报告
```

## ✅ 你现在有什么可以用的？

### 1. 🗂️ 真正可用的文件服务
**位置**: `real-mcp-example/`
**功能**: 让Claude帮你读写文件、管理目录

```bash
# 测试一下
cd real-mcp-example
node test-mcp.js

# 你会看到：
# 🎉 MCP服务完全正常！
# 📋 如何与Claude Desktop集成: (有详细步骤)
```

### 2. 📦 完整的开发框架
**位置**: `packages/`
**用途**: 如果你想开发自己的MCP服务

### 3. 🎯 三个示例服务
**位置**: `services/`
**用途**: 学习不同的开发模式

## 🚀 立即上手 - 让Claude帮你管理文件

### 第一步：确认服务正常
```bash
cd /Users/<USER>/Desktop/mcp_agent/real-mcp-example
node test-mcp.js
```

看到 `🎉 MCP服务完全正常！` 就OK了。

### 第二步：配置Claude Desktop

1. **打开Claude Desktop**
2. **进入设置** (Settings)
3. **找到MCP配置** (通常在Developer或Advanced选项中)
4. **添加配置**：
```json
{
  "mcpServers": {
    "file-manager": {
      "command": "node",
      "args": ["/Users/<USER>/Desktop/mcp_agent/real-mcp-example/simple-file-server.js"]
    }
  }
}
```

### 第三步：重启Claude Desktop

### 第四步：测试功能
对Claude说：
- "帮我列出当前目录的文件"
- "帮我读取package.json文件"
- "帮我创建一个hello.txt文件，内容是'Hello MCP!'"

## 📊 JSON-RPC通信示例

### 客户端请求 (Claude → 你的服务)
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "read_file",
    "arguments": {
      "path": "README.md"
    }
  }
}
```

### 服务端响应 (你的服务 → Claude)
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": {
    "content": [
      {
        "type": "text",
        "text": "文件内容 (README.md):\n\n# 我的项目\n这是项目说明..."
      }
    ]
  }
}
```

## 🛠️ 如果你想开发自己的MCP服务

### 最简单的方式
```javascript
#!/usr/bin/env node

// 1. 定义你的工具
const tools = [
  {
    name: "my_tool",
    description: "我的自定义工具",
    inputSchema: {
      type: "object",
      properties: {
        input: { type: "string", description: "输入参数" }
      },
      required: ["input"]
    }
  }
];

// 2. 实现工具逻辑
async function handleToolCall(toolName, args) {
  if (toolName === "my_tool") {
    return `你输入了: ${args.input}`;
  }
  throw new Error(`未知工具: ${toolName}`);
}

// 3. 处理JSON-RPC消息
process.stdin.on('data', async (data) => {
  const request = JSON.parse(data.toString());
  
  let result;
  if (request.method === "tools/list") {
    result = { tools };
  } else if (request.method === "tools/call") {
    const output = await handleToolCall(request.params.name, request.params.arguments);
    result = { content: [{ type: "text", text: output }] };
  }
  
  console.log(JSON.stringify({
    jsonrpc: "2.0",
    id: request.id,
    result
  }));
});
```

## 🎯 常见问题

### Q: 为什么Claude看不到我的工具？
A: 检查：
1. MCP服务是否正常启动
2. Claude Desktop配置是否正确
3. 文件路径是否正确
4. 是否重启了Claude Desktop

### Q: 如何调试MCP服务？
A: 
1. 运行 `node test-mcp.js` 测试基本功能
2. 查看服务的stderr输出（日志）
3. 检查JSON-RPC消息格式

### Q: 可以创建什么类型的工具？
A: 任何你能用代码实现的功能：
- 文件操作
- 网络请求
- 数据库查询
- 系统命令
- API调用
- 数据处理

## 🎉 总结

**你现在拥有的**：
- ✅ 一个完全可用的MCP文件服务
- ✅ 完整的开发框架
- ✅ 详细的使用文档
- ✅ 真实的JSON-RPC通信示例

**下一步**：
1. 先让文件服务跑起来
2. 配置Claude Desktop
3. 体验AI助手帮你管理文件
4. 根据需要开发更多工具

**MCP的威力**：让AI不再只是聊天工具，而是真正能帮你干活的智能助手！🚀
