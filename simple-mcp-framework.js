#!/usr/bin/env node

/**
 * 简单实用的MCP框架
 * 
 * 真正的价值：
 * 1. 5行代码创建MCP服务 vs 200行手写
 * 2. 自动处理JSON-RPC协议细节
 * 3. 内置错误处理和验证
 * 4. 支持热重载开发
 */

import fs from 'fs/promises';
import { spawn } from 'child_process';

// 核心MCP框架类
export class SimpleMCP {
  constructor(name, version = '1.0.0') {
    this.name = name;
    this.version = version;
    this.tools = new Map();
    this.resources = new Map();
  }

  // 添加工具 - 超简单API
  tool(name, description, handler, schema = {}) {
    this.tools.set(name, {
      name,
      description,
      handler,
      inputSchema: {
        type: 'object',
        properties: schema,
        required: Object.keys(schema).filter(key => schema[key].required)
      }
    });
    return this;
  }

  // 添加资源
  resource(uri, name, description, handler, mimeType = 'text/plain') {
    this.resources.set(uri, {
      uri, name, description, handler, mimeType
    });
    return this;
  }

  // 启动服务 - 自动处理所有协议细节
  start() {
    console.error(`🚀 ${this.name} v${this.version} 已启动`);
    console.error(`🔧 工具: ${Array.from(this.tools.keys()).join(', ')}`);
    
    process.stdin.setEncoding('utf8');
    let buffer = '';
    
    process.stdin.on('data', async (chunk) => {
      buffer += chunk;
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.trim()) {
          const response = await this.handleRequest(line.trim());
          if (response) console.log(JSON.stringify(response));
        }
      }
    });
  }

  // 自动处理所有MCP协议
  async handleRequest(message) {
    try {
      const request = JSON.parse(message);
      let result;

      switch (request.method) {
        case 'initialize':
          result = {
            protocolVersion: '2024-11-05',
            capabilities: { tools: {}, resources: {} },
            serverInfo: { name: this.name, version: this.version }
          };
          break;

        case 'notifications/initialized':
          return null;

        case 'tools/list':
          result = {
            tools: Array.from(this.tools.values()).map(t => ({
              name: t.name,
              description: t.description,
              inputSchema: t.inputSchema
            }))
          };
          break;

        case 'tools/call':
          const tool = this.tools.get(request.params.name);
          if (!tool) throw new Error(`工具不存在: ${request.params.name}`);
          
          const output = await tool.handler(request.params.arguments || {});
          result = {
            content: [{
              type: 'text',
              text: typeof output === 'string' ? output : JSON.stringify(output, null, 2)
            }]
          };
          break;

        case 'resources/list':
          result = {
            resources: Array.from(this.resources.values()).map(r => ({
              uri: r.uri,
              name: r.name,
              description: r.description,
              mimeType: r.mimeType
            }))
          };
          break;

        case 'resources/read':
          const resource = this.resources.get(request.params.uri);
          if (!resource) throw new Error(`资源不存在: ${request.params.uri}`);
          
          const content = await resource.handler(request.params.uri);
          result = {
            contents: [{
              uri: request.params.uri,
              mimeType: resource.mimeType,
              text: content
            }]
          };
          break;

        default:
          throw new Error(`未知方法: ${request.method}`);
      }

      return { jsonrpc: '2.0', id: request.id, result };
    } catch (error) {
      return {
        jsonrpc: '2.0',
        id: request?.id || null,
        error: { code: -32603, message: error.message }
      };
    }
  }
}

// 开发工具：热重载
export function dev(scriptPath) {
  console.log(`🔥 开发模式: ${scriptPath}`);
  
  let child;
  
  const restart = () => {
    if (child) child.kill();
    child = spawn('node', [scriptPath], { stdio: 'inherit' });
  };
  
  // 监听文件变化
  fs.watch(scriptPath, restart);
  restart();
  
  process.on('SIGINT', () => {
    if (child) child.kill();
    process.exit(0);
  });
}

// 快速创建工具
export const createMCP = (name, version) => new SimpleMCP(name, version);

// 常用工具模板
export const templates = {
  fileOps: {
    read_file: {
      description: '读取文件',
      schema: { path: { type: 'string', required: true } },
      handler: async ({ path }) => {
        const content = await fs.readFile(path, 'utf-8');
        return `文件内容 (${path}):\n\n${content}`;
      }
    },
    
    write_file: {
      description: '写入文件',
      schema: { 
        path: { type: 'string', required: true },
        content: { type: 'string', required: true }
      },
      handler: async ({ path, content }) => {
        await fs.writeFile(path, content, 'utf-8');
        return `文件已写入: ${path}`;
      }
    },
    
    list_directory: {
      description: '列出目录',
      schema: { path: { type: 'string', default: '.' } },
      handler: async ({ path = '.' }) => {
        const items = await fs.readdir(path, { withFileTypes: true });
        const list = items.map(item => 
          `${item.isDirectory() ? '📁' : '📄'} ${item.name}`
        );
        return `目录内容 (${path}):\n${list.join('\n')}`;
      }
    }
  }
};

// CLI入口
if (import.meta.url === `file://${process.argv[1]}`) {
  const command = process.argv[2];
  const arg = process.argv[3];
  
  if (command === 'dev' && arg) {
    dev(arg);
  } else {
    console.log(`
🚀 SimpleMCP 框架

用法:
  node simple-mcp-framework.js dev <script.js>  # 热重载开发

示例:
  import { createMCP, templates } from './simple-mcp-framework.js';
  
  const service = createMCP('my-service')
    .tool('read_file', ...templates.fileOps.read_file)
    .tool('write_file', ...templates.fileOps.write_file)
    .start();
`);
  }
}
