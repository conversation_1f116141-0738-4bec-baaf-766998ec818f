{"root": true, "env": {"node": true, "es2022": true}, "extends": ["eslint:recommended", "@typescript-eslint/recommended", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2022, "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint", "import", "json"], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "warn", "import/order": ["error", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index"], "newlines-between": "always"}]}, "ignorePatterns": ["dist", "build", "coverage", "node_modules", "*.config.js", "*.config.ts"]}