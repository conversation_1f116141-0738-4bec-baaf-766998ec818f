<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏢 完整MCP管理平台</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8fafc; }
        
        .header { background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%); color: white; padding: 15px 0; }
        .header-content { max-width: 1400px; margin: 0 auto; padding: 0 20px; display: flex; justify-content: space-between; align-items: center; }
        .logo { font-size: 1.5em; font-weight: bold; }
        
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .main-layout { display: grid; grid-template-columns: 250px 1fr; gap: 20px; }
        
        .sidebar { background: white; border-radius: 12px; padding: 20px; height: fit-content; }
        .nav-item { padding: 12px 16px; margin: 5px 0; border-radius: 8px; cursor: pointer; transition: all 0.3s; display: flex; align-items: center; gap: 10px; }
        .nav-item:hover { background: #f1f5f9; }
        .nav-item.active { background: #3b82f6; color: white; }
        
        .main-content { background: white; border-radius: 12px; padding: 30px; }
        .page-title { font-size: 1.8em; margin-bottom: 20px; color: #1e293b; }
        
        .stats-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #f8fafc; padding: 20px; border-radius: 12px; text-align: center; border-left: 4px solid #3b82f6; }
        .stat-number { font-size: 2em; font-weight: bold; color: #1e293b; }
        .stat-label { color: #64748b; margin-top: 5px; }
        
        .services-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; }
        .service-card { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px; transition: all 0.3s; }
        .service-card:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.1); }
        .service-card.running { border-left: 4px solid #10b981; }
        .service-card.stopped { border-left: 4px solid #ef4444; }
        
        .service-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .service-title { font-size: 1.2em; font-weight: 600; }
        .service-status { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; }
        .service-status.running { background: #dcfce7; color: #166534; }
        .service-status.stopped { background: #fef2f2; color: #dc2626; }
        
        .btn { padding: 8px 16px; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s; margin: 2px; }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn-secondary { background: #6b7280; color: white; }
        .btn:hover { opacity: 0.8; }
        
        .permissions-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .permissions-table th, .permissions-table td { padding: 12px; text-align: left; border-bottom: 1px solid #e2e8f0; }
        .permissions-table th { background: #f8fafc; font-weight: 600; }
        
        .auth-token { background: #1f2937; color: #f9fafb; padding: 8px 12px; border-radius: 6px; font-family: monospace; font-size: 12px; }
        
        .log-panel { background: #1a1a1a; color: #10b981; padding: 20px; border-radius: 8px; font-family: monospace; font-size: 12px; height: 400px; overflow-y: auto; }
        
        .config-panel { background: #1f2937; color: #f9fafb; padding: 20px; border-radius: 8px; font-family: monospace; font-size: 14px; }
        
        .hidden { display: none; }
        
        .notification { position: fixed; top: 20px; right: 20px; padding: 15px 20px; border-radius: 8px; color: white; font-weight: 600; z-index: 1000; transform: translateX(400px); transition: transform 0.3s; }
        .notification.show { transform: translateX(0); }
        .notification.success { background: #10b981; }
        .notification.error { background: #ef4444; }
        .notification.info { background: #3b82f6; }
        
        .api-converter-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .converter-card { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px; }
        
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; }
        .form-group input, .form-group select, .form-group textarea { width: 100%; padding: 10px; border: 1px solid #d1d5db; border-radius: 6px; }
        
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; }
        .modal-content { background: white; margin: 5% auto; padding: 30px; border-radius: 12px; width: 90%; max-width: 600px; }
        .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .modal-title { font-size: 1.3em; font-weight: 600; }
        .close { font-size: 24px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏢 完整MCP管理平台</div>
            <div>管理员 | 在线</div>
        </div>
    </div>

    <div class="container">
        <div class="main-layout">
            <div class="sidebar">
                <div class="nav-item active" data-page="dashboard">
                    <span>📊</span> 仪表板
                </div>
                <div class="nav-item" data-page="services">
                    <span>🛠️</span> 服务管理
                </div>
                <div class="nav-item" data-page="permissions">
                    <span>🔐</span> 权限管理
                </div>
                <div class="nav-item" data-page="logs">
                    <span>📝</span> 调用日志
                </div>
                <div class="nav-item" data-page="api-converter">
                    <span>🔄</span> API转换
                </div>
                <div class="nav-item" data-page="network">
                    <span>🌐</span> 网络配置
                </div>
                <div class="nav-item" data-page="settings">
                    <span>⚙️</span> 系统设置
                </div>
            </div>

            <div class="main-content">
                <!-- 仪表板 -->
                <div id="dashboard" class="page">
                    <h2 class="page-title">📊 系统仪表板</h2>
                    
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number" id="runningCount">0</div>
                            <div class="stat-label">运行中服务</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="totalCount">6</div>
                            <div class="stat-label">总服务数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="toolsCount">0</div>
                            <div class="stat-label">可用工具</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="callsCount">0</div>
                            <div class="stat-label">今日调用</div>
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <button class="btn btn-success" onclick="startAllServices()">🚀 启动所有服务</button>
                        <button class="btn btn-danger" onclick="stopAllServices()">⏹️ 停止所有服务</button>
                        <button class="btn btn-primary" onclick="refreshStats()">🔄 刷新状态</button>
                        <button class="btn btn-secondary" onclick="exportConfig()">📤 导出配置</button>
                    </div>
                </div>

                <!-- 服务管理 -->
                <div id="services" class="page hidden">
                    <h2 class="page-title">🛠️ MCP服务管理</h2>
                    
                    <div style="margin-bottom: 20px;">
                        <button class="btn btn-primary" onclick="showAddServiceModal()">➕ 添加服务</button>
                        <button class="btn btn-secondary" onclick="importServices()">📥 导入服务</button>
                    </div>
                    
                    <div class="services-grid" id="servicesGrid"></div>
                </div>

                <!-- 权限管理 -->
                <div id="permissions" class="page hidden">
                    <h2 class="page-title">🔐 权限管理</h2>
                    
                    <div style="margin-bottom: 20px;">
                        <button class="btn btn-primary" onclick="showAddUserModal()">👤 添加用户</button>
                        <button class="btn btn-secondary" onclick="generateAuthToken()">🔑 生成令牌</button>
                    </div>
                    
                    <table class="permissions-table">
                        <thead>
                            <tr>
                                <th>用户</th>
                                <th>授权令牌</th>
                                <th>可用服务</th>
                                <th>权限级别</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><EMAIL></td>
                                <td><span class="auth-token">mcp_admin_***</span></td>
                                <td>全部服务</td>
                                <td>管理员</td>
                                <td><span class="service-status running">活跃</span></td>
                                <td>
                                    <button class="btn btn-secondary">编辑</button>
                                    <button class="btn btn-danger">禁用</button>
                                </td>
                            </tr>
                            <tr>
                                <td><EMAIL></td>
                                <td><span class="auth-token">mcp_user_***</span></td>
                                <td>文件操作, Web工具</td>
                                <td>普通用户</td>
                                <td><span class="service-status running">活跃</span></td>
                                <td>
                                    <button class="btn btn-secondary">编辑</button>
                                    <button class="btn btn-danger">禁用</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 调用日志 -->
                <div id="logs" class="page hidden">
                    <h2 class="page-title">📝 MCP调用日志</h2>
                    
                    <div style="margin-bottom: 20px; display: flex; gap: 10px; align-items: center;">
                        <select id="logFilter">
                            <option value="all">全部日志</option>
                            <option value="success">成功调用</option>
                            <option value="error">错误日志</option>
                            <option value="auth">认证日志</option>
                        </select>
                        <input type="date" id="logDate" style="padding: 8px;">
                        <button class="btn btn-primary" onclick="filterLogs()">🔍 筛选</button>
                        <button class="btn btn-secondary" onclick="clearLogs()">🗑️ 清空</button>
                        <button class="btn btn-secondary" onclick="exportLogs()">📤 导出</button>
                    </div>
                    
                    <div class="log-panel" id="logPanel"></div>
                </div>

                <!-- API转换 -->
                <div id="api-converter" class="page hidden">
                    <h2 class="page-title">🔄 API转MCP转换器</h2>
                    
                    <div style="margin-bottom: 20px;">
                        <button class="btn btn-primary" onclick="showApiConverterModal()">➕ 添加API转换</button>
                        <button class="btn btn-secondary" onclick="testApiConversion()">🧪 测试转换</button>
                    </div>
                    
                    <div class="api-converter-grid">
                        <div class="converter-card">
                            <div class="service-header">
                                <div class="service-title">天气API转换</div>
                                <div class="service-status running">运行中</div>
                            </div>
                            <div style="color: #64748b; margin-bottom: 15px;">
                                将OpenWeatherMap API转换为MCP工具
                            </div>
                            <div style="font-size: 12px; color: #64748b; margin-bottom: 15px;">
                                <div>API端点: https://api.openweathermap.org/data/2.5/weather</div>
                                <div>MCP工具: weather__get_current</div>
                            </div>
                            <div>
                                <button class="btn btn-secondary">编辑</button>
                                <button class="btn btn-danger">删除</button>
                                <button class="btn btn-primary">测试</button>
                            </div>
                        </div>
                        
                        <div class="converter-card">
                            <div class="service-header">
                                <div class="service-title">翻译API转换</div>
                                <div class="service-status running">运行中</div>
                            </div>
                            <div style="color: #64748b; margin-bottom: 15px;">
                                将Google Translate API转换为MCP工具
                            </div>
                            <div style="font-size: 12px; color: #64748b; margin-bottom: 15px;">
                                <div>API端点: https://translate.googleapis.com/translate_a/single</div>
                                <div>MCP工具: translate__text</div>
                            </div>
                            <div>
                                <button class="btn btn-secondary">编辑</button>
                                <button class="btn btn-danger">删除</button>
                                <button class="btn btn-primary">测试</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 网络配置 -->
                <div id="network" class="page hidden">
                    <h2 class="page-title">🌐 网络传输配置</h2>
                    
                    <div style="margin-bottom: 30px;">
                        <h3 style="margin-bottom: 15px;">传输协议支持</h3>
                        <div style="display: flex; gap: 15px;">
                            <div style="padding: 15px; border: 2px solid #10b981; border-radius: 8px; background: #f0fff4;">
                                <strong>📡 stdio</strong><br>
                                <small>标准输入输出 - Claude Desktop</small>
                            </div>
                            <div style="padding: 15px; border: 2px solid #10b981; border-radius: 8px; background: #f0fff4;">
                                <strong>🌊 SSE</strong><br>
                                <small>服务器推送事件 - 实时Web</small>
                            </div>
                            <div style="padding: 15px; border: 2px solid #10b981; border-radius: 8px; background: #f0fff4;">
                                <strong>🔄 HTTP</strong><br>
                                <small>HTTP流式传输 - REST API</small>
                            </div>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <h3 style="margin-bottom: 15px;">统一MCP配置</h3>
                            <div class="config-panel" id="unifiedConfig"></div>
                            <button class="btn btn-primary" onclick="copyUnifiedConfig()" style="margin-top: 15px;">📋 复制配置</button>
                        </div>
                        
                        <div>
                            <h3 style="margin-bottom: 15px;">网络端点</h3>
                            <div style="background: #f8fafc; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 12px;">
                                <div><strong>HTTP:</strong> http://localhost:9091/mcp</div>
                                <div><strong>SSE:</strong> http://localhost:9091/sse</div>
                                <div><strong>管理API:</strong> http://localhost:8081/api</div>
                                <div><strong>Web界面:</strong> http://localhost:3001</div>
                            </div>
                            
                            <h4 style="margin: 20px 0 10px 0;">认证示例</h4>
                            <div class="config-panel">
curl -H "Authorization: Bearer mcp_token_123" \<br>
&nbsp;&nbsp;&nbsp;&nbsp;http://localhost:9091/mcp
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统设置 -->
                <div id="settings" class="page hidden">
                    <h2 class="page-title">⚙️ 系统设置</h2>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                        <div>
                            <h3 style="margin-bottom: 15px;">🔐 认证信息</h3>
                            <div style="background: #f8fafc; padding: 20px; border-radius: 8px;">
                                <p><strong>管理员令牌:</strong></p>
                                <div class="auth-token" style="margin: 10px 0;">mcp_admin_0205a6d19866acf1c5afd6fc464c8d50</div>
                                
                                <p style="margin-top: 20px;"><strong>用户令牌:</strong></p>
                                <div class="auth-token" style="margin: 10px 0;">mcp_user_b5297672c1a1dd6858bd3115cab4af45</div>
                                
                                <button class="btn btn-primary" style="margin-top: 15px;" onclick="generateNewToken()">🔑 生成新令牌</button>
                            </div>
                        </div>
                        
                        <div>
                            <h3 style="margin-bottom: 15px;">🌐 服务端点</h3>
                            <div style="background: #f8fafc; padding: 20px; border-radius: 8px;">
                                <p><strong>管理界面:</strong> http://localhost:3001</p>
                                <p><strong>MCP HTTP:</strong> http://localhost:9091/mcp</p>
                                <p><strong>MCP SSE:</strong> http://localhost:9091/sse</p>
                                <p><strong>管理API:</strong> http://localhost:8081/api</p>
                                
                                <button class="btn btn-secondary" style="margin-top: 15px;" onclick="testAllEndpoints()">🧪 测试所有端点</button>
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 30px;">
                        <h3 style="margin-bottom: 15px;">📊 系统状态</h3>
                        <div style="background: #f8fafc; padding: 20px; border-radius: 8px;">
                            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; text-align: center;">
                                <div>
                                    <div style="font-size: 1.5em; font-weight: bold; color: #10b981;">正常</div>
                                    <div style="color: #64748b;">系统状态</div>
                                </div>
                                <div>
                                    <div style="font-size: 1.5em; font-weight: bold; color: #10b981;" id="uptimeDisplay">0分钟</div>
                                    <div style="color: #64748b;">运行时间</div>
                                </div>
                                <div>
                                    <div style="font-size: 1.5em; font-weight: bold; color: #3b82f6;">128MB</div>
                                    <div style="color: #64748b;">内存使用</div>
                                </div>
                                <div>
                                    <div style="font-size: 1.5em; font-weight: bold; color: #3b82f6;">v1.0.0</div>
                                    <div style="color: #64748b;">平台版本</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="addServiceModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">➕ 添加MCP服务</h3>
                <span class="close" onclick="closeModal('addServiceModal')">&times;</span>
            </div>
            <div class="form-group">
                <label>服务名称</label>
                <input type="text" id="serviceName" placeholder="my-service">
            </div>
            <div class="form-group">
                <label>服务描述</label>
                <textarea id="serviceDescription" placeholder="服务功能描述"></textarea>
            </div>
            <div class="form-group">
                <label>启动命令</label>
                <input type="text" id="serviceCommand" placeholder="node my-service.js">
            </div>
            <div style="text-align: right;">
                <button class="btn btn-secondary" onclick="closeModal('addServiceModal')">取消</button>
                <button class="btn btn-primary" onclick="addService()">添加服务</button>
            </div>
        </div>
    </div>

    <div id="notification" class="notification"></div>

    <script src="complete-mcp-platform.js"></script>
</body>
</html>
