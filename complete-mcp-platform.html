<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏢 完整MCP管理平台</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8fafc; }
        
        .header { background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%); color: white; padding: 15px 0; }
        .header-content { max-width: 1400px; margin: 0 auto; padding: 0 20px; display: flex; justify-content: space-between; align-items: center; }
        .logo { font-size: 1.5em; font-weight: bold; }
        
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .main-layout { display: grid; grid-template-columns: 250px 1fr; gap: 20px; }
        
        .sidebar { background: white; border-radius: 12px; padding: 20px; height: fit-content; }
        .nav-item { padding: 12px 16px; margin: 5px 0; border-radius: 8px; cursor: pointer; transition: all 0.3s; display: flex; align-items: center; gap: 10px; }
        .nav-item:hover { background: #f1f5f9; }
        .nav-item.active { background: #3b82f6; color: white; }
        
        .main-content { background: white; border-radius: 12px; padding: 30px; }
        .page-title { font-size: 1.8em; margin-bottom: 20px; color: #1e293b; }
        
        .stats-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #f8fafc; padding: 20px; border-radius: 12px; text-align: center; border-left: 4px solid #3b82f6; }
        .stat-number { font-size: 2em; font-weight: bold; color: #1e293b; }
        .stat-label { color: #64748b; margin-top: 5px; }
        
        .services-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; }
        .service-card { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px; transition: all 0.3s; }
        .service-card:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.1); }
        .service-card.running { border-left: 4px solid #10b981; }
        .service-card.stopped { border-left: 4px solid #ef4444; }
        
        .service-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .service-title { font-size: 1.2em; font-weight: 600; }
        .service-status { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; }
        .service-status.running { background: #dcfce7; color: #166534; }
        .service-status.stopped { background: #fef2f2; color: #dc2626; }
        
        .btn { padding: 8px 16px; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s; margin: 2px; }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn-secondary { background: #6b7280; color: white; }
        .btn:hover { opacity: 0.8; }
        
        .permissions-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .permissions-table th, .permissions-table td { padding: 12px; text-align: left; border-bottom: 1px solid #e2e8f0; }
        .permissions-table th { background: #f8fafc; font-weight: 600; }
        
        .auth-token { background: #1f2937; color: #f9fafb; padding: 8px 12px; border-radius: 6px; font-family: monospace; font-size: 12px; }
        
        .log-panel { background: #1a1a1a; color: #10b981; padding: 20px; border-radius: 8px; font-family: monospace; font-size: 12px; height: 400px; overflow-y: auto; }
        
        .config-panel { background: #1f2937; color: #f9fafb; padding: 20px; border-radius: 8px; font-family: monospace; font-size: 14px; }
        
        .hidden { display: none; }
        
        .notification { position: fixed; top: 20px; right: 20px; padding: 15px 20px; border-radius: 8px; color: white; font-weight: 600; z-index: 1000; transform: translateX(400px); transition: transform 0.3s; }
        .notification.show { transform: translateX(0); }
        .notification.success { background: #10b981; }
        .notification.error { background: #ef4444; }
        .notification.info { background: #3b82f6; }
        
        .api-converter-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .converter-card { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px; }
        
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; }
        .form-group input, .form-group select, .form-group textarea { width: 100%; padding: 10px; border: 1px solid #d1d5db; border-radius: 6px; }
        
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; }
        .modal-content { background: white; margin: 5% auto; padding: 30px; border-radius: 12px; width: 90%; max-width: 600px; }
        .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .modal-title { font-size: 1.3em; font-weight: 600; }
        .close { font-size: 24px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏢 完整MCP管理平台</div>
            <div>管理员 | 在线</div>
        </div>
    </div>

    <div class="container">
        <div class="main-layout">
            <div class="sidebar">
                <div class="nav-item active" data-page="dashboard">
                    <span>📊</span> 仪表板
                </div>
                <div class="nav-item" data-page="services">
                    <span>🛠️</span> 服务管理
                </div>
                <div class="nav-item" data-page="permissions">
                    <span>🔐</span> 权限管理
                </div>
                <div class="nav-item" data-page="logs">
                    <span>📝</span> 调用日志
                </div>
                <div class="nav-item" data-page="api-converter">
                    <span>🔄</span> API转换
                </div>
                <div class="nav-item" data-page="network">
                    <span>🌐</span> 网络配置
                </div>
                <div class="nav-item" data-page="settings">
                    <span>⚙️</span> 系统设置
                </div>
            </div>

            <div class="main-content">
                <!-- 仪表板 -->
                <div id="dashboard" class="page">
                    <h2 class="page-title">📊 系统仪表板</h2>
                    
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number" id="runningCount">0</div>
                            <div class="stat-label">运行中服务</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="totalCount">6</div>
                            <div class="stat-label">总服务数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="toolsCount">0</div>
                            <div class="stat-label">可用工具</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="callsCount">0</div>
                            <div class="stat-label">今日调用</div>
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <button class="btn btn-success" onclick="startAllServices()">🚀 启动所有服务</button>
                        <button class="btn btn-danger" onclick="stopAllServices()">⏹️ 停止所有服务</button>
                        <button class="btn btn-primary" onclick="refreshStats()">🔄 刷新状态</button>
                        <button class="btn btn-secondary" onclick="reloadRealData()">📡 重载真实数据</button>
                        <button class="btn btn-secondary" onclick="exportConfig()">📤 导出配置</button>
                    </div>
                </div>

                <!-- 服务管理 -->
                <div id="services" class="page hidden">
                    <h2 class="page-title">🛠️ MCP服务管理</h2>
                    
                    <div style="margin-bottom: 20px;">
                        <button class="btn btn-primary" onclick="showAddServiceModal()">➕ 添加服务</button>
                        <button class="btn btn-secondary" onclick="importServices()">📥 导入服务</button>
                    </div>
                    
                    <div class="services-grid" id="servicesGrid"></div>
                </div>

                <!-- 权限管理 -->
                <div id="permissions" class="page hidden">
                    <h2 class="page-title">🔐 权限管理</h2>
                    
                    <div style="margin-bottom: 20px;">
                        <button class="btn btn-primary" onclick="showAddUserModal()">👤 添加用户</button>
                        <button class="btn btn-secondary" onclick="generateAuthToken()">🔑 生成令牌</button>
                    </div>
                    
                    <table class="permissions-table">
                        <thead>
                            <tr>
                                <th>用户</th>
                                <th>授权令牌</th>
                                <th>可用服务</th>
                                <th>权限级别</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><EMAIL></td>
                                <td><span class="auth-token">mcp_admin_***</span></td>
                                <td>全部服务</td>
                                <td>管理员</td>
                                <td><span class="service-status running">活跃</span></td>
                                <td>
                                    <button class="btn btn-secondary">编辑</button>
                                    <button class="btn btn-danger">禁用</button>
                                </td>
                            </tr>
                            <tr>
                                <td><EMAIL></td>
                                <td><span class="auth-token">mcp_user_***</span></td>
                                <td>文件操作, Web工具</td>
                                <td>普通用户</td>
                                <td><span class="service-status running">活跃</span></td>
                                <td>
                                    <button class="btn btn-secondary">编辑</button>
                                    <button class="btn btn-danger">禁用</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 调用日志 -->
                <div id="logs" class="page hidden">
                    <h2 class="page-title">📝 MCP调用日志</h2>
                    
                    <div style="margin-bottom: 20px; display: flex; gap: 10px; align-items: center;">
                        <select id="logFilter">
                            <option value="all">全部日志</option>
                            <option value="success">成功调用</option>
                            <option value="error">错误日志</option>
                            <option value="auth">认证日志</option>
                        </select>
                        <input type="date" id="logDate" style="padding: 8px;">
                        <button class="btn btn-primary" onclick="filterLogs()">🔍 筛选</button>
                        <button class="btn btn-secondary" onclick="clearLogs()">🗑️ 清空</button>
                        <button class="btn btn-secondary" onclick="exportLogs()">📤 导出</button>
                    </div>
                    
                    <div class="log-panel" id="logPanel"></div>
                </div>

                <!-- API转换 -->
                <div id="api-converter" class="page hidden">
                    <h2 class="page-title">🔄 API转MCP转换器</h2>
                    
                    <div style="margin-bottom: 20px;">
                        <button class="btn btn-primary" onclick="showApiConverterModal()">➕ 添加API转换</button>
                        <button class="btn btn-secondary" onclick="testApiConversion()">🧪 测试转换</button>
                    </div>
                    
                    <div class="api-converter-grid">
                        <div class="converter-card">
                            <div class="service-header">
                                <div class="service-title">天气API转换</div>
                                <div class="service-status running">运行中</div>
                            </div>
                            <div style="color: #64748b; margin-bottom: 15px;">
                                将OpenWeatherMap API转换为MCP工具
                            </div>
                            <div style="font-size: 12px; color: #64748b; margin-bottom: 15px;">
                                <div>API端点: https://api.openweathermap.org/data/2.5/weather</div>
                                <div>MCP工具: weather__get_current</div>
                            </div>
                            <div>
                                <button class="btn btn-secondary">编辑</button>
                                <button class="btn btn-danger">删除</button>
                                <button class="btn btn-primary">测试</button>
                            </div>
                        </div>
                        
                        <div class="converter-card">
                            <div class="service-header">
                                <div class="service-title">翻译API转换</div>
                                <div class="service-status running">运行中</div>
                            </div>
                            <div style="color: #64748b; margin-bottom: 15px;">
                                将Google Translate API转换为MCP工具
                            </div>
                            <div style="font-size: 12px; color: #64748b; margin-bottom: 15px;">
                                <div>API端点: https://translate.googleapis.com/translate_a/single</div>
                                <div>MCP工具: translate__text</div>
                            </div>
                            <div>
                                <button class="btn btn-secondary">编辑</button>
                                <button class="btn btn-danger">删除</button>
                                <button class="btn btn-primary">测试</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 网络配置 -->
                <div id="network" class="page hidden">
                    <h2 class="page-title">🌐 网络传输配置</h2>
                    
                    <div style="margin-bottom: 30px;">
                        <h3 style="margin-bottom: 15px;">传输协议支持</h3>
                        <div style="display: flex; gap: 15px;">
                            <div style="padding: 15px; border: 2px solid #10b981; border-radius: 8px; background: #f0fff4;">
                                <strong>📡 stdio</strong><br>
                                <small>标准输入输出 - Claude Desktop</small>
                            </div>
                            <div style="padding: 15px; border: 2px solid #10b981; border-radius: 8px; background: #f0fff4;">
                                <strong>🌊 SSE</strong><br>
                                <small>服务器推送事件 - 实时Web</small>
                            </div>
                            <div style="padding: 15px; border: 2px solid #10b981; border-radius: 8px; background: #f0fff4;">
                                <strong>🔄 HTTP</strong><br>
                                <small>HTTP流式传输 - REST API</small>
                            </div>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <h3 style="margin-bottom: 15px;">统一MCP配置</h3>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">选择传输协议:</label>
                                <select id="transportSelect" onchange="updateMCPConfig()" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 200px;">
                                    <option value="stdio">stdio - Claude Desktop</option>
                                    <option value="sse">SSE - 实时Web服务</option>
                                    <option value="http">HTTP - REST API</option>
                                </select>
                            </div>
                            <div class="config-panel" id="unifiedConfig"></div>
                            <button class="btn btn-primary" onclick="copyUnifiedConfig()" style="margin-top: 15px;">📋 复制配置</button>
                        </div>
                        
                        <div>
                            <h3 style="margin-bottom: 15px;">网络端点</h3>
                            <div style="background: #f8fafc; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 12px;">
                                <div><strong>HTTP:</strong> http://localhost:9091/mcp</div>
                                <div><strong>SSE:</strong> http://localhost:9091/sse</div>
                                <div><strong>管理API:</strong> http://localhost:8081/api</div>
                                <div><strong>Web界面:</strong> http://localhost:3001</div>
                            </div>
                            
                            <h4 style="margin: 20px 0 10px 0;">认证示例</h4>
                            <div class="config-panel">
curl -H "Authorization: Bearer mcp_token_123" \<br>
&nbsp;&nbsp;&nbsp;&nbsp;http://localhost:9091/mcp
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统设置 -->
                <div id="settings" class="page hidden">
                    <h2 class="page-title">⚙️ 系统设置</h2>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                        <div>
                            <h3 style="margin-bottom: 15px;">🔐 认证信息</h3>
                            <div style="background: #f8fafc; padding: 20px; border-radius: 8px;">
                                <p><strong>管理员令牌:</strong></p>
                                <div class="auth-token" style="margin: 10px 0;">mcp_admin_0205a6d19866acf1c5afd6fc464c8d50</div>
                                
                                <p style="margin-top: 20px;"><strong>用户令牌:</strong></p>
                                <div class="auth-token" style="margin: 10px 0;">mcp_user_b5297672c1a1dd6858bd3115cab4af45</div>
                                
                                <button class="btn btn-primary" style="margin-top: 15px;" onclick="generateNewToken()">🔑 生成新令牌</button>
                            </div>
                        </div>
                        
                        <div>
                            <h3 style="margin-bottom: 15px;">🌐 服务端点</h3>
                            <div style="background: #f8fafc; padding: 20px; border-radius: 8px;">
                                <p><strong>管理界面:</strong> http://localhost:3001</p>
                                <p><strong>MCP HTTP:</strong> http://localhost:9091/mcp</p>
                                <p><strong>MCP SSE:</strong> http://localhost:9091/sse</p>
                                <p><strong>管理API:</strong> http://localhost:8081/api</p>
                                
                                <button class="btn btn-secondary" style="margin-top: 15px;" onclick="testAllEndpoints()">🧪 测试所有端点</button>
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 30px;">
                        <h3 style="margin-bottom: 15px;">📊 系统状态</h3>
                        <div style="background: #f8fafc; padding: 20px; border-radius: 8px;">
                            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; text-align: center;">
                                <div>
                                    <div style="font-size: 1.5em; font-weight: bold; color: #10b981;">正常</div>
                                    <div style="color: #64748b;">系统状态</div>
                                </div>
                                <div>
                                    <div style="font-size: 1.5em; font-weight: bold; color: #10b981;" id="uptimeDisplay">0分钟</div>
                                    <div style="color: #64748b;">运行时间</div>
                                </div>
                                <div>
                                    <div style="font-size: 1.5em; font-weight: bold; color: #3b82f6;">128MB</div>
                                    <div style="color: #64748b;">内存使用</div>
                                </div>
                                <div>
                                    <div style="font-size: 1.5em; font-weight: bold; color: #3b82f6;">v1.0.0</div>
                                    <div style="color: #64748b;">平台版本</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="addServiceModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">➕ 添加MCP服务</h3>
                <span class="close" onclick="closeModal('addServiceModal')">&times;</span>
            </div>
            <div class="form-group">
                <label>服务名称</label>
                <input type="text" id="serviceName" placeholder="my-service">
            </div>
            <div class="form-group">
                <label>服务描述</label>
                <textarea id="serviceDescription" placeholder="服务功能描述"></textarea>
            </div>
            <div class="form-group">
                <label>启动命令</label>
                <input type="text" id="serviceCommand" placeholder="node my-service.js">
            </div>
            <div style="text-align: right;">
                <button class="btn btn-secondary" onclick="closeModal('addServiceModal')">取消</button>
                <button class="btn btn-primary" onclick="addService()">添加服务</button>
            </div>
        </div>
    </div>

    <div id="notification" class="notification"></div>

    <script>
        // 内嵌JavaScript避免加载问题
        /**
         * 🏢 完整MCP管理平台 JavaScript
         */

        // 全局状态
        const state = {
            services: new Map(),
            logs: [],
            currentPage: 'dashboard',
            startTime: new Date(),
            apiConverters: []
        };

        // 从真实API加载服务数据
        async function loadRealServices() {
            try {
                const response = await fetch('/api/services', {
                    headers: {
                        'Authorization': 'Bearer admin_token_placeholder'
                    }
                });

                if (response.ok) {
                    const realServices = await response.json();

                    // 清空现有服务
                    state.services.clear();

                    // 添加真实服务数据
                    realServices.forEach(service => {
                        const serviceData = {
                            id: service.id,
                            name: service.name,
                            status: service.status,
                            tools: service.tools || [],
                            description: getServiceDescription(service.id)
                        };
                        state.services.set(service.id, serviceData);
                    });

                    addLog(`📦 加载了 ${realServices.length} 个真实服务`);
                    return true;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                addLog(`❌ 无法加载真实服务数据: ${error.message}`);
                return false;
            }
        }

        // 获取服务描述
        function getServiceDescription(serviceId) {
            const descriptions = {
                'universal': '提供文件操作、Web工具、系统工具等基础功能',
                'database': '提供数据库操作功能',
                'email': '提供邮件发送和接收功能',
                'password': '提供密码生成和管理功能',
                'analytics': '提供数据分析和统计功能',
                'weather': '提供天气查询和预报功能'
            };
            return descriptions[serviceId] || '未知服务';
        }

        // 获取服务工具列表
        function getServiceToolsList(serviceId) {
            const toolsMap = {
                'universal': [
                    'read_file - 读取文件内容',
                    'write_file - 写入文件内容',
                    'http_request - 发送HTTP请求',
                    'get_system_info - 获取系统信息'
                ],
                'database': [
                    'create_table - 创建数据表',
                    'insert_data - 插入数据',
                    'query_data - 查询数据'
                ],
                'email': [
                    'send_email - 发送邮件',
                    'check_inbox - 检查收件箱'
                ],
                'password': [
                    'generate_password - 生成密码',
                    'store_password - 存储密码',
                    'get_password - 获取密码'
                ],
                'analytics': [
                    'analyze_csv - 分析CSV数据',
                    'calculate_stats - 计算统计数据'
                ],
                'weather': [
                    'get_weather - 获取当前天气',
                    'get_forecast - 获取天气预报'
                ]
            };

            const tools = toolsMap[serviceId] || ['未知工具'];
            return tools.map(tool => `• ${tool}`).join('<br>');
        }

        // 页面切换
        function showPage(pageId) {
            console.log('切换到页面:', pageId);

            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.add('hidden');
            });

            // 移除所有导航项的active类
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // 显示当前页面
            const currentPage = document.getElementById(pageId);
            if (currentPage) {
                currentPage.classList.remove('hidden');
            }

            // 激活当前导航项
            const currentNav = document.querySelector(`[data-page="${pageId}"]`);
            if (currentNav) {
                currentNav.classList.add('active');
            }

            state.currentPage = pageId;

            // 页面特定初始化
            switch(pageId) {
                case 'dashboard':
                    updateDashboard();
                    break;
                case 'services':
                    renderServices();
                    break;
                case 'logs':
                    updateLogs();
                    break;
                case 'permissions':
                    renderPermissions();
                    break;
                case 'network':
                    updateNetworkConfig();
                    break;
                case 'api-converter':
                    renderApiConverters();
                    break;
                case 'settings':
                    updateSettings();
                    break;
            }
        }

        // 服务管理
        function renderServices() {
            console.log('渲染服务列表');
            const grid = document.getElementById('servicesGrid');
            if (!grid) return;

            const services = Array.from(state.services.values());

            grid.innerHTML = services.map(service => `
                <div class="service-card ${service.status}">
                    <div class="service-header">
                        <div class="service-title">${service.name}</div>
                        <div class="service-status ${service.status}">
                            ${service.status === 'running' ? '运行中' : '已停止'}
                        </div>
                    </div>
                    <div style="color: #64748b; margin-bottom: 15px;">
                        ${service.description}
                    </div>
                    <div style="color: #64748b; margin-bottom: 15px; font-size: 12px;">
                        <div><strong>工具数量:</strong> ${Array.isArray(service.tools) ? service.tools.length : service.tools || 0}</div>
                        <div style="margin-top: 5px;"><strong>具体工具:</strong></div>
                        <div style="margin-left: 10px; font-size: 11px;">
                            ${getServiceToolsList(service.id)}
                        </div>
                    </div>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        ${service.status === 'running' ?
                            `<button class="btn btn-danger" onclick="stopService('${service.id}'); event.stopPropagation();">⏹️ 停止</button>` :
                            `<button class="btn btn-success" onclick="startService('${service.id}'); event.stopPropagation();">🚀 启动</button>`
                        }
                        <button class="btn btn-secondary" onclick="configService('${service.id}'); event.stopPropagation();">⚙️ 配置</button>
                        <button class="btn btn-primary" onclick="testService('${service.id}'); event.stopPropagation();">🧪 测试</button>
                        <button class="btn btn-secondary" onclick="viewServiceLogs('${service.id}'); event.stopPropagation();">📝 日志</button>
                    </div>
                </div>
            `).join('');

            updateDashboard();
        }

        // 服务操作 - 真实API调用
        async function startService(serviceId) {
            const service = state.services.get(serviceId);
            if (!service) return;

            showNotification(`正在启动 ${service.name}...`, 'info');

            try {
                const response = await fetch(`/api/services/${serviceId}/start`, {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer admin_token_placeholder',
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    service.status = 'running';
                    state.services.set(serviceId, service);

                    addLog(`✅ 服务启动成功: ${service.name}`);
                    showNotification(`${service.name} 启动成功`, 'success');

                    if (state.currentPage === 'services') {
                        renderServices();
                    }
                    updateDashboard();
                } else {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
            } catch (error) {
                addLog(`❌ 服务启动失败: ${service.name} - ${error.message}`);
                showNotification(`${service.name} 启动失败: ${error.message}`, 'error');
            }
        }

        async function stopService(serviceId) {
            const service = state.services.get(serviceId);
            if (!service) return;

            showNotification(`正在停止 ${service.name}...`, 'info');

            try {
                const response = await fetch(`/api/services/${serviceId}/stop`, {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer admin_token_placeholder',
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    service.status = 'stopped';
                    state.services.set(serviceId, service);

                    addLog(`⏹️ 服务停止: ${service.name}`);
                    showNotification(`${service.name} 已停止`, 'success');

                    if (state.currentPage === 'services') {
                        renderServices();
                    }
                    updateDashboard();
                } else {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
            } catch (error) {
                addLog(`❌ 服务停止失败: ${service.name} - ${error.message}`);
                showNotification(`${service.name} 停止失败: ${error.message}`, 'error');
            }
        }

        function startAllServices() {
            console.log('启动所有服务');
            const services = Array.from(state.services.keys());
            let index = 0;

            function startNext() {
                if (index < services.length) {
                    const serviceId = services[index];
                    startService(serviceId);
                    index++;
                    setTimeout(startNext, 2000); // 延迟2秒启动下一个
                }
            }

            startNext();
        }

        function stopAllServices() {
            console.log('停止所有服务');
            const services = Array.from(state.services.keys());
            let index = 0;

            function stopNext() {
                if (index < services.length) {
                    const serviceId = services[index];
                    stopService(serviceId);
                    index++;
                    setTimeout(stopNext, 1500); // 延迟1.5秒停止下一个
                }
            }

            stopNext();
        }

        // 统计更新 - 真实数据
        async function updateDashboard() {
            try {
                // 获取真实服务状态
                const response = await fetch('/api/services', {
                    headers: {
                        'Authorization': 'Bearer admin_token_placeholder'
                    }
                });

                if (response.ok) {
                    const realServices = await response.json();

                    // 更新本地状态
                    realServices.forEach(service => {
                        if (state.services.has(service.id)) {
                            const localService = state.services.get(service.id);
                            localService.status = service.status;
                            state.services.set(service.id, localService);
                        }
                    });
                }
            } catch (error) {
                console.log('无法获取真实服务状态:', error.message);
            }

            // 计算统计数据
            const services = Array.from(state.services.values());
            const running = services.filter(s => s.status === 'running').length;
            const totalTools = services.reduce((sum, s) => {
                const toolCount = Array.isArray(s.tools) ? s.tools.length : (s.tools || 0);
                return sum + toolCount;
            }, 0);

            const runningEl = document.getElementById('runningCount');
            const totalEl = document.getElementById('totalCount');
            const toolsEl = document.getElementById('toolsCount');
            const callsEl = document.getElementById('callsCount');

            if (runningEl) runningEl.textContent = running;
            if (totalEl) totalEl.textContent = services.length;
            if (toolsEl) toolsEl.textContent = totalTools;

            // 获取真实调用统计数据
            try {
                const statsResponse = await fetch('/api/stats', {
                    headers: {
                        'Authorization': 'Bearer admin_token_placeholder'
                    }
                });

                if (statsResponse.ok) {
                    const stats = await statsResponse.json();
                    if (callsEl) callsEl.textContent = stats.calls?.today || 0;
                } else {
                    if (callsEl) callsEl.textContent = '离线';
                }
            } catch (error) {
                if (callsEl) callsEl.textContent = '离线';
            }

            console.log(`仪表板更新: 运行中${running}, 总计${services.length}, 工具${totalTools}`);
        }

        async function refreshStats() {
            await updateDashboard();
            showNotification('统计数据已刷新', 'success');
        }

        async function reloadRealData() {
            showNotification('正在重新加载真实数据...', 'info');
            addLog('🔄 开始重新加载真实数据');

            const servicesLoaded = await loadRealServices();
            if (servicesLoaded) {
                await updateDashboard();
                if (state.currentPage === 'services') {
                    renderServices();
                }
                showNotification('真实数据重载成功', 'success');
                addLog('✅ 真实数据重载完成');
            } else {
                showNotification('真实数据重载失败', 'error');
                addLog('❌ 真实数据重载失败');
            }
        }

        // 其他功能
        function configService(serviceId) {
            showNotification(`配置服务: ${serviceId}`, 'info');
            addLog(`⚙️ 配置服务: ${serviceId}`);
        }

        function testService(serviceId) {
            showNotification(`测试服务: ${serviceId}`, 'info');
            addLog(`🧪 测试服务: ${serviceId}`);
        }

        function viewServiceLogs(serviceId) {
            showPage('logs');
            showNotification(`查看服务日志: ${serviceId}`, 'info');
        }

        function renderPermissions() {
            console.log('权限管理页面已加载');
        }

        function renderApiConverters() {
            const content = document.getElementById('content');
            content.innerHTML = `
                <div class="page-header">
                    <h2>🔄 API转MCP转换器</h2>
                    <p>将任意REST API转换为MCP工具调用</p>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div class="card">
                        <h3>➕ 添加API转换</h3>
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">API名称:</label>
                            <input type="text" id="apiName" placeholder="例如: weather_api" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">API URL:</label>
                            <input type="text" id="apiUrl" placeholder="https://api.openweathermap.org/data/2.5/weather" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">HTTP方法:</label>
                            <select id="apiMethod" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                <option value="GET">GET</option>
                                <option value="POST">POST</option>
                                <option value="PUT">PUT</option>
                                <option value="DELETE">DELETE</option>
                            </select>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">请求头 (JSON格式):</label>
                            <textarea id="apiHeaders" placeholder='{"Authorization": "Bearer YOUR_TOKEN", "Content-Type": "application/json"}' style="width: 100%; height: 80px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-family: monospace;"></textarea>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">参数配置 (JSON格式):</label>
                            <textarea id="apiParams" placeholder='{"q": {"type": "string", "description": "城市名称"}, "appid": {"type": "string", "description": "API密钥"}}' style="width: 100%; height: 100px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-family: monospace;"></textarea>
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <button class="btn btn-primary" onclick="addApiConverter()">➕ 添加转换器</button>
                            <button class="btn btn-secondary" onclick="testApiConverter()">🧪 测试API</button>
                        </div>
                    </div>

                    <div class="card">
                        <h3>📋 已转换的API</h3>
                        <div id="apiConvertersList" style="max-height: 400px; overflow-y: auto;">
                            ${renderApiConvertersList()}
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h3>🔧 生成的MCP工具代码</h3>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">选择API:</label>
                        <select id="selectedApiConverter" onchange="generateMCPCode()" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 300px;">
                            <option value="">请选择API转换器</option>
                            ${getApiConverterOptions()}
                        </select>
                    </div>
                    <pre id="generatedMCPCode" style="background: #1e293b; color: #e2e8f0; padding: 15px; border-radius: 8px; font-size: 12px; overflow-x: auto; min-height: 200px;">请先添加API转换器</pre>
                    <button class="btn btn-primary" onclick="copyMCPCode()" style="margin-top: 15px;">📋 复制代码</button>
                </div>
            `;
        }

        function updateNetworkConfig() {
            // 初始化传输选择器
            const transportSelect = document.getElementById('transportSelect');
            if (transportSelect && !transportSelect.value) {
                transportSelect.value = 'stdio';
            }

            // 更新配置
            updateMCPConfig();

            // 更新网络端点显示
            const networkEndpoints = document.getElementById('networkEndpoints');
            if (networkEndpoints) {
                networkEndpoints.innerHTML = `
                    <div><strong>HTTP:</strong> http://localhost:9091/mcp</div>
                    <div><strong>SSE:</strong> http://localhost:9091/sse</div>
                    <div><strong>管理API:</strong> http://localhost:8081/api</div>
                    <div><strong>Web界面:</strong> http://localhost:3001</div>
                `;
            }

            // 更新认证示例
            const authExample = document.getElementById('authExample');
            if (authExample) {
                authExample.innerHTML = `curl -H "Authorization: Bearer mcp_token_123" \\
     http://localhost:9091/mcp`;
            }
        }

        // 更新MCP配置
        function updateMCPConfig() {
            const transportSelect = document.getElementById('transportSelect');
            const transport = transportSelect ? transportSelect.value : 'stdio';
            let config = {};

            switch(transport) {
                case 'stdio':
                    config = {
                        "mcpServers": {
                            "enterprise-mcp": {
                                "command": "node",
                                "args": ["enterprise-mcp-server.js"],
                                "env": {
                                    "MCP_AUTH_TOKEN": "your-auth-token",
                                    "MCP_TRANSPORT": "stdio"
                                }
                            }
                        }
                    };
                    break;
                case 'sse':
                    config = {
                        "mcpServers": {
                            "enterprise-mcp": {
                                "command": "node",
                                "args": ["enterprise-mcp-server.js"],
                                "env": {
                                    "MCP_AUTH_TOKEN": "your-auth-token",
                                    "MCP_TRANSPORT": "sse",
                                    "MCP_SSE_URL": "http://localhost:9091/sse"
                                }
                            }
                        }
                    };
                    break;
                case 'http':
                    config = {
                        "mcpServers": {
                            "enterprise-mcp": {
                                "command": "node",
                                "args": ["enterprise-mcp-server.js"],
                                "env": {
                                    "MCP_AUTH_TOKEN": "your-auth-token",
                                    "MCP_TRANSPORT": "http",
                                    "MCP_HTTP_URL": "http://localhost:9091/mcp"
                                }
                            }
                        }
                    };
                    break;
            }

            const configEl = document.getElementById('unifiedConfig');
            if (configEl) {
                configEl.innerHTML = `
                    <pre style="background: #1e293b; color: #e2e8f0; padding: 15px; border-radius: 8px; font-size: 12px; overflow-x: auto; min-height: 150px;">${JSON.stringify(config, null, 2)}</pre>
                `;
            }
        }

        function updateSettings() {
            const uptimeEl = document.getElementById('uptimeDisplay');
            if (uptimeEl) {
                const uptime = Math.floor((new Date() - state.startTime) / 1000 / 60);
                uptimeEl.textContent = `${uptime}分钟`;
            }
        }

        // API转换器功能
        function addApiConverter() {
            const name = document.getElementById('apiName').value.trim();
            const url = document.getElementById('apiUrl').value.trim();
            const method = document.getElementById('apiMethod').value;
            const headersText = document.getElementById('apiHeaders').value.trim();
            const paramsText = document.getElementById('apiParams').value.trim();

            if (!name || !url) {
                alert('请填写API名称和URL');
                return;
            }

            let headers = {};
            let params = {};

            try {
                if (headersText) {
                    headers = JSON.parse(headersText);
                }
                if (paramsText) {
                    params = JSON.parse(paramsText);
                }
            } catch (error) {
                alert('JSON格式错误，请检查请求头和参数配置');
                return;
            }

            const converter = {
                id: Date.now().toString(),
                name,
                url,
                method,
                headers,
                params,
                createdAt: new Date().toISOString()
            };

            if (!state.apiConverters) {
                state.apiConverters = [];
            }

            state.apiConverters.push(converter);

            // 清空表单
            document.getElementById('apiName').value = '';
            document.getElementById('apiUrl').value = '';
            document.getElementById('apiHeaders').value = '';
            document.getElementById('apiParams').value = '';

            // 刷新列表
            document.getElementById('apiConvertersList').innerHTML = renderApiConvertersList();

            // 更新选择器
            const selector = document.getElementById('selectedApiConverter');
            if (selector) {
                selector.innerHTML = '<option value="">请选择API转换器</option>' + getApiConverterOptions();
            }

            addLog(`✅ API转换器 "${name}" 添加成功`);
        }

        function renderApiConvertersList() {
            if (!state.apiConverters || state.apiConverters.length === 0) {
                return '<div style="color: #64748b; text-align: center; padding: 20px;">暂无API转换器</div>';
            }

            return state.apiConverters.map(converter => `
                <div style="border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; margin-bottom: 10px;">
                    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 10px;">
                        <h4 style="margin: 0; color: #1e293b;">${converter.name}</h4>
                        <button class="btn btn-danger" onclick="deleteApiConverter('${converter.id}')" style="padding: 4px 8px; font-size: 12px;">删除</button>
                    </div>
                    <div style="font-size: 12px; color: #64748b;">
                        <div><strong>URL:</strong> ${converter.method} ${converter.url}</div>
                        <div><strong>参数:</strong> ${Object.keys(converter.params).length}个</div>
                        <div><strong>创建时间:</strong> ${new Date(converter.createdAt).toLocaleString()}</div>
                    </div>
                </div>
            `).join('');
        }

        function getApiConverterOptions() {
            if (!state.apiConverters || state.apiConverters.length === 0) {
                return '';
            }

            return state.apiConverters.map(converter =>
                `<option value="${converter.id}">${converter.name}</option>`
            ).join('');
        }

        function deleteApiConverter(id) {
            if (!confirm('确定要删除这个API转换器吗？')) {
                return;
            }

            state.apiConverters = state.apiConverters.filter(c => c.id !== id);

            // 刷新列表
            document.getElementById('apiConvertersList').innerHTML = renderApiConvertersList();

            // 更新选择器
            const selector = document.getElementById('selectedApiConverter');
            if (selector) {
                selector.innerHTML = '<option value="">请选择API转换器</option>' + getApiConverterOptions();
            }

            // 清空代码显示
            document.getElementById('generatedMCPCode').textContent = '请先添加API转换器';

            addLog(`🗑️ API转换器已删除`);
        }

        function generateMCPCode() {
            const selectedId = document.getElementById('selectedApiConverter').value;
            if (!selectedId) {
                document.getElementById('generatedMCPCode').textContent = '请先选择API转换器';
                return;
            }

            const converter = state.apiConverters.find(c => c.id === selectedId);
            if (!converter) {
                document.getElementById('generatedMCPCode').textContent = '找不到选中的API转换器';
                return;
            }

            const mcpCode = generateMCPToolCode(converter);
            document.getElementById('generatedMCPCode').textContent = mcpCode;
        }

        function generateMCPToolCode(converter) {
            const paramsSchema = Object.entries(converter.params).map(([key, config]) => {
                return `        ${key}: {
            type: "${config.type || 'string'}",
            description: "${config.description || ''}"
        }`;
            }).join(',\n');

            const paramsExtraction = Object.keys(converter.params).map(key =>
                `        const ${key} = args.${key};`
            ).join('\n');

            const requestBody = converter.method === 'GET' ?
                'null' :
                `JSON.stringify({${Object.keys(converter.params).map(key => `${key}`).join(', ')}})`;

            const headersCode = JSON.stringify(converter.headers, null, 8).replace(/^/gm, '        ');

            return `// MCP工具: ${converter.name}
// 自动生成于: ${new Date().toLocaleString()}

import { MCPService } from '@mcp-framework/core';

const service = new MCPService({
    name: '${converter.name}_service',
    version: '1.0.0'
});

// ${converter.name} 工具
service.addTool({
    name: '${converter.name}',
    description: '调用 ${converter.url} API',
    inputSchema: {
        type: 'object',
        properties: {
${paramsSchema}
        },
        required: [${Object.keys(converter.params).map(k => `'${k}'`).join(', ')}]
    }
}, async (args) => {
    try {
${paramsExtraction}

        const response = await fetch('${converter.url}', {
            method: '${converter.method}',
            headers: ${headersCode},
            ${converter.method !== 'GET' ? `body: ${requestBody}` : ''}
        });

        if (!response.ok) {
            throw new Error(\`API请求失败: \${response.status} \${response.statusText}\`);
        }

        const data = await response.json();

        return {
            content: [{
                type: 'text',
                text: JSON.stringify(data, null, 2)
            }]
        };
    } catch (error) {
        return {
            content: [{
                type: 'text',
                text: \`错误: \${error.message}\`
            }],
            isError: true
        };
    }
});

// 启动服务
service.start().then(() => {
    console.log('${converter.name} MCP服务已启动');
}).catch(console.error);`;
        }

        function testApiConverter() {
            const url = document.getElementById('apiUrl').value.trim();
            const method = document.getElementById('apiMethod').value;
            const headersText = document.getElementById('apiHeaders').value.trim();

            if (!url) {
                alert('请填写API URL');
                return;
            }

            let headers = {};
            try {
                if (headersText) {
                    headers = JSON.parse(headersText);
                }
            } catch (error) {
                alert('请求头JSON格式错误');
                return;
            }

            // 显示测试中状态
            const testBtn = event.target;
            const originalText = testBtn.textContent;
            testBtn.textContent = '测试中...';
            testBtn.disabled = true;

            fetch(url, {
                method: method,
                headers: headers
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(\`HTTP \${response.status}: \${response.statusText}\`);
                }
                return response.json();
            })
            .then(data => {
                alert('API测试成功！\\n\\n响应数据：\\n' + JSON.stringify(data, null, 2).substring(0, 500));
                addLog(\`✅ API测试成功: \${url}\`);
            })
            .catch(error => {
                alert('API测试失败：\\n' + error.message);
                addLog(\`❌ API测试失败: \${url} - \${error.message}\`);
            })
            .finally(() => {
                testBtn.textContent = originalText;
                testBtn.disabled = false;
            });
        }

        function copyMCPCode() {
            const code = document.getElementById('generatedMCPCode').textContent;
            if (code === '请先添加API转换器' || code === '请先选择API转换器') {
                alert('没有可复制的代码');
                return;
            }

            navigator.clipboard.writeText(code).then(() => {
                alert('MCP代码已复制到剪贴板');
                addLog('📋 MCP代码已复制');
            }).catch(() => {
                alert('复制失败，请手动复制');
            });
        }

        // 复制统一配置
        function copyUnifiedConfig() {
            const configEl = document.getElementById('unifiedConfig');
            if (!configEl) {
                alert('配置不存在');
                return;
            }

            const configText = configEl.textContent || configEl.innerText;
            if (!configText || configText.trim() === '') {
                alert('没有可复制的配置');
                return;
            }

            navigator.clipboard.writeText(configText).then(() => {
                alert('MCP配置已复制到剪贴板');
                addLog('📋 MCP配置已复制');
            }).catch(() => {
                alert('复制失败，请手动复制');
            });
        }

        // 日志管理
        function addLog(message) {
            const timestamp = new Date().toLocaleString();
            const logEntry = `[${timestamp}] ${message}`;
            state.logs.push(logEntry);

            if (state.currentPage === 'logs') {
                updateLogs();
            }
        }

        function updateLogs() {
            const logPanel = document.getElementById('logPanel');
            if (logPanel) {
                logPanel.innerHTML = state.logs.slice(-50).join('<br>') || '暂无日志';
                logPanel.scrollTop = logPanel.scrollHeight;
            }
        }

        function filterLogs() {
            const filter = document.getElementById('logFilter').value;
            const date = document.getElementById('logDate').value;
            showNotification(`日志筛选: ${filter} ${date}`, 'info');
            updateLogs();
        }

        function clearLogs() {
            state.logs = [];
            updateLogs();
            showNotification('日志已清空', 'success');
        }

        function exportLogs() {
            const logs = state.logs.join('\n');
            const blob = new Blob([logs], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'mcp-platform-logs.txt';
            a.click();
            URL.revokeObjectURL(url);
            showNotification('日志已导出', 'success');
        }

        // 模态框和其他功能
        function showAddServiceModal() {
            document.getElementById('addServiceModal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function addService() {
            const name = document.getElementById('serviceName').value;
            const description = document.getElementById('serviceDescription').value;
            const command = document.getElementById('serviceCommand').value;

            if (!name || !command) {
                showNotification('请填写必要信息', 'error');
                return;
            }

            showNotification(`服务 ${name} 添加成功`, 'success');
            addLog(`📦 添加服务: ${name}`);
            closeModal('addServiceModal');
            renderServices();
        }

        function exportConfig() {
            const config = {
                services: Array.from(state.services.values()),
                logs: state.logs.slice(-100)
            };

            const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'mcp-platform-config.json';
            a.click();
            URL.revokeObjectURL(url);

            showNotification('配置已导出', 'success');
        }

        function copyUnifiedConfig() {
            const config = document.getElementById('unifiedConfig').textContent;
            navigator.clipboard.writeText(config).then(() => {
                showNotification('配置已复制到剪贴板', 'success');
            });
        }

        function generateNewToken() {
            const token = 'mcp_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
            showNotification(`新令牌已生成: ${token}`, 'success');
            addLog(`🔑 生成新令牌: ${token.substring(0, 12)}***`);
        }

        function testAllEndpoints() {
            showNotification('正在测试所有端点...', 'info');
            addLog('🧪 开始测试所有端点');

            setTimeout(() => {
                showNotification('所有端点测试完成', 'success');
                addLog('✅ 所有端点测试完成');
            }, 2000);
        }

        // 通知系统
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type} show`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('完整MCP平台初始化开始');

            // 添加导航点击事件 - 修复菜单点击问题
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', (e) => {
                    e.preventDefault();
                    const pageId = item.getAttribute('data-page');
                    if (pageId) {
                        showPage(pageId);
                    }
                });
            });

            // 初始化日志
            addLog('🚀 完整MCP平台启动完成');
            addLog('📦 正在连接后端服务...');

            // 检查后端连接状态并加载真实数据
            try {
                const response = await fetch('/api/stats', {
                    headers: {
                        'Authorization': 'Bearer admin_token_placeholder'
                    }
                });

                if (response.ok) {
                    addLog('✅ 后端服务连接成功');
                    addLog('📊 正在加载真实服务数据...');

                    // 加载真实服务数据
                    const servicesLoaded = await loadRealServices();
                    if (servicesLoaded) {
                        addLog('✅ 真实服务数据加载成功');
                    } else {
                        addLog('⚠️ 服务数据加载失败');
                    }
                } else {
                    addLog('⚠️ 后端服务连接失败，无法加载真实数据');
                }
            } catch (error) {
                addLog('⚠️ 后端服务不可用，无法加载真实数据');
            }

            // 初始化数据
            await updateDashboard();
            updateNetworkConfig();
            addLog('🎛️ 管理界面就绪');

            // 显示默认页面
            showPage('dashboard');

            // 定期刷新状态 (每30秒)
            setInterval(async () => {
                if (state.currentPage === 'dashboard') {
                    await updateDashboard();
                }
            }, 30000);

            // 模态框关闭事件
            document.addEventListener('click', (e) => {
                if (e.target.classList.contains('modal')) {
                    e.target.style.display = 'none';
                }
            });

            console.log('完整MCP平台初始化完成');
        });
    </script>
</body>
</html>
