<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 MCP平台测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px; }
        .service-card { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 4px; }
        .running { border-left: 4px solid #28a745; }
        .stopped { border-left: 4px solid #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 MCP平台功能测试</h1>
        
        <div>
            <h3>📊 仪表板测试</h3>
            <button class="btn btn-primary" onclick="testDashboard()">测试仪表板</button>
            <div id="dashboardResult" class="result"></div>
        </div>

        <div>
            <h3>🛠️ 服务管理测试</h3>
            <button class="btn btn-success" onclick="startTestService()">启动测试服务</button>
            <button class="btn btn-danger" onclick="stopTestService()">停止测试服务</button>
            <button class="btn btn-primary" onclick="listServices()">列出所有服务</button>
            <div id="servicesResult" class="result"></div>
        </div>

        <div>
            <h3>🔐 权限测试</h3>
            <button class="btn btn-primary" onclick="testAuth()">测试认证</button>
            <div id="authResult" class="result"></div>
        </div>

        <div>
            <h3>📝 日志测试</h3>
            <button class="btn btn-primary" onclick="addTestLog()">添加测试日志</button>
            <button class="btn btn-danger" onclick="clearTestLogs()">清空日志</button>
            <div id="logsResult" class="result"></div>
        </div>

        <div>
            <h3>🌐 MCP API测试</h3>
            <button class="btn btn-primary" onclick="testMcpApi()">测试MCP API</button>
            <div id="mcpResult" class="result"></div>
        </div>
    </div>

    <script>
        // 模拟服务状态
        const services = {
            'universal': { name: '通用MCP服务', status: 'stopped' },
            'database': { name: '数据库服务', status: 'stopped' },
            'email': { name: '邮件服务', status: 'stopped' }
        };

        function testDashboard() {
            const running = Object.values(services).filter(s => s.status === 'running').length;
            const total = Object.keys(services).length;
            
            document.getElementById('dashboardResult').innerHTML = `
                <strong>仪表板数据:</strong><br>
                运行中服务: ${running}<br>
                总服务数: ${total}<br>
                状态: ✅ 正常
            `;
        }

        function startTestService() {
            services.universal.status = 'running';
            updateServicesDisplay();
            log('✅ 启动服务: 通用MCP服务');
        }

        function stopTestService() {
            services.universal.status = 'stopped';
            updateServicesDisplay();
            log('⏹️ 停止服务: 通用MCP服务');
        }

        function listServices() {
            updateServicesDisplay();
        }

        function updateServicesDisplay() {
            const html = Object.entries(services).map(([id, service]) => `
                <div class="service-card ${service.status}">
                    <strong>${service.name}</strong> - 
                    <span style="color: ${service.status === 'running' ? 'green' : 'red'}">
                        ${service.status === 'running' ? '运行中' : '已停止'}
                    </span>
                </div>
            `).join('');
            
            document.getElementById('servicesResult').innerHTML = html;
        }

        function testAuth() {
            const token = 'mcp_admin_test123';
            document.getElementById('authResult').innerHTML = `
                <strong>认证测试:</strong><br>
                令牌: ${token}<br>
                状态: ✅ 认证成功<br>
                权限: 管理员
            `;
        }

        const logs = [];
        function log(message) {
            const timestamp = new Date().toLocaleString();
            logs.push(`[${timestamp}] ${message}`);
            updateLogsDisplay();
        }

        function addTestLog() {
            log('🧪 这是一条测试日志');
        }

        function clearTestLogs() {
            logs.length = 0;
            updateLogsDisplay();
        }

        function updateLogsDisplay() {
            document.getElementById('logsResult').innerHTML = 
                logs.slice(-5).join('<br>') || '暂无日志';
        }

        async function testMcpApi() {
            try {
                const response = await fetch('http://localhost:9090/api/status');
                const data = await response.json();
                
                document.getElementById('mcpResult').innerHTML = `
                    <strong>MCP API测试:</strong><br>
                    状态: ✅ 连接成功<br>
                    响应: ${JSON.stringify(data, null, 2)}
                `;
            } catch (error) {
                document.getElementById('mcpResult').innerHTML = `
                    <strong>MCP API测试:</strong><br>
                    状态: ❌ 连接失败<br>
                    错误: ${error.message}
                `;
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 测试页面初始化完成');
            testDashboard();
            updateServicesDisplay();
        });
    </script>
</body>
</html>
