#!/usr/bin/env node
// 使用框架 - 只需要5行核心代码！

import { createMCP, templates } from './simple-mcp-framework.js';

// 5行代码创建完整MCP服务
createMCP('file-service')
  .tool('read_file', templates.fileOps.read_file.description, templates.fileOps.read_file.handler, templates.fileOps.read_file.schema)
  .tool('write_file', templates.fileOps.write_file.description, templates.fileOps.write_file.handler, templates.fileOps.write_file.schema)
  .tool('list_directory', templates.fileOps.list_directory.description, templates.fileOps.list_directory.handler, templates.fileOps.list_directory.schema)
  .start();

// 总计：5行核心代码 vs 200+行手写代码
