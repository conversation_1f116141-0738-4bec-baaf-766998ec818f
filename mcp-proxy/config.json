{"mcpProxy": {"baseURL": "http://localhost:9090", "addr": ":9090", "name": "Enterprise MCP Proxy", "version": "1.0.0", "type": "streamable-http", "options": {"logEnabled": true, "authTokens": ["mcp_admin_0f3528dfdf9b7f6b8f86b16206778107", "mcp_user_cb37957bf17e4dfa4981764e43d5cde6"]}}, "mcpServers": {"enterprise": {"url": "http://localhost:9090/mcp", "transportType": "streamable-http", "headers": {"Content-Type": "application/json"}}}}