{"name": "mcp-framework", "version": "0.1.0", "description": "A powerful framework for building MCP (Model Context Protocol) services with ease", "private": true, "type": "module", "scripts": {"build": "pnpm -r build", "dev": "pnpm -r --parallel dev", "test": "vitest", "test:ci": "vitest run", "lint": "eslint . --ext .ts,.js,.json", "lint:fix": "eslint . --ext .ts,.js,.json --fix", "format": "prettier --write .", "format:check": "prettier --check .", "typecheck": "pnpm -r typecheck", "clean": "pnpm -r clean && rm -rf node_modules", "changeset": "changeset", "version-packages": "changeset version", "release": "pnpm build && changeset publish", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs"}, "keywords": ["mcp", "model-context-protocol", "ai", "framework", "typescript", "cli", "development-tools"], "author": "MCP Framework Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/mcp-framework/mcp-framework.git"}, "bugs": {"url": "https://github.com/mcp-framework/mcp-framework/issues"}, "homepage": "https://mcp-framework.dev", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0", "devDependencies": {"@changesets/cli": "^2.27.1", "@types/node": "^20.11.0", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-json": "^3.1.0", "prettier": "^3.2.4", "typescript": "^5.3.3", "vitepress": "^1.0.0-rc.40", "vitest": "^1.2.0"}}