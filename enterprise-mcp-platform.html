<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏢 企业级MCP管理平台</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8fafc; }
        
        .header { background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%); color: white; padding: 15px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header-content { max-width: 1400px; margin: 0 auto; padding: 0 20px; display: flex; justify-content: space-between; align-items: center; }
        .logo { font-size: 1.5em; font-weight: bold; }
        .user-info { display: flex; align-items: center; gap: 15px; }
        .user-avatar { width: 32px; height: 32px; border-radius: 50%; background: #60a5fa; display: flex; align-items: center; justify-content: center; }
        
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .main-layout { display: grid; grid-template-columns: 250px 1fr; gap: 20px; }
        
        .sidebar { background: white; border-radius: 12px; padding: 20px; height: fit-content; box-shadow: 0 2px 10px rgba(0,0,0,0.05); }
        .nav-item { padding: 12px 16px; margin: 5px 0; border-radius: 8px; cursor: pointer; transition: all 0.3s; display: flex; align-items: center; gap: 10px; }
        .nav-item:hover { background: #f1f5f9; }
        .nav-item.active { background: #3b82f6; color: white; }
        
        .main-content { background: white; border-radius: 12px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); }
        .page-title { font-size: 1.8em; margin-bottom: 20px; color: #1e293b; }
        
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); padding: 20px; border-radius: 12px; text-align: center; border-left: 4px solid #3b82f6; }
        .stat-number { font-size: 2em; font-weight: bold; color: #1e293b; }
        .stat-label { color: #64748b; margin-top: 5px; }
        
        .services-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; }
        .service-card { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px; transition: all 0.3s; }
        .service-card:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.1); }
        .service-card.running { border-left: 4px solid #10b981; }
        .service-card.stopped { border-left: 4px solid #ef4444; }
        
        .service-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .service-title { font-size: 1.2em; font-weight: 600; color: #1e293b; }
        .service-status { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; }
        .service-status.running { background: #dcfce7; color: #166534; }
        .service-status.stopped { background: #fef2f2; color: #dc2626; }
        
        .btn { padding: 8px 16px; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s; }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn-secondary { background: #6b7280; color: white; }
        .btn:hover { opacity: 0.8; }
        
        .permissions-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .permissions-table th, .permissions-table td { padding: 12px; text-align: left; border-bottom: 1px solid #e2e8f0; }
        .permissions-table th { background: #f8fafc; font-weight: 600; }
        
        .auth-token { background: #1f2937; color: #f9fafb; padding: 8px 12px; border-radius: 6px; font-family: monospace; font-size: 12px; }
        
        .log-panel { background: #1a1a1a; color: #10b981; padding: 20px; border-radius: 8px; font-family: 'SF Mono', Monaco, monospace; font-size: 12px; height: 400px; overflow-y: auto; }
        
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; }
        .modal-content { background: white; margin: 5% auto; padding: 30px; border-radius: 12px; width: 90%; max-width: 600px; }
        .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .modal-title { font-size: 1.3em; font-weight: 600; }
        .close { font-size: 24px; cursor: pointer; }
        
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; }
        .form-group input, .form-group select, .form-group textarea { width: 100%; padding: 10px; border: 1px solid #d1d5db; border-radius: 6px; }
        
        .transport-selector { display: flex; gap: 10px; margin: 10px 0; }
        .transport-option { padding: 8px 16px; border: 2px solid #e5e7eb; border-radius: 6px; cursor: pointer; transition: all 0.3s; }
        .transport-option.selected { border-color: #3b82f6; background: #eff6ff; }
        
        .notification { position: fixed; top: 20px; right: 20px; padding: 15px 20px; border-radius: 8px; color: white; font-weight: 600; z-index: 1001; transform: translateX(400px); transition: transform 0.3s; }
        .notification.show { transform: translateX(0); }
        .notification.success { background: #10b981; }
        .notification.error { background: #ef4444; }
        .notification.info { background: #3b82f6; }
        
        .hidden { display: none; }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏢 企业MCP平台</div>
            <div class="user-info">
                <span>管理员</span>
                <div class="user-avatar">A</div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="main-layout">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="nav-item active" onclick="showPage('dashboard')">
                    <span>📊</span> 仪表板
                </div>
                <div class="nav-item" onclick="showPage('services')">
                    <span>🛠️</span> 服务管理
                </div>
                <div class="nav-item" onclick="showPage('permissions')">
                    <span>🔐</span> 权限管理
                </div>
                <div class="nav-item" onclick="showPage('logs')">
                    <span>📝</span> 调用日志
                </div>
                <div class="nav-item" onclick="showPage('api-converter')">
                    <span>🔄</span> API转换
                </div>
                <div class="nav-item" onclick="showPage('network')">
                    <span>🌐</span> 网络配置
                </div>
                <div class="nav-item" onclick="showPage('settings')">
                    <span>⚙️</span> 系统设置
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="main-content">
                <!-- 仪表板 -->
                <div id="dashboard" class="page">
                    <h2 class="page-title">📊 系统仪表板</h2>
                    
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number" id="runningServices">0</div>
                            <div class="stat-label">运行中服务</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="totalUsers">0</div>
                            <div class="stat-label">授权用户</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="todayCalls">0</div>
                            <div class="stat-label">今日调用</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="activeTokens">0</div>
                            <div class="stat-label">活跃令牌</div>
                        </div>
                    </div>

                    <div style="display: flex; gap: 20px; margin-bottom: 20px;">
                        <button class="btn btn-success" onclick="startAllServices()">🚀 启动所有服务</button>
                        <button class="btn btn-danger" onclick="stopAllServices()">⏹️ 停止所有服务</button>
                        <button class="btn btn-primary" onclick="generateUnifiedConfig()">📋 生成统一配置</button>
                        <button class="btn btn-secondary" onclick="exportLogs()">📤 导出日志</button>
                    </div>
                </div>

                <!-- 服务管理 -->
                <div id="services" class="page hidden">
                    <h2 class="page-title">🛠️ MCP服务管理</h2>
                    
                    <div style="margin-bottom: 20px;">
                        <button class="btn btn-primary" onclick="showAddServiceModal()">➕ 添加服务</button>
                        <button class="btn btn-secondary" onclick="importServices()">📥 导入服务</button>
                    </div>
                    
                    <div class="services-grid" id="servicesGrid"></div>
                </div>

                <!-- 权限管理 -->
                <div id="permissions" class="page hidden">
                    <h2 class="page-title">🔐 权限管理</h2>
                    
                    <div style="margin-bottom: 20px;">
                        <button class="btn btn-primary" onclick="showAddUserModal()">👤 添加用户</button>
                        <button class="btn btn-secondary" onclick="generateAuthToken()">🔑 生成令牌</button>
                    </div>
                    
                    <table class="permissions-table">
                        <thead>
                            <tr>
                                <th>用户</th>
                                <th>授权令牌</th>
                                <th>可用服务</th>
                                <th>权限级别</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="permissionsTable">
                            <tr>
                                <td><EMAIL></td>
                                <td><span class="auth-token">mcp_admin_abc123</span></td>
                                <td>全部服务</td>
                                <td>管理员</td>
                                <td><span class="service-status running">活跃</span></td>
                                <td>
                                    <button class="btn btn-secondary">编辑</button>
                                    <button class="btn btn-danger">禁用</button>
                                </td>
                            </tr>
                            <tr>
                                <td><EMAIL></td>
                                <td><span class="auth-token">mcp_user_def456</span></td>
                                <td>文件操作, Web工具</td>
                                <td>普通用户</td>
                                <td><span class="service-status running">活跃</span></td>
                                <td>
                                    <button class="btn btn-secondary">编辑</button>
                                    <button class="btn btn-danger">禁用</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 调用日志 -->
                <div id="logs" class="page hidden">
                    <h2 class="page-title">📝 MCP调用日志</h2>
                    
                    <div style="margin-bottom: 20px; display: flex; gap: 10px; align-items: center;">
                        <select id="logFilter">
                            <option value="all">全部日志</option>
                            <option value="success">成功调用</option>
                            <option value="error">错误日志</option>
                            <option value="auth">认证日志</option>
                        </select>
                        <input type="date" id="logDate" style="padding: 8px;">
                        <button class="btn btn-primary" onclick="filterLogs()">🔍 筛选</button>
                        <button class="btn btn-secondary" onclick="clearLogs()">🗑️ 清空</button>
                    </div>
                    
                    <div class="log-panel" id="logPanel">
                        [2024-01-15 10:30:00] 🔐 用户认证成功: <EMAIL> (token: mcp_admin_***)<br>
                        [2024-01-15 10:30:05] 🛠️ 调用工具: file-ops__read_file (用户: <EMAIL>)<br>
                        [2024-01-15 10:30:06] ✅ 工具调用成功: 返回文件内容 (耗时: 120ms)<br>
                        [2024-01-15 10:31:00] 🔐 用户认证成功: <EMAIL> (token: mcp_user_***)<br>
                        [2024-01-15 10:31:05] 🛠️ 调用工具: web-tools__http_request (用户: <EMAIL>)<br>
                        [2024-01-15 10:31:07] ✅ 工具调用成功: HTTP 200 OK (耗时: 2.1s)<br>
                        [2024-01-15 10:32:00] ❌ 权限拒绝: <EMAIL> 尝试访问 database__create_table<br>
                        [2024-01-15 10:33:00] 🔄 API转换: REST API -> MCP工具 (endpoint: /api/weather)<br>
                    </div>
                </div>

                <!-- API转换 -->
                <div id="api-converter" class="page hidden">
                    <h2 class="page-title">🔄 API转MCP转换器</h2>
                    
                    <div style="margin-bottom: 20px;">
                        <button class="btn btn-primary" onclick="showApiConverterModal()">➕ 添加API转换</button>
                        <button class="btn btn-secondary" onclick="testApiConversion()">🧪 测试转换</button>
                    </div>
                    
                    <div class="services-grid">
                        <div class="service-card">
                            <div class="service-header">
                                <div class="service-title">天气API转换</div>
                                <div class="service-status running">运行中</div>
                            </div>
                            <div style="color: #64748b; margin-bottom: 15px;">
                                将OpenWeatherMap API转换为MCP工具
                            </div>
                            <div style="font-size: 12px; color: #64748b;">
                                <div>API端点: https://api.openweathermap.org/data/2.5/weather</div>
                                <div>MCP工具: weather__get_current</div>
                            </div>
                            <div style="margin-top: 15px;">
                                <button class="btn btn-secondary">编辑</button>
                                <button class="btn btn-danger">删除</button>
                            </div>
                        </div>
                        
                        <div class="service-card">
                            <div class="service-header">
                                <div class="service-title">翻译API转换</div>
                                <div class="service-status running">运行中</div>
                            </div>
                            <div style="color: #64748b; margin-bottom: 15px;">
                                将Google Translate API转换为MCP工具
                            </div>
                            <div style="font-size: 12px; color: #64748b;">
                                <div>API端点: https://translate.googleapis.com/translate_a/single</div>
                                <div>MCP工具: translate__text</div>
                            </div>
                            <div style="margin-top: 15px;">
                                <button class="btn btn-secondary">编辑</button>
                                <button class="btn btn-danger">删除</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 网络配置 -->
                <div id="network" class="page hidden">
                    <h2 class="page-title">🌐 网络传输配置</h2>
                    
                    <div style="margin-bottom: 30px;">
                        <h3 style="margin-bottom: 15px;">传输协议支持</h3>
                        <div class="transport-selector">
                            <div class="transport-option selected">
                                <strong>📡 stdio</strong><br>
                                <small>标准输入输出</small>
                            </div>
                            <div class="transport-option selected">
                                <strong>🌊 SSE</strong><br>
                                <small>服务器推送事件</small>
                            </div>
                            <div class="transport-option selected">
                                <strong>🔄 HTTP Streaming</strong><br>
                                <small>HTTP流式传输</small>
                            </div>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <h3 style="margin-bottom: 15px;">统一MCP配置</h3>
                            <textarea id="unifiedConfig" style="width: 100%; height: 300px; font-family: monospace; font-size: 12px;" readonly>
{
  "mcpServers": {
    "enterprise-mcp": {
      "command": "node",
      "args": ["enterprise-mcp-server.js"],
      "env": {
        "MCP_AUTH_TOKEN": "your-auth-token"
      }
    }
  }
}
                            </textarea>
                            <button class="btn btn-primary" onclick="copyUnifiedConfig()">📋 复制配置</button>
                        </div>
                        
                        <div>
                            <h3 style="margin-bottom: 15px;">网络端点</h3>
                            <div style="background: #f8fafc; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 12px;">
                                <div><strong>HTTP:</strong> http://localhost:9090/mcp</div>
                                <div><strong>SSE:</strong> http://localhost:9090/sse</div>
                                <div><strong>WebSocket:</strong> ws://localhost:9090/ws</div>
                                <div><strong>管理API:</strong> http://localhost:8080/api</div>
                            </div>
                            
                            <h4 style="margin: 20px 0 10px 0;">认证示例</h4>
                            <div style="background: #1f2937; color: #f9fafb; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 12px;">
curl -H "Authorization: Bearer mcp_token_123" \<br>
&nbsp;&nbsp;&nbsp;&nbsp;http://localhost:9090/mcp
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="addServiceModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">➕ 添加MCP服务</h3>
                <span class="close" onclick="closeModal('addServiceModal')">&times;</span>
            </div>
            <div class="form-group">
                <label>服务名称</label>
                <input type="text" id="serviceName" placeholder="my-service">
            </div>
            <div class="form-group">
                <label>服务描述</label>
                <textarea id="serviceDescription" placeholder="服务功能描述"></textarea>
            </div>
            <div class="form-group">
                <label>启动命令</label>
                <input type="text" id="serviceCommand" placeholder="node my-service.js">
            </div>
            <div class="form-group">
                <label>传输方式</label>
                <div class="transport-selector">
                    <div class="transport-option selected" data-transport="stdio">stdio</div>
                    <div class="transport-option" data-transport="sse">SSE</div>
                    <div class="transport-option" data-transport="http">HTTP</div>
                </div>
            </div>
            <div style="text-align: right;">
                <button class="btn btn-secondary" onclick="closeModal('addServiceModal')">取消</button>
                <button class="btn btn-primary" onclick="addService()">添加服务</button>
            </div>
        </div>
    </div>

    <!-- 通知 -->
    <div id="notification" class="notification"></div>

    <script>
        // 全局状态
        const state = {
            services: new Map(),
            users: new Map(),
            logs: [],
            currentPage: 'dashboard',
            adminToken: 'admin_token_placeholder' // 临时管理员令牌
        };

        // 初始化服务数据
        const initialServices = {
            'universal': {
                id: 'universal',
                name: '通用MCP服务',
                status: 'stopped',
                tools: ['read_file', 'write_file', 'http_request', 'get_system_info'],
                description: '提供文件操作、Web工具、系统工具等基础功能'
            },
            'database': {
                id: 'database',
                name: '数据库服务',
                status: 'stopped',
                tools: ['create_table', 'insert_data', 'query_data'],
                description: '提供数据库操作功能'
            },
            'email': {
                id: 'email',
                name: '邮件服务',
                status: 'stopped',
                tools: ['send_email', 'check_inbox'],
                description: '提供邮件发送和接收功能'
            },
            'password': {
                id: 'password',
                name: '密码管理',
                status: 'stopped',
                tools: ['generate_password', 'store_password', 'get_password'],
                description: '提供密码生成和管理功能'
            },
            'analytics': {
                id: 'analytics',
                name: '数据分析',
                status: 'stopped',
                tools: ['analyze_csv', 'calculate_stats'],
                description: '提供数据分析和统计功能'
            },
            'weather': {
                id: 'weather',
                name: '天气服务',
                status: 'stopped',
                tools: ['get_weather', 'get_forecast'],
                description: '提供天气查询和预报功能'
            }
        };

        // 初始化状态
        Object.values(initialServices).forEach(service => {
            state.services.set(service.id, service);
        });

        // 页面切换 - 完全重写
        function showPage(pageId) {
            console.log('切换到页面:', pageId);

            // 移除所有导航项的active类
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.add('hidden');
            });

            // 找到并激活当前导航项
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(nav => {
                const onclick = nav.getAttribute('onclick');
                if (onclick && onclick.includes(`'${pageId}'`)) {
                    nav.classList.add('active');
                }
            });

            // 显示当前页面
            const currentPage = document.getElementById(pageId);
            if (currentPage) {
                currentPage.classList.remove('hidden');
                console.log('页面显示成功:', pageId);
            } else {
                console.error('页面不存在:', pageId);
            }

            state.currentPage = pageId;

            // 页面特定的初始化
            switch(pageId) {
                case 'dashboard':
                    updateDashboard();
                    break;
                case 'services':
                    renderServices();
                    break;
                case 'logs':
                    updateLogs();
                    break;
                case 'permissions':
                    renderPermissions();
                    break;
                case 'network':
                    updateNetworkConfig();
                    break;
                case 'api-converter':
                    renderApiConverters();
                    break;
            }
        }

        // 服务管理 - 完全重写
        function renderServices() {
            console.log('渲染服务列表');
            const grid = document.getElementById('servicesGrid');
            if (!grid) {
                console.error('服务网格元素不存在');
                return;
            }

            const services = Array.from(state.services.values());
            console.log('当前服务:', services);

            grid.innerHTML = services.map(service => `
                <div class="service-card ${service.status}">
                    <div class="service-header">
                        <div class="service-title">${service.name}</div>
                        <div class="service-status ${service.status}">
                            ${service.status === 'running' ? '运行中' : '已停止'}
                        </div>
                    </div>
                    <div style="color: #64748b; margin-bottom: 15px;">
                        ${service.description}
                    </div>
                    <div style="color: #64748b; margin-bottom: 15px; font-size: 12px;">
                        工具: ${service.tools.join(', ')}
                    </div>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        ${service.status === 'running' ?
                            `<button class="btn btn-danger" onclick="stopService('${service.id}')">⏹️ 停止</button>` :
                            `<button class="btn btn-success" onclick="startService('${service.id}')">🚀 启动</button>`
                        }
                        <button class="btn btn-secondary" onclick="configService('${service.id}')">⚙️ 配置</button>
                        <button class="btn btn-secondary" onclick="testService('${service.id}')">🧪 测试</button>
                    </div>
                </div>
            `).join('');

            updateDashboard();
        }

        // 更新仪表板
        function updateDashboard() {
            const services = Array.from(state.services.values());
            const running = services.filter(s => s.status === 'running').length;
            const stopped = services.filter(s => s.status === 'stopped').length;
            const totalTools = services.reduce((sum, s) => sum + s.tools.length, 0);

            const runningEl = document.getElementById('runningServices');
            const totalUsersEl = document.getElementById('totalUsers');
            const todayCallsEl = document.getElementById('todayCalls');
            const activeTokensEl = document.getElementById('activeTokens');

            if (runningEl) runningEl.textContent = running;
            if (totalUsersEl) totalUsersEl.textContent = '2';
            if (todayCallsEl) todayCallsEl.textContent = Math.floor(Math.random() * 1000) + 500;
            if (activeTokensEl) activeTokensEl.textContent = '3';

            console.log(`仪表板更新: 运行中${running}, 总计${services.length}, 工具${totalTools}`);
        }

        // 服务操作 - 完全重写，使用本地状态
        function startService(serviceId) {
            console.log('启动服务:', serviceId);

            const service = state.services.get(serviceId);
            if (!service) {
                showNotification(`服务不存在: ${serviceId}`, 'error');
                return;
            }

            if (service.status === 'running') {
                showNotification(`服务 ${serviceId} 已在运行`, 'info');
                return;
            }

            // 模拟启动过程
            showNotification(`正在启动服务: ${service.name}`, 'info');

            setTimeout(() => {
                service.status = 'running';
                state.services.set(serviceId, service);

                showNotification(`服务 ${service.name} 启动成功`, 'success');
                addLog(`✅ 服务启动: ${service.name}`);

                // 重新渲染服务列表
                if (state.currentPage === 'services') {
                    renderServices();
                }
                updateDashboard();
            }, 1000);
        }

        function stopService(serviceId) {
            console.log('停止服务:', serviceId);

            const service = state.services.get(serviceId);
            if (!service) {
                showNotification(`服务不存在: ${serviceId}`, 'error');
                return;
            }

            if (service.status === 'stopped') {
                showNotification(`服务 ${serviceId} 已停止`, 'info');
                return;
            }

            // 模拟停止过程
            showNotification(`正在停止服务: ${service.name}`, 'info');

            setTimeout(() => {
                service.status = 'stopped';
                state.services.set(serviceId, service);

                showNotification(`服务 ${service.name} 已停止`, 'success');
                addLog(`⏹️ 服务停止: ${service.name}`);

                // 重新渲染服务列表
                if (state.currentPage === 'services') {
                    renderServices();
                }
                updateDashboard();
            }, 1000);
        }

        function startAllServices() {
            console.log('启动所有服务');
            const services = Array.from(state.services.keys());
            let index = 0;

            function startNext() {
                if (index < services.length) {
                    const serviceId = services[index];
                    startService(serviceId);
                    index++;
                    setTimeout(startNext, 1500); // 延迟1.5秒启动下一个
                }
            }

            startNext();
        }

        function stopAllServices() {
            console.log('停止所有服务');
            const services = Array.from(state.services.keys());
            let index = 0;

            function stopNext() {
                if (index < services.length) {
                    const serviceId = services[index];
                    stopService(serviceId);
                    index++;
                    setTimeout(stopNext, 1500); // 延迟1.5秒停止下一个
                }
            }

            stopNext();
        }

        // 权限管理
        function renderPermissions() {
            console.log('权限管理页面已加载');
            // 权限表格已在HTML中定义，这里可以添加动态加载逻辑
        }

        function renderApiConverters() {
            console.log('API转换器页面已加载');
            // API转换器内容已在HTML中定义
        }

        function configService(serviceId) {
            showNotification(`配置服务: ${serviceId}`, 'info');
            addLog(`⚙️ 配置服务: ${serviceId}`);
        }

        function testService(serviceId) {
            showNotification(`测试服务: ${serviceId}`, 'info');
            addLog(`🧪 测试服务: ${serviceId}`);
        }

        function showAddUserModal() {
            document.getElementById('addServiceModal').style.display = 'block';
        }

        function generateAuthToken() {
            const token = 'mcp_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
            showNotification(`新令牌已生成: ${token}`, 'success');
            addLog(`🔑 生成新令牌: ${token.substring(0, 12)}***`);
        }

        // 网络配置
        function updateNetworkConfig() {
            const config = {
                mcpServers: {
                    "enterprise-mcp": {
                        command: "node",
                        args: ["enterprise-mcp-server.js"],
                        env: {
                            MCP_AUTH_TOKEN: "your-auth-token"
                        }
                    }
                }
            };

            document.getElementById('unifiedConfig').value = JSON.stringify(config, null, 2);
        }

        function copyUnifiedConfig() {
            const config = document.getElementById('unifiedConfig').value;
            navigator.clipboard.writeText(config).then(() => {
                showNotification('配置已复制到剪贴板', 'success');
            });
        }

        // API转换器
        function showApiConverterModal() {
            showNotification('API转换器功能开发中', 'info');
        }

        function testApiConversion() {
            showNotification('API转换测试功能开发中', 'info');
        }

        // 日志管理
        function addLog(message) {
            const timestamp = new Date().toLocaleString();
            const logEntry = `[${timestamp}] ${message}`;
            state.logs.push(logEntry);

            if (state.currentPage === 'logs') {
                updateLogs();
            }
        }

        function updateLogs() {
            const logPanel = document.getElementById('logPanel');
            if (logPanel) {
                logPanel.innerHTML = state.logs.slice(-50).join('<br>');
                logPanel.scrollTop = logPanel.scrollHeight;
            }
        }

        function filterLogs() {
            const filter = document.getElementById('logFilter').value;
            const date = document.getElementById('logDate').value;
            showNotification(`日志筛选: ${filter} ${date}`, 'info');
            updateLogs();
        }

        function clearLogs() {
            state.logs = [];
            updateLogs();
            showNotification('日志已清空', 'info');
        }

        // 模态框管理
        function showAddServiceModal() {
            document.getElementById('addServiceModal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function addService() {
            const name = document.getElementById('serviceName').value;
            const description = document.getElementById('serviceDescription').value;
            const command = document.getElementById('serviceCommand').value;

            if (!name || !command) {
                showNotification('请填写必要信息', 'error');
                return;
            }

            showNotification(`服务 ${name} 添加成功`, 'success');
            addLog(`📦 添加服务: ${name}`);
            closeModal('addServiceModal');
            renderServices();
        }

        function configService(serviceId) {
            showNotification(`配置服务: ${serviceId}`, 'info');
        }

        function testService(serviceId) {
            showNotification(`测试服务: ${serviceId}`, 'info');
        }

        function viewLogs(serviceId) {
            showPage('logs');
            showNotification(`查看服务日志: ${serviceId}`, 'info');
        }

        // 其他功能
        function generateUnifiedConfig() {
            updateNetworkConfig();
            showNotification('统一配置已生成', 'success');
        }

        function exportConfig() {
            const config = {
                services: Array.from(state.services.values()),
                users: Array.from(state.users.values())
            };

            const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'mcp-platform-config.json';
            a.click();
            URL.revokeObjectURL(url);

            showNotification('配置已导出', 'success');
        }

        function importConfig() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = (e) => {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const config = JSON.parse(e.target.result);
                            showNotification('配置导入成功', 'success');
                            addLog('📥 配置文件导入成功');
                        } catch (error) {
                            showNotification('配置文件格式错误', 'error');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        function exportLogs() {
            const logs = state.logs.join('\n');
            const blob = new Blob([logs], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'mcp-platform-logs.txt';
            a.click();
            URL.revokeObjectURL(url);

            showNotification('日志已导出', 'success');
        }

        function restartProxy() {
            showNotification('代理服务重启中...', 'info');
            addLog('🔄 代理服务重启');
            setTimeout(() => {
                showNotification('代理服务重启完成', 'success');
            }, 2000);
        }

        // 通知系统
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type} show`;
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // 统一配置生成
        function generateUnifiedConfig() {
            const config = {
                mcpServers: {
                    "enterprise-mcp": {
                        command: "node",
                        args: ["enterprise-mcp-server.js"],
                        env: {
                            MCP_AUTH_TOKEN: "your-auth-token"
                        }
                    }
                }
            };
            
            document.getElementById('unifiedConfig').value = JSON.stringify(config, null, 2);
            showNotification('统一配置已生成', 'success');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面初始化开始');

            // 初始化日志
            addLog('🚀 企业MCP平台启动完成');
            addLog('📦 加载服务配置...');
            addLog('🔐 权限系统初始化完成');
            addLog('🌐 网络服务启动成功');

            // 初始化页面数据
            updateDashboard();
            updateNetworkConfig();

            // 默认显示仪表板
            showPage('dashboard');

            // 添加传输选项点击事件
            document.querySelectorAll('.transport-option').forEach(option => {
                option.addEventListener('click', function() {
                    // 移除其他选中状态
                    this.parentNode.querySelectorAll('.transport-option').forEach(opt =>
                        opt.classList.remove('selected')
                    );
                    // 添加选中状态
                    this.classList.add('selected');
                });
            });

            // 模态框关闭事件
            document.addEventListener('click', (e) => {
                if (e.target.classList.contains('modal')) {
                    e.target.style.display = 'none';
                }
            });

            console.log('页面初始化完成');
        });
    </script>
</body>
</html>
