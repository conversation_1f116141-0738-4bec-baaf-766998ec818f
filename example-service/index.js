#!/usr/bin/env node

/**
 * Example MCP Service
 * 
 * A simple working example of an MCP service
 */

console.log('🚀 Starting Example MCP Service...');

// Simulate MCP service functionality
class SimpleMCPService {
  constructor(name, version) {
    this.name = name;
    this.version = version;
    this.tools = new Map();
  }

  addTool(name, description, handler) {
    this.tools.set(name, { name, description, handler });
    console.log(`   ✅ Added tool: ${name}`);
  }

  async callTool(name, input) {
    const tool = this.tools.get(name);
    if (!tool) {
      throw new Error(`Tool not found: ${name}`);
    }
    return await tool.handler(input);
  }

  start() {
    console.log(`📡 Service started: ${this.name} v${this.version}`);
    console.log(`🔧 Available tools: ${Array.from(this.tools.keys()).join(', ')}`);
    
    // Simulate some tool calls
    this.demo();
  }

  async demo() {
    console.log('\n🧪 Running demo...');
    
    try {
      const result1 = await this.callTool('greet', { name: 'World' });
      console.log(`   Result: ${result1}`);
      
      const result2 = await this.callTool('add', { a: 5, b: 3 });
      console.log(`   Result: ${result2}`);
      
      console.log('\n✅ Demo completed successfully!');
    } catch (error) {
      console.error('❌ Demo failed:', error.message);
    }
  }
}

// Create and configure service
const service = new SimpleMCPService('example-service', '1.0.0');

// Add tools
service.addTool('greet', 'Say hello to someone', ({ name }) => {
  return `Hello, ${name}!`;
});

service.addTool('add', 'Add two numbers', ({ a, b }) => {
  return a + b;
});

service.addTool('echo', 'Echo back the input', (input) => {
  return JSON.stringify(input);
});

// Start service
service.start();
