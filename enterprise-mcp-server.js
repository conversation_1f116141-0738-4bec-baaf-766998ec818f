#!/usr/bin/env node

/**
 * 🏢 企业级MCP服务器
 * 
 * 核心功能:
 * 1. 统一MCP服务管理
 * 2. 细粒度权限控制
 * 3. 多传输协议支持 (stdio/SSE/HTTP)
 * 4. API转MCP转换
 * 5. 完整的调用日志
 * 6. 授权码认证
 */

import http from 'http';
import https from 'https';
import { spawn } from 'child_process';
import crypto from 'crypto';
import fs from 'fs/promises';
import { EventEmitter } from 'events';

class EnterpriseMCPServer extends EventEmitter {
  constructor() {
    super();
    this.services = new Map();
    this.users = new Map();
    this.authTokens = new Map();
    this.callLogs = [];
    this.apiConverters = new Map();
    this.httpServer = null;
    this.sseClients = new Set();
    
    this.initializeDefaultUsers();
  }

  // 初始化默认用户
  initializeDefaultUsers() {
    this.addUser({
      id: 'admin',
      email: '<EMAIL>',
      role: 'admin',
      permissions: ['*'], // 全部权限
      authToken: 'mcp_admin_' + crypto.randomBytes(16).toString('hex')
    });

    this.addUser({
      id: 'user1',
      email: '<EMAIL>', 
      role: 'user',
      permissions: ['file-ops:*', 'web-tools:*'], // 特定服务权限
      authToken: 'mcp_user_' + crypto.randomBytes(16).toString('hex')
    });
  }

  // 用户管理
  addUser(userData) {
    this.users.set(userData.id, userData);
    this.authTokens.set(userData.authToken, userData.id);
    this.log(`👤 用户添加: ${userData.email} (${userData.role})`);
  }

  // 权限验证 - 细粒度权限控制
  checkPermission(authToken, service, tool, action = 'execute') {
    const userId = this.authTokens.get(authToken);
    if (!userId) return false;

    const user = this.users.get(userId);
    if (!user || !user.active) return false;

    // 检查用户状态
    if (user.expiresAt && new Date() > new Date(user.expiresAt)) {
      return false;
    }

    // 管理员权限
    if (user.role === 'admin' || user.permissions.includes('*')) return true;

    // 检查时间限制
    if (user.timeRestrictions) {
      const now = new Date();
      const currentHour = now.getHours();
      const currentDay = now.getDay();

      if (user.timeRestrictions.hours &&
          !user.timeRestrictions.hours.includes(currentHour)) {
        return false;
      }

      if (user.timeRestrictions.days &&
          !user.timeRestrictions.days.includes(currentDay)) {
        return false;
      }
    }

    // 检查调用频率限制
    if (user.rateLimits) {
      const recentCalls = this.callLogs.filter(log =>
        log.user === user.email &&
        new Date() - new Date(log.timestamp) < user.rateLimits.window * 1000
      );

      if (recentCalls.length >= user.rateLimits.maxCalls) {
        return false;
      }
    }

    // 检查服务权限
    const servicePermission = `${service}:*`;
    if (user.permissions.includes(servicePermission)) return true;

    // 检查具体工具权限
    const toolPermission = `${service}:${tool}`;
    if (user.permissions.includes(toolPermission)) return true;

    // 检查带动作的权限
    const actionPermission = `${service}:${tool}:${action}`;
    return user.permissions.includes(actionPermission);
  }

  // 权限管理API
  async updateUserPermissions(userId, permissions) {
    const user = this.users.get(userId);
    if (!user) throw new Error('用户不存在');

    user.permissions = permissions;
    user.updatedAt = new Date();

    this.log(`🔐 权限更新: ${user.email}`);
    this.broadcastSse('permission-updated', { userId, permissions });
  }

  // 生成授权码
  generateAuthToken(userId, options = {}) {
    const user = this.users.get(userId);
    if (!user) throw new Error('用户不存在');

    const tokenData = {
      userId,
      type: options.type || 'access',
      expiresAt: options.expiresAt,
      permissions: options.permissions || user.permissions,
      createdAt: new Date()
    };

    const token = 'mcp_' + crypto.randomBytes(32).toString('hex');
    this.authTokens.set(token, userId);

    // 存储令牌详细信息
    user.tokens = user.tokens || [];
    user.tokens.push({ token, ...tokenData });

    this.log(`🔑 授权码生成: ${user.email} (${tokenData.type})`);
    return token;
  }

  // 撤销授权码
  revokeAuthToken(token) {
    const userId = this.authTokens.get(token);
    if (!userId) return false;

    this.authTokens.delete(token);

    const user = this.users.get(userId);
    if (user && user.tokens) {
      user.tokens = user.tokens.filter(t => t.token !== token);
    }

    this.log(`🚫 授权码撤销: ${token.substring(0, 12)}***`);
    return true;
  }

  // 服务注册
  registerService(serviceId, config) {
    this.services.set(serviceId, {
      id: serviceId,
      ...config,
      status: 'stopped',
      process: null,
      tools: config.tools || [],
      lastActivity: null
    });
    
    this.log(`📦 服务注册: ${serviceId}`);
  }

  // 启动服务
  async startService(serviceId) {
    const service = this.services.get(serviceId);
    if (!service) throw new Error(`服务不存在: ${serviceId}`);

    if (service.status === 'running') {
      this.log(`⚠️ 服务 ${serviceId} 已在运行`);
      return;
    }

    try {
      const childProcess = spawn(service.command, service.args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env, ...service.env }
      });

      service.process = childProcess;
      service.status = 'running';
      service.startTime = new Date();

      childProcess.on('exit', (code) => {
        service.status = 'stopped';
        service.process = null;
        this.log(`📡 服务 ${serviceId} 退出 (代码: ${code})`);
      });

      this.log(`✅ 服务启动: ${serviceId}`);
      this.emit('serviceStarted', serviceId);
    } catch (error) {
      service.status = 'error';
      this.log(`❌ 服务启动失败: ${serviceId} - ${error.message}`);
      throw error;
    }
  }

  // 停止服务
  async stopService(serviceId) {
    const service = this.services.get(serviceId);
    if (!service || service.status !== 'running') return;

    if (service.process) {
      service.process.kill();
    }
    
    service.status = 'stopped';
    service.process = null;
    this.log(`⏹️ 服务停止: ${serviceId}`);
    this.emit('serviceStopped', serviceId);
  }

  // API转MCP转换器
  addApiConverter(converterId, config) {
    this.apiConverters.set(converterId, {
      id: converterId,
      ...config,
      createdAt: new Date()
    });
    
    this.log(`🔄 API转换器添加: ${converterId}`);
  }

  // 转换API调用为MCP工具
  async convertApiToMcp(converterId, params) {
    const converter = this.apiConverters.get(converterId);
    if (!converter) throw new Error(`转换器不存在: ${converterId}`);

    try {
      // 构建API请求
      const apiUrl = this.buildApiUrl(converter.endpoint, params);
      const response = await this.makeHttpRequest(apiUrl, converter.method, converter.headers);
      
      // 转换响应格式
      const mcpResponse = this.transformApiResponse(response, converter.responseTransform);
      
      this.log(`🔄 API转换成功: ${converterId}`);
      return mcpResponse;
    } catch (error) {
      this.log(`❌ API转换失败: ${converterId} - ${error.message}`);
      throw error;
    }
  }

  // HTTP请求工具
  async makeHttpRequest(url, method = 'GET', headers = {}) {
    return new Promise((resolve, reject) => {
      const client = url.startsWith('https:') ? https : http;
      const req = client.request(url, { method, headers }, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            resolve(JSON.parse(data));
          } catch {
            resolve(data);
          }
        });
      });
      
      req.on('error', reject);
      req.end();
    });
  }

  // MCP请求处理
  async handleMcpRequest(request, authToken) {
    // 权限验证
    const userId = this.authTokens.get(authToken);
    if (!userId) {
      this.logCall(null, request.method, 'AUTH_FAILED', authToken);
      throw new Error('认证失败');
    }

    const user = this.users.get(userId);
    this.logCall(user.email, request.method, 'REQUEST', authToken);

    try {
      let result;
      
      switch (request.method) {
        case 'initialize':
          result = await this.handleInitialize(user);
          break;
        case 'tools/list':
          result = await this.handleToolsList(user);
          break;
        case 'tools/call':
          result = await this.handleToolCall(request.params, user, authToken);
          break;
        default:
          throw new Error(`未知方法: ${request.method}`);
      }

      this.logCall(user.email, request.method, 'SUCCESS', authToken);
      return { jsonrpc: "2.0", id: request.id, result };
      
    } catch (error) {
      this.logCall(user.email, request.method, 'ERROR', authToken, error.message);
      return {
        jsonrpc: "2.0",
        id: request.id || null,
        error: { code: -32603, message: error.message }
      };
    }
  }

  // 处理初始化
  async handleInitialize(user) {
    return {
      protocolVersion: "2024-11-05",
      capabilities: { tools: {} },
      serverInfo: {
        name: "Enterprise MCP Server",
        version: "1.0.0"
      }
    };
  }

  // 处理工具列表
  async handleToolsList(user) {
    const tools = [];
    
    for (const [serviceId, service] of this.services) {
      if (service.status !== 'running') continue;
      
      for (const tool of service.tools) {
        // 检查权限
        if (this.checkPermission(user.authToken, serviceId, tool.name)) {
          tools.push({
            ...tool,
            name: `${serviceId}__${tool.name}`,
            description: `[${service.name}] ${tool.description}`
          });
        }
      }
    }

    // 添加API转换工具
    for (const [converterId, converter] of this.apiConverters) {
      if (this.checkPermission(user.authToken, 'api-converter', converterId)) {
        tools.push({
          name: `api__${converterId}`,
          description: `[API转换] ${converter.description}`,
          inputSchema: converter.inputSchema
        });
      }
    }

    return { tools };
  }

  // 处理工具调用
  async handleToolCall(params, user, authToken) {
    const toolName = params.name;
    const args = params.arguments || {};

    // 解析工具名称
    if (toolName.startsWith('api__')) {
      // API转换工具
      const converterId = toolName.substring(5);
      if (!this.checkPermission(authToken, 'api-converter', converterId)) {
        throw new Error('权限不足');
      }
      
      const result = await this.convertApiToMcp(converterId, args);
      return { content: [{ type: "text", text: JSON.stringify(result, null, 2) }] };
    } else {
      // 普通MCP工具
      const [serviceId, actualToolName] = toolName.split('__');
      
      if (!this.checkPermission(authToken, serviceId, actualToolName)) {
        throw new Error('权限不足');
      }

      const service = this.services.get(serviceId);
      if (!service || service.status !== 'running') {
        throw new Error(`服务不可用: ${serviceId}`);
      }

      // 转发到具体服务
      const result = await this.forwardToService(service, actualToolName, args);
      return { content: [{ type: "text", text: result }] };
    }
  }

  // 转发到具体服务
  async forwardToService(service, toolName, args) {
    return new Promise((resolve, reject) => {
      const request = {
        jsonrpc: "2.0",
        id: crypto.randomUUID(),
        method: "tools/call",
        params: { name: toolName, arguments: args }
      };

      service.process.stdin.write(JSON.stringify(request) + '\n');

      const timeout = setTimeout(() => {
        reject(new Error('服务调用超时'));
      }, 30000);

      const onData = (data) => {
        try {
          const response = JSON.parse(data.toString().trim());
          if (response.id === request.id) {
            clearTimeout(timeout);
            service.process.stdout.off('data', onData);
            
            if (response.error) {
              reject(new Error(response.error.message));
            } else {
              resolve(response.result.content[0].text);
            }
          }
        } catch (error) {
          // 忽略解析错误，继续等待
        }
      };

      service.process.stdout.on('data', onData);
    });
  }

  // 启动HTTP服务器
  async startHttpServer(port = 9091) {
    this.httpServer = http.createServer(async (req, res) => {
      // CORS设置
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

      if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
      }

      const url = new URL(req.url, `http://${req.headers.host}`);
      const authToken = this.extractAuthToken(req);

      try {
        if (url.pathname === '/mcp' && req.method === 'POST') {
          // HTTP MCP调用
          const body = await this.readRequestBody(req);
          const request = JSON.parse(body);
          const response = await this.handleMcpRequest(request, authToken);
          
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify(response));
          
        } else if (url.pathname === '/sse') {
          // SSE连接
          this.handleSseConnection(req, res, authToken);
          
        } else if (url.pathname === '/api/status') {
          // 状态API
          const status = this.getSystemStatus();
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify(status));
          
        } else {
          res.writeHead(404);
          res.end('Not Found');
        }
      } catch (error) {
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: error.message }));
      }
    });

    this.httpServer.listen(port, () => {
      this.log(`🌐 HTTP服务器启动: http://localhost:${port}`);
    });
  }

  // SSE连接处理
  handleSseConnection(req, res, authToken) {
    if (!this.authTokens.has(authToken)) {
      res.writeHead(401);
      res.end('Unauthorized');
      return;
    }

    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive'
    });

    const client = { res, authToken };
    this.sseClients.add(client);

    req.on('close', () => {
      this.sseClients.delete(client);
    });

    // 发送初始状态
    this.sendSseMessage(client, 'status', this.getSystemStatus());
  }

  // 发送SSE消息
  sendSseMessage(client, event, data) {
    client.res.write(`event: ${event}\n`);
    client.res.write(`data: ${JSON.stringify(data)}\n\n`);
  }

  // 广播SSE消息
  broadcastSse(event, data) {
    for (const client of this.sseClients) {
      this.sendSseMessage(client, event, data);
    }
  }

  // 提取认证令牌
  extractAuthToken(req) {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    return null;
  }

  // 读取请求体
  async readRequestBody(req) {
    return new Promise((resolve, reject) => {
      let body = '';
      req.on('data', chunk => body += chunk.toString());
      req.on('end', () => resolve(body));
      req.on('error', reject);
    });
  }

  // 获取系统状态
  getSystemStatus() {
    const runningServices = Array.from(this.services.values()).filter(s => s.status === 'running').length;
    const totalServices = this.services.size;
    const totalUsers = this.users.size;
    const todayCalls = this.callLogs.filter(log => {
      const today = new Date().toDateString();
      return new Date(log.timestamp).toDateString() === today;
    }).length;

    return {
      services: {
        running: runningServices,
        total: totalServices
      },
      users: totalUsers,
      calls: {
        today: todayCalls,
        total: this.callLogs.length
      },
      uptime: process.uptime()
    };
  }

  // 调用日志
  logCall(user, method, status, authToken, error = null) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      user,
      method,
      status,
      authToken: authToken ? authToken.substring(0, 12) + '***' : null,
      error
    };
    
    this.callLogs.push(logEntry);
    
    // 广播日志更新
    this.broadcastSse('log', logEntry);
  }

  // 系统日志
  log(message) {
    const timestamp = new Date().toLocaleString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    
    // 广播系统日志
    this.broadcastSse('system-log', { timestamp, message });
  }

  // 启动完整服务
  async start() {
    this.log('🚀 企业MCP服务器启动中...');
    
    // 注册所有MCP服务
    this.registerService('universal', {
      name: '通用MCP服务',
      command: 'node',
      args: ['universal-mcp-server.js'],
      tools: [
        { name: 'read_file', description: '读取文件' },
        { name: 'write_file', description: '写入文件' },
        { name: 'http_request', description: 'HTTP请求' },
        { name: 'get_system_info', description: '获取系统信息' }
      ]
    });

    this.registerService('database', {
      name: '数据库服务',
      command: 'node',
      args: ['mcp-services-collection.js', 'database'],
      tools: [
        { name: 'create_table', description: '创建数据表' },
        { name: 'insert_data', description: '插入数据' },
        { name: 'query_data', description: '查询数据' }
      ]
    });

    this.registerService('email', {
      name: '邮件服务',
      command: 'node',
      args: ['mcp-services-collection.js', 'email'],
      tools: [
        { name: 'send_email', description: '发送邮件' },
        { name: 'check_inbox', description: '检查收件箱' }
      ]
    });

    this.registerService('password', {
      name: '密码管理',
      command: 'node',
      args: ['mcp-services-collection.js', 'password'],
      tools: [
        { name: 'generate_password', description: '生成密码' },
        { name: 'store_password', description: '存储密码' },
        { name: 'get_password', description: '获取密码' }
      ]
    });

    this.registerService('analytics', {
      name: '数据分析',
      command: 'node',
      args: ['mcp-services-collection.js', 'analytics'],
      tools: [
        { name: 'analyze_csv', description: '分析CSV数据' },
        { name: 'calculate_stats', description: '计算统计数据' }
      ]
    });

    this.registerService('weather', {
      name: '天气服务',
      command: 'node',
      args: ['mcp-services-collection.js', 'weather'],
      tools: [
        { name: 'get_weather', description: '获取天气信息' },
        { name: 'get_forecast', description: '获取天气预报' }
      ]
    });

    // 添加API转换器示例
    this.addApiConverter('weather', {
      description: '天气查询API转换',
      endpoint: 'https://api.openweathermap.org/data/2.5/weather',
      method: 'GET',
      headers: {},
      inputSchema: {
        type: 'object',
        properties: {
          city: { type: 'string', description: '城市名称' }
        },
        required: ['city']
      }
    });

    // 启动HTTP服务器
    await this.startHttpServer();
    
    this.log('✅ 企业MCP服务器启动完成');
    this.log('📋 管理界面: enterprise-mcp-platform.html');
    this.log('🌐 HTTP端点: http://localhost:9090/mcp');
    this.log('📡 SSE端点: http://localhost:9090/sse');
  }
}

// 启动服务器
if (import.meta.url === `file://${process.argv[1]}`) {
  const server = new EnterpriseMCPServer();
  
  server.start().catch(error => {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  });

  // 优雅关闭
  process.on('SIGINT', async () => {
    console.log('\n🛑 正在关闭服务器...');
    
    // 停止所有服务
    for (const serviceId of server.services.keys()) {
      await server.stopService(serviceId).catch(console.error);
    }
    
    // 关闭HTTP服务器
    if (server.httpServer) {
      server.httpServer.close();
    }
    
    console.log('👋 服务器已关闭');
    process.exit(0);
  });
}

export default EnterpriseMCPServer;
