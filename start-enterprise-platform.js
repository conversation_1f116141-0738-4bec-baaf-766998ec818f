#!/usr/bin/env node

/**
 * 🚀 企业MCP平台启动脚本
 * 
 * 一键启动完整的企业级MCP管理平台
 */

import EnterpriseMCPServer from './enterprise-mcp-server.js';
import MCPManagementAPI from './mcp-management-api.js';
import { spawn } from 'child_process';
import fs from 'fs/promises';

class EnterprisePlatformLauncher {
  constructor() {
    this.mcpServer = null;
    this.managementAPI = null;
    this.proxyProcess = null;
  }

  async start() {
    console.log('🚀 启动企业MCP平台...\n');

    try {
      // 1. 启动企业MCP服务器
      console.log('📡 启动企业MCP服务器...');
      this.mcpServer = new EnterpriseMCPServer();
      await this.mcpServer.start();
      console.log('✅ 企业MCP服务器启动成功\n');

      // 2. 启动管理API服务器
      console.log('📊 启动管理API服务器...');
      this.managementAPI = new MCPManagementAPI(this.mcpServer);
      await this.managementAPI.startServer(8080);
      console.log('✅ 管理API服务器启动成功\n');

      // 3. 生成配置文件
      console.log('📋 生成配置文件...');
      await this.generateConfigs();
      console.log('✅ 配置文件生成完成\n');

      // 4. 启动mcp-proxy (如果存在)
      if (await this.checkProxyExists()) {
        console.log('🌐 启动MCP代理服务器...');
        await this.startProxy();
        console.log('✅ MCP代理服务器启动成功\n');
      }

      // 5. 显示启动信息
      this.showStartupInfo();

    } catch (error) {
      console.error('❌ 平台启动失败:', error.message);
      process.exit(1);
    }
  }

  // 检查mcp-proxy是否存在
  async checkProxyExists() {
    try {
      await fs.access('./mcp-proxy/main.go');
      return true;
    } catch {
      return false;
    }
  }

  // 启动mcp-proxy
  async startProxy() {
    const proxyConfig = {
      mcpProxy: {
        baseURL: "http://localhost:9090",
        addr: ":9090",
        name: "Enterprise MCP Proxy",
        version: "1.0.0",
        type: "streamable-http",
        options: {
          logEnabled: true,
          authTokens: Array.from(this.mcpServer.authTokens.keys())
        }
      },
      mcpServers: {
        "enterprise": {
          url: "http://localhost:9090/mcp",
          transportType: "streamable-http",
          headers: {
            "Content-Type": "application/json"
          }
        }
      }
    };

    // 写入代理配置
    await fs.writeFile('./mcp-proxy/config.json', JSON.stringify(proxyConfig, null, 2));

    // 启动代理
    this.proxyProcess = spawn('go', ['run', '.', '--config', 'config.json'], {
      cwd: './mcp-proxy',
      stdio: ['pipe', 'pipe', 'pipe']
    });

    this.proxyProcess.stdout.on('data', (data) => {
      console.log(`[PROXY] ${data.toString().trim()}`);
    });

    this.proxyProcess.stderr.on('data', (data) => {
      console.error(`[PROXY] ${data.toString().trim()}`);
    });

    // 等待代理启动
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  // 生成配置文件
  async generateConfigs() {
    // Claude Desktop配置
    const claudeConfig = {
      mcpServers: {
        "enterprise-mcp": {
          command: "curl",
          args: [
            "-X", "POST",
            "-H", "Content-Type: application/json",
            "-H", "Authorization: Bearer YOUR_AUTH_TOKEN",
            "http://localhost:9090/mcp"
          ]
        }
      }
    };

    await fs.writeFile('claude-desktop-config.json', JSON.stringify(claudeConfig, null, 2));

    // 用户认证令牌文件
    const authTokens = Array.from(this.mcpServer.users.values()).map(user => ({
      email: user.email,
      role: user.role,
      token: user.authToken,
      permissions: user.permissions
    }));

    await fs.writeFile('auth-tokens.json', JSON.stringify(authTokens, null, 2));

    // 服务配置文件
    const servicesConfig = Array.from(this.mcpServer.services.values()).map(service => ({
      id: service.id,
      name: service.name,
      command: service.command,
      args: service.args,
      tools: service.tools
    }));

    await fs.writeFile('services-config.json', JSON.stringify(servicesConfig, null, 2));

    // API转换器配置
    const convertersConfig = Array.from(this.mcpServer.apiConverters.values());
    await fs.writeFile('api-converters-config.json', JSON.stringify(convertersConfig, null, 2));
  }

  // 显示启动信息
  showStartupInfo() {
    console.log('🎉 企业MCP平台启动完成！\n');
    
    console.log('📋 服务端点:');
    console.log('  🌐 MCP HTTP端点:     http://localhost:9090/mcp');
    console.log('  📡 MCP SSE端点:      http://localhost:9090/sse');
    console.log('  📊 管理API:          http://localhost:8080/api');
    console.log('  🎛️ 管理界面:         enterprise-mcp-platform.html');
    if (this.proxyProcess) {
      console.log('  🔄 代理服务:         http://localhost:9090');
    }
    console.log('');

    console.log('🔐 认证信息:');
    const users = Array.from(this.mcpServer.users.values());
    users.forEach(user => {
      console.log(`  👤 ${user.email} (${user.role})`);
      console.log(`     令牌: ${user.authToken}`);
      console.log(`     权限: ${user.permissions.join(', ')}`);
      console.log('');
    });

    console.log('📁 配置文件:');
    console.log('  📋 Claude Desktop:   claude-desktop-config.json');
    console.log('  🔑 认证令牌:         auth-tokens.json');
    console.log('  🛠️ 服务配置:         services-config.json');
    console.log('  🔄 API转换器:        api-converters-config.json');
    console.log('');

    console.log('🚀 快速开始:');
    console.log('  1. 打开 enterprise-mcp-platform.html 进行可视化管理');
    console.log('  2. 使用认证令牌调用MCP服务');
    console.log('  3. 通过管理API添加新服务和用户');
    console.log('');

    console.log('📖 API示例:');
    console.log('  # 调用MCP工具');
    console.log('  curl -X POST http://localhost:9090/mcp \\');
    console.log('    -H "Authorization: Bearer YOUR_TOKEN" \\');
    console.log('    -H "Content-Type: application/json" \\');
    console.log('    -d \'{"jsonrpc":"2.0","id":1,"method":"tools/list"}\'');
    console.log('');
    console.log('  # 获取系统状态');
    console.log('  curl http://localhost:8080/api/stats \\');
    console.log('    -H "Authorization: Bearer ADMIN_TOKEN"');
    console.log('');

    console.log('⚠️  重要提示:');
    console.log('  - 请妥善保管认证令牌');
    console.log('  - 定期检查用户权限设置');
    console.log('  - 监控系统调用日志');
    console.log('  - 按需调整服务配置');
    console.log('');

    console.log('按 Ctrl+C 停止平台');
  }

  // 优雅关闭
  async shutdown() {
    console.log('\n🛑 正在关闭企业MCP平台...');

    // 停止所有MCP服务
    if (this.mcpServer) {
      for (const serviceId of this.mcpServer.services.keys()) {
        await this.mcpServer.stopService(serviceId).catch(console.error);
      }
    }

    // 停止代理服务
    if (this.proxyProcess) {
      this.proxyProcess.kill();
      console.log('⏹️ MCP代理服务已停止');
    }

    // 关闭HTTP服务器
    if (this.mcpServer && this.mcpServer.httpServer) {
      this.mcpServer.httpServer.close();
      console.log('⏹️ 企业MCP服务器已停止');
    }

    console.log('👋 企业MCP平台已关闭');
    process.exit(0);
  }
}

// 启动平台
const launcher = new EnterprisePlatformLauncher();

launcher.start().catch(error => {
  console.error('❌ 平台启动失败:', error);
  process.exit(1);
});

// 优雅关闭处理
process.on('SIGINT', () => launcher.shutdown());
process.on('SIGTERM', () => launcher.shutdown());

export default EnterprisePlatformLauncher;
