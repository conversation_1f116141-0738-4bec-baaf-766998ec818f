# MCP Framework

> 🚀 A powerful framework for building MCP (Model Context Protocol) services with ease

[![npm version](https://badge.fury.io/js/@mcp-framework%2Fcore.svg)](https://badge.fury.io/js/@mcp-framework%2Fcore)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![TypeScript](https://img.shields.io/badge/TypeScript-Ready-blue.svg)](https://www.typescriptlang.org/)

## ✨ Features

- **🎯 极简开发体验** - 配置优于编码，5分钟上手
- **🔧 多传输支持** - stdio、SSE、HTTP 统一接口
- **📝 声明式配置** - YAML配置文件，自动代码生成
- **🛠️ 强大CLI工具** - 项目脚手架、热重载、一键部署
- **🔍 可视化调试** - 实时消息监控和调试面板
- **📚 丰富生态** - 模板市场、插件系统

## 🚀 快速开始

### 安装

```bash
npm install -g @mcp-framework/cli
```

### 创建项目

```bash
mcp init my-service --template=basic
cd my-service
```

### 配置服务

编辑 `mcp.config.yaml`:

```yaml
name: "my-service"
version: "1.0.0"
transport: ["stdio", "http"]

tools:
  - name: "hello"
    description: "Say hello"
    input:
      type: object
      properties:
        name: { type: string }
    handler: "./src/handlers/hello.js"
```

### 开发和测试

```bash
# 开发模式（热重载）
mcp dev

# 构建生产版本
mcp build

# 运行测试
mcp test
```

## 📦 包结构

- **[@mcp-framework/core](./packages/core)** - 核心框架
- **[@mcp-framework/cli](./packages/cli)** - 命令行工具
- **[@mcp-framework/sdk-typescript](./packages/sdk-typescript)** - TypeScript SDK

## 🏗️ 项目结构

```
mcp-framework/
├── packages/           # 核心包
│   ├── core/          # 框架核心
│   ├── cli/           # CLI工具
│   └── sdk-*/         # 多语言SDK
├── examples/          # 示例项目
├── templates/         # 项目模板
├── docs/             # 文档
└── tools/            # 开发工具
```

## 🤝 贡献

欢迎贡献代码！请查看 [贡献指南](./CONTRIBUTING.md)。

## 📄 许可证

MIT License - 查看 [LICENSE](./LICENSE) 文件了解详情。
