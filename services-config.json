[{"id": "universal", "name": "通用MCP服务", "command": "node", "args": ["universal-mcp-server.js"], "tools": [{"name": "read_file", "description": "读取文件"}, {"name": "write_file", "description": "写入文件"}, {"name": "http_request", "description": "HTTP请求"}, {"name": "get_system_info", "description": "获取系统信息"}]}, {"id": "database", "name": "数据库服务", "command": "node", "args": ["mcp-services-collection.js", "database"], "tools": [{"name": "create_table", "description": "创建数据表"}, {"name": "insert_data", "description": "插入数据"}, {"name": "query_data", "description": "查询数据"}]}, {"id": "email", "name": "邮件服务", "command": "node", "args": ["mcp-services-collection.js", "email"], "tools": [{"name": "send_email", "description": "发送邮件"}, {"name": "check_inbox", "description": "检查收件箱"}]}, {"id": "password", "name": "密码管理", "command": "node", "args": ["mcp-services-collection.js", "password"], "tools": [{"name": "generate_password", "description": "生成密码"}, {"name": "store_password", "description": "存储密码"}, {"name": "get_password", "description": "获取密码"}]}, {"id": "analytics", "name": "数据分析", "command": "node", "args": ["mcp-services-collection.js", "analytics"], "tools": [{"name": "analyze_csv", "description": "分析CSV数据"}, {"name": "calculate_stats", "description": "计算统计数据"}]}, {"id": "weather", "name": "天气服务", "command": "node", "args": ["mcp-services-collection.js", "weather"], "tools": [{"name": "get_weather", "description": "获取天气信息"}, {"name": "get_forecast", "description": "获取天气预报"}]}]